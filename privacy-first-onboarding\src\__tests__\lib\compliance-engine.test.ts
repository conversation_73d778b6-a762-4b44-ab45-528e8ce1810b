import { complianceEngine, COMPLIANCE_RULES } from '@/lib/compliance-engine';
import { prisma } from '@/lib/database';

// Mock Prisma
jest.mock('@/lib/database');
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('Compliance Engine', () => {
  const mockTenantId = 'test-tenant-id';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockPrisma.tenant.findUnique.mockResolvedValue({
      id: mockTenantId,
      name: 'Test Tenant',
      slug: 'test',
      regions: ['US', 'EU', 'UK'],
      defaultRegion: 'US',
      createdAt: new Date(),
      updatedAt: new Date(),
      privacyConfig: {
        id: 'config-id',
        tenantId: mockTenantId,
        requireExplicitConsent: true,
        enableRightToBeForgotten: true,
        enableDataPortability: true,
        enableOptOut: true,
        cookieConsentRequired: true,
        minorProtection: true,
        personalIdentifiersRetention: 2555,
        contactInfoRetention: 1095,
        financialInfoRetention: 2555,
        biometricDataRetention: 365,
        locationDataRetention: 365,
        behavioralDataRetention: 730,
        sensitivePersonalRetention: 365,
        healthDataRetention: 2555,
        employmentDataRetention: 2555,
        educationDataRetention: 2555,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  });

  describe('Compliance Rules', () => {
    it('should have GDPR rules defined', () => {
      const gdprRules = COMPLIANCE_RULES.filter(rule => rule.law === 'GDPR');
      
      expect(gdprRules.length).toBeGreaterThan(0);
      expect(gdprRules.some(rule => rule.requirement.includes('consent'))).toBe(true);
      expect(gdprRules.some(rule => rule.requirement.includes('access'))).toBe(true);
      expect(gdprRules.some(rule => rule.requirement.includes('erasure'))).toBe(true);
    });

    it('should have CCPA rules defined', () => {
      const ccpaRules = COMPLIANCE_RULES.filter(rule => rule.law === 'CCPA');
      
      expect(ccpaRules.length).toBeGreaterThan(0);
      expect(ccpaRules.some(rule => rule.requirement.includes('know'))).toBe(true);
      expect(ccpaRules.some(rule => rule.requirement.includes('delete'))).toBe(true);
      expect(ccpaRules.some(rule => rule.requirement.includes('opt-out'))).toBe(true);
    });

    it('should have DPDP rules defined', () => {
      const dpdpRules = COMPLIANCE_RULES.filter(rule => rule.law === 'DPDP');
      
      expect(dpdpRules.length).toBeGreaterThan(0);
      expect(dpdpRules.some(rule => rule.requirement.includes('consent'))).toBe(true);
      expect(dpdpRules.some(rule => rule.requirement.includes('localization'))).toBe(true);
    });
  });

  describe('Compliance Assessment', () => {
    beforeEach(() => {
      // Mock additional Prisma calls for assessment
      mockPrisma.consent.count.mockResolvedValue(10);
      mockPrisma.privacyRequest.count.mockResolvedValue(0);
      mockPrisma.user.count.mockResolvedValue(0);
      mockPrisma.dataBreach.findMany.mockResolvedValue([]);
    });

    it('should assess GDPR compliance', async () => {
      const assessment = await complianceEngine.assessCompliance(mockTenantId, 'EU', 'GDPR');
      
      expect(assessment).toBeDefined();
      expect(assessment.score).toBeGreaterThanOrEqual(0);
      expect(assessment.score).toBeLessThanOrEqual(100);
      expect(typeof assessment.compliant).toBe('boolean');
      expect(Array.isArray(assessment.findings)).toBe(true);
      expect(Array.isArray(assessment.recommendations)).toBe(true);
    });

    it('should assess CCPA compliance', async () => {
      const assessment = await complianceEngine.assessCompliance(mockTenantId, 'US', 'CCPA');
      
      expect(assessment).toBeDefined();
      expect(assessment.score).toBeGreaterThanOrEqual(0);
      expect(assessment.score).toBeLessThanOrEqual(100);
      expect(typeof assessment.compliant).toBe('boolean');
    });

    it('should assess DPDP compliance', async () => {
      const assessment = await complianceEngine.assessCompliance(mockTenantId, 'IN', 'DPDP');
      
      expect(assessment).toBeDefined();
      expect(assessment.score).toBeGreaterThanOrEqual(0);
      expect(assessment.score).toBeLessThanOrEqual(100);
      expect(typeof assessment.compliant).toBe('boolean');
    });

    it('should return high score for compliant configuration', async () => {
      // Mock a fully compliant setup
      mockPrisma.consent.count.mockResolvedValue(100);
      mockPrisma.privacyRequest.count.mockResolvedValue(0); // No overdue requests
      mockPrisma.user.count.mockResolvedValue(0); // No expired data
      
      const assessment = await complianceEngine.assessCompliance(mockTenantId, 'EU', 'GDPR');
      
      expect(assessment.score).toBeGreaterThanOrEqual(80);
      expect(assessment.compliant).toBe(true);
    });

    it('should return lower score for non-compliant configuration', async () => {
      // Mock a non-compliant setup
      mockPrisma.tenant.findUnique.mockResolvedValue({
        id: mockTenantId,
        name: 'Test Tenant',
        slug: 'test',
        regions: ['US'],
        defaultRegion: 'US',
        createdAt: new Date(),
        updatedAt: new Date(),
        privacyConfig: {
          id: 'config-id',
          tenantId: mockTenantId,
          requireExplicitConsent: false, // Non-compliant
          enableRightToBeForgotten: false, // Non-compliant
          enableDataPortability: false, // Non-compliant
          enableOptOut: false,
          cookieConsentRequired: false,
          minorProtection: false,
          personalIdentifiersRetention: 2555,
          contactInfoRetention: 1095,
          financialInfoRetention: 2555,
          biometricDataRetention: 365,
          locationDataRetention: 365,
          behavioralDataRetention: 730,
          sensitivePersonalRetention: 365,
          healthDataRetention: 2555,
          employmentDataRetention: 2555,
          educationDataRetention: 2555,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      // Mock overdue privacy requests
      mockPrisma.privacyRequest.count.mockResolvedValue(5);
      
      const assessment = await complianceEngine.assessCompliance(mockTenantId, 'EU', 'GDPR');
      
      expect(assessment.score).toBeLessThan(80);
      expect(assessment.compliant).toBe(false);
      expect(assessment.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Compliance Report Generation', () => {
    beforeEach(() => {
      // Mock all necessary Prisma calls for report generation
      mockPrisma.consent.count.mockResolvedValue(10);
      mockPrisma.privacyRequest.count.mockResolvedValue(0);
      mockPrisma.user.count.mockResolvedValue(0);
      mockPrisma.dataBreach.findMany.mockResolvedValue([]);
    });

    it('should generate comprehensive compliance report', async () => {
      const report = await complianceEngine.generateComplianceReport(mockTenantId);
      
      expect(report).toBeDefined();
      expect(report.tenantId).toBe(mockTenantId);
      expect(report.tenantName).toBe('Test Tenant');
      expect(report.generatedAt).toBeInstanceOf(Date);
      expect(Array.isArray(report.regions)).toBe(true);
      expect(Array.isArray(report.assessments)).toBe(true);
      expect(typeof report.overallScore).toBe('number');
      expect(typeof report.overallCompliant).toBe('boolean');
      expect(report.summary).toBeDefined();
    });

    it('should include assessments for all tenant regions', async () => {
      const report = await complianceEngine.generateComplianceReport(mockTenantId);
      
      // Should have assessments for US, EU, UK regions
      const regions = [...new Set(report.assessments.map((a: any) => a.region))];
      expect(regions).toContain('US');
      expect(regions).toContain('EU');
      expect(regions).toContain('UK');
    });

    it('should calculate overall compliance score', async () => {
      const report = await complianceEngine.generateComplianceReport(mockTenantId);
      
      expect(report.overallScore).toBeGreaterThanOrEqual(0);
      expect(report.overallScore).toBeLessThanOrEqual(100);
      
      // Overall compliance should be true if score >= 80
      if (report.overallScore >= 80) {
        expect(report.overallCompliant).toBe(true);
      } else {
        expect(report.overallCompliant).toBe(false);
      }
    });

    it('should include summary statistics', async () => {
      const report = await complianceEngine.generateComplianceReport(mockTenantId);
      
      expect(report.summary.totalRules).toBeGreaterThanOrEqual(0);
      expect(report.summary.compliantRules).toBeGreaterThanOrEqual(0);
      expect(report.summary.criticalIssues).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(report.summary.recommendations)).toBe(true);
      
      // Compliant rules should not exceed total rules
      expect(report.summary.compliantRules).toBeLessThanOrEqual(report.summary.totalRules);
    });

    it('should handle tenant not found error', async () => {
      mockPrisma.tenant.findUnique.mockResolvedValue(null);
      
      await expect(complianceEngine.generateComplianceReport('non-existent'))
        .rejects.toThrow('Tenant not found');
    });
  });

  describe('Data Breach Assessment', () => {
    it('should assess breach notification compliance', async () => {
      // Mock a data breach that requires notification
      const mockBreach = {
        id: 'breach-1',
        title: 'Test Breach',
        description: 'Test breach description',
        severity: 'HIGH',
        status: 'DETECTED',
        affectedDataTypes: ['PERSONAL_IDENTIFIERS'],
        affectedUserCount: 100,
        affectedRegions: ['EU'],
        detectedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
        containedAt: null,
        resolvedAt: null,
        requiresNotification: true,
        notificationSent: false,
        notificationSentAt: null,
        reportedToAuthorities: false,
        reportedAt: null,
        authorityReference: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.dataBreach.findMany.mockResolvedValue([mockBreach]);
      
      const assessment = await complianceEngine.assessCompliance(mockTenantId, 'EU', 'GDPR');
      
      // Should detect the overdue breach notification
      expect(assessment.score).toBeLessThan(100);
      expect(assessment.findings.some(f => 
        f.requirement.includes('breach') && !f.compliant
      )).toBe(true);
    });
  });

  describe('Data Retention Assessment', () => {
    it('should assess data retention compliance', async () => {
      // Mock expired inactive users
      mockPrisma.user.count.mockResolvedValue(5);
      
      const assessment = await complianceEngine.assessCompliance(mockTenantId, 'EU', 'GDPR');
      
      // Should include retention assessment in findings
      expect(assessment.findings.some(f => 
        f.requirement.includes('retention') || f.issues.some((issue: string) => issue.includes('retention'))
      )).toBeTruthy();
    });
  });
});

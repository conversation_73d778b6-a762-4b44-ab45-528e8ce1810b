// Complete Flow Test for Document Generator
const { DocumentGeneratorService } = require('./src/lib/document-generator-service.ts');

async function testCompleteFlow() {
  console.log('🚀 Testing Complete Document Generator Flow...\n');

  const tenantId = '550e8400-e29b-41d4-a716-446655440000';
  const userId = '550e8400-e29b-41d4-a716-446655440001';
  const service = new DocumentGeneratorService(tenantId);

  try {
    // Step 1: Create Session
    console.log('📄 Step 1: Creating session...');
    const sessionResponse = await service.createSession({
      document_type: 'privacy_policy',
      form_data: {
        company_name: 'Test Company',
        website_url: 'https://testcompany.com',
        contact_email: '<EMAIL>'
      }
    }, userId);

    if (!sessionResponse.success) {
      throw new Error('Failed to create session: ' + sessionResponse.error);
    }

    console.log('✅ Session created:', sessionResponse.data.id);
    const sessionId = sessionResponse.data.id;

    // Step 2: Update Session with Form Data
    console.log('\n📝 Step 2: Updating session with form data...');
    const updateResponse = await service.updateSession(sessionId, {
      form_data: {
        company_name: 'Test Company Inc.',
        website_url: 'https://testcompany.com',
        contact_email: '<EMAIL>',
        contact_address: '123 Test Street, Test City, TC 12345',
        data_types: ['personal_info', 'usage_data', 'cookies'],
        data_purpose: 'To provide our services and improve user experience',
        data_sharing: 'service_providers',
        retention_period: '2_years',
        user_rights: ['access_right', 'rectification_right', 'erasure_right']
      },
      completion_status: 'completed'
    });

    if (!updateResponse.success) {
      throw new Error('Failed to update session: ' + updateResponse.error);
    }

    console.log('✅ Session updated with form data');

    // Step 3: Generate Document
    console.log('\n🔧 Step 3: Generating document...');
    const generateResponse = await service.generateDocument({
      session_id: sessionId,
      title: 'Privacy Policy - Test Company Inc.'
    });

    if (!generateResponse.success) {
      throw new Error('Failed to generate document: ' + generateResponse.error);
    }

    console.log('✅ Document generated:', generateResponse.data.id);
    console.log('📄 Document title:', generateResponse.data.title);
    console.log('📊 Document type:', generateResponse.data.document_type);
    console.log('📅 Created at:', generateResponse.data.created_at);

    // Step 4: Verify Document Content
    console.log('\n🔍 Step 4: Verifying document content...');
    const document = generateResponse.data;
    
    if (document.generated_content.includes('Test Company Inc.')) {
      console.log('✅ Company name correctly inserted in HTML content');
    } else {
      console.log('❌ Company name not found in HTML content');
    }

    if (document.markdown_content && document.markdown_content.includes('Test Company Inc.')) {
      console.log('✅ Company name correctly inserted in Markdown content');
    } else {
      console.log('❌ Company name not found in Markdown content');
    }

    if (document.generated_content.includes('<EMAIL>')) {
      console.log('✅ Contact email correctly inserted');
    } else {
      console.log('❌ Contact email not found');
    }

    // Step 5: Test Session Retrieval
    console.log('\n📋 Step 5: Testing session retrieval...');
    const sessionsResponse = await service.getSessions(userId);
    
    if (sessionsResponse.success) {
      console.log('✅ Sessions retrieved successfully');
      console.log('📊 Number of sessions:', sessionsResponse.data.length);
    } else {
      console.log('❌ Failed to retrieve sessions:', sessionsResponse.error);
    }

    console.log('\n🎉 Complete Flow Test PASSED!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Session Creation: Working');
    console.log('   ✅ Session Updates: Working');
    console.log('   ✅ Document Generation: Working');
    console.log('   ✅ Content Templating: Working');
    console.log('   ✅ Session Retrieval: Working');
    console.log('\n🚀 The document generator is ready for production use!');

    return {
      success: true,
      sessionId: sessionId,
      documentId: document.id,
      document: document
    };

  } catch (error) {
    console.error('\n❌ Complete Flow Test FAILED:', error.message);
    console.log('\n🔧 This indicates an issue that needs to be fixed.');
    return {
      success: false,
      error: error.message
    };
  }
}

// Run the test
if (require.main === module) {
  testCompleteFlow()
    .then(result => {
      if (result.success) {
        console.log('\n✅ All tests passed! The application is ready.');
        process.exit(0);
      } else {
        console.log('\n❌ Tests failed. Please check the errors above.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testCompleteFlow };

-- Document Management System Schema Enhancement
-- Extends existing multi-tenant PostgreSQL architecture with document management capabilities

-- Document templates and generated policies
CREATE TABLE document_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    template_type TEXT NOT NULL CHECK (template_type IN ('privacy_policy', 'terms_of_service', 'cookie_policy', 'data_processing_agreement')),
    name TEXT NOT NULL,
    description TEXT,
    template_content JSONB NOT NULL, -- Structured template data
    markdown_template TEXT NOT NULL, -- Base markdown template
    compliance_frameworks TEXT[] NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(tenant_id, name, template_type)
);

-- Document versions for change tracking
CREATE TABLE document_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    template_id UUID NOT NULL REFERENCES document_templates(id) ON DELETE CASCADE,
    version_number TEXT NOT NULL, -- e.g., "1.0.0", "1.1.0"
    version_type TEXT NOT NULL CHECK (version_type IN ('major', 'minor', 'patch')),
    markdown_content TEXT NOT NULL,
    generated_data JSONB NOT NULL, -- Data used to generate this version
    pdf_url TEXT, -- S3/storage URL for PDF version
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    changelog TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(tenant_id, template_id, version_number)
);

-- End user consent tracking
CREATE TABLE end_user_consents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    document_version_id UUID NOT NULL REFERENCES document_versions(id) ON DELETE CASCADE,
    end_user_identifier TEXT NOT NULL, -- email, user_id, etc.
    end_user_email TEXT,
    consent_type TEXT NOT NULL CHECK (consent_type IN ('privacy_policy', 'terms_of_service', 'cookie_policy')),
    consent_given BOOLEAN NOT NULL,
    consent_timestamp TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ip_address INET,
    user_agent TEXT,
    consent_method TEXT NOT NULL CHECK (consent_method IN ('website_signup', 'email_confirmation', 'api_integration', 'embedded_widget')),
    notification_sent BOOLEAN DEFAULT false,
    notification_sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Notification preferences and configuration
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('email', 'sms', 'webhook')),
    is_enabled BOOLEAN DEFAULT true,
    configuration JSONB NOT NULL, -- Email templates, SMS settings, webhook URLs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(tenant_id, notification_type)
);

-- Notification history and delivery tracking
CREATE TABLE notification_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    end_user_identifier TEXT NOT NULL,
    notification_type TEXT NOT NULL,
    document_version_id UUID REFERENCES document_versions(id),
    status TEXT NOT NULL CHECK (status IN ('sent', 'delivered', 'failed', 'pending')),
    notification_data JSONB, -- Email content, SMS text, etc.
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0
);

-- Notification queue for async processing
CREATE TABLE notification_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    document_version_id UUID NOT NULL REFERENCES document_versions(id) ON DELETE CASCADE,
    end_user_email TEXT NOT NULL,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('version_update', 'consent_confirmation', 'policy_change')),
    priority INTEGER DEFAULT 5, -- 1 = highest, 10 = lowest
    scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT now(),
    processed_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Document audit log for compliance tracking
CREATE TABLE document_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id UUID NOT NULL,
    user_id UUID REFERENCES users(id),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX idx_document_templates_tenant_type ON document_templates(tenant_id, template_type);
CREATE INDEX idx_document_versions_template_status ON document_versions(template_id, status);
CREATE INDEX idx_document_versions_tenant_published ON document_versions(tenant_id, published_at) WHERE status = 'published';
CREATE INDEX idx_end_user_consents_tenant_email ON end_user_consents(tenant_id, end_user_email);
CREATE INDEX idx_end_user_consents_document_consent ON end_user_consents(document_version_id, consent_given);
CREATE INDEX idx_notification_queue_scheduled ON notification_queue(scheduled_for, status) WHERE status = 'pending';
CREATE INDEX idx_notification_history_tenant_user ON notification_history(tenant_id, end_user_identifier);
CREATE INDEX idx_document_audit_log_tenant_timestamp ON document_audit_log(tenant_id, timestamp);

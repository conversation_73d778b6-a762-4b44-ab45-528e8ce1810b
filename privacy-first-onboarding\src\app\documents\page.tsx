'use client';

import React, { useState, useEffect } from 'react';
import { DocumentManagement } from '@/components/documents/document-management';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FileText, AlertCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function DocumentsPage() {
  const [tenantId, setTenantId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    // Get tenant ID from URL params or default tenant
    const urlParams = new URLSearchParams(window.location.search);
    const paramTenantId = urlParams.get('tenantId');
    
    if (paramTenantId) {
      setTenantId(paramTenantId);
    } else {
      // Use default tenant ID for demo
      setTenantId('550e8400-e29b-41d4-a716-446655440000');
    }
    
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!tenantId || tenantId.trim() === '') {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-6 w-6" />
              Document Management
            </CardTitle>
            <CardDescription>
              Tenant ID is required to access document management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                Please provide a tenant ID to access the document management system.
              </p>
              <div className="flex gap-2">
                <Link href="/dashboard">
                  <Button variant="outline">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
                <Button onClick={() => setTenantId('550e8400-e29b-41d4-a716-446655440000')}>
                  Use Demo Tenant
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <DocumentManagement tenantId={tenantId} />;
}

-- Document Generator System Migration
-- Creates tables for document generator sessions and generated documents

-- Document Generator Sessions Table
CREATE TABLE IF NOT EXISTS document_generator_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  user_id UUID,
  document_type TEXT NOT NULL CHECK (document_type IN (
    'privacy_policy',
    'terms_conditions', 
    'cookie_policy',
    'eula',
    'acceptable_use',
    'return_policy',
    'disclaimer',
    'shipping_policy'
  )),
  session_name TEXT NOT NULL,
  form_data JSONB NOT NULL DEFAULT '{}',
  completion_status TEXT NOT NULL DEFAULT 'draft' CHECK (completion_status IN ('draft', 'completed', 'published')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Indexes
  CONSTRAINT fk_document_sessions_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- Generated Documents Table
CREATE TABLE IF NOT EXISTS generated_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID NOT NULL REFERENCES document_generator_sessions(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  document_type TEXT NOT NULL CHECK (document_type IN (
    'privacy_policy',
    'terms_conditions',
    'cookie_policy', 
    'eula',
    'acceptable_use',
    'return_policy',
    'disclaimer',
    'shipping_policy'
  )),
  title TEXT NOT NULL,
  generated_content TEXT NOT NULL,
  markdown_content TEXT NOT NULL,
  pdf_url TEXT,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Indexes
  CONSTRAINT fk_generated_documents_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- Document Versions Table (for tracking changes)
CREATE TABLE IF NOT EXISTS document_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES generated_documents(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  content TEXT NOT NULL,
  markdown_content TEXT NOT NULL,
  changes_summary TEXT,
  created_by UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Ensure unique version numbers per document
  UNIQUE(document_id, version_number)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_document_sessions_tenant_id ON document_generator_sessions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_document_sessions_user_id ON document_generator_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_document_sessions_type ON document_generator_sessions(document_type);
CREATE INDEX IF NOT EXISTS idx_document_sessions_status ON document_generator_sessions(completion_status);
CREATE INDEX IF NOT EXISTS idx_document_sessions_updated ON document_generator_sessions(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_generated_documents_tenant_id ON generated_documents(tenant_id);
CREATE INDEX IF NOT EXISTS idx_generated_documents_session_id ON generated_documents(session_id);
CREATE INDEX IF NOT EXISTS idx_generated_documents_type ON generated_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_generated_documents_status ON generated_documents(status);
CREATE INDEX IF NOT EXISTS idx_generated_documents_created ON generated_documents(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX IF NOT EXISTS idx_document_versions_created ON document_versions(created_at DESC);

-- Row Level Security (RLS) Policies
ALTER TABLE document_generator_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE generated_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for document_generator_sessions
CREATE POLICY "Users can view their tenant's document sessions" ON document_generator_sessions
  FOR SELECT USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create document sessions for their tenant" ON document_generator_sessions
  FOR INSERT WITH CHECK (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their tenant's document sessions" ON document_generator_sessions
  FOR UPDATE USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete their tenant's document sessions" ON document_generator_sessions
  FOR DELETE USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

-- RLS Policies for generated_documents
CREATE POLICY "Users can view their tenant's generated documents" ON generated_documents
  FOR SELECT USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create generated documents for their tenant" ON generated_documents
  FOR INSERT WITH CHECK (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their tenant's generated documents" ON generated_documents
  FOR UPDATE USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete their tenant's generated documents" ON generated_documents
  FOR DELETE USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenant_roles 
      WHERE user_id = auth.uid()
    )
  );

-- RLS Policies for document_versions
CREATE POLICY "Users can view document versions for their tenant's documents" ON document_versions
  FOR SELECT USING (
    document_id IN (
      SELECT id FROM generated_documents 
      WHERE tenant_id IN (
        SELECT tenant_id FROM user_tenant_roles 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can create document versions for their tenant's documents" ON document_versions
  FOR INSERT WITH CHECK (
    document_id IN (
      SELECT id FROM generated_documents 
      WHERE tenant_id IN (
        SELECT tenant_id FROM user_tenant_roles 
        WHERE user_id = auth.uid()
      )
    )
  );

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_document_session_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_generated_document_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for automatic timestamp updates
CREATE TRIGGER trigger_update_document_session_updated_at
  BEFORE UPDATE ON document_generator_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_document_session_updated_at();

CREATE TRIGGER trigger_update_generated_document_updated_at
  BEFORE UPDATE ON generated_documents
  FOR EACH ROW
  EXECUTE FUNCTION update_generated_document_updated_at();

-- Function to create document version when document is updated
CREATE OR REPLACE FUNCTION create_document_version()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create version if content actually changed
  IF OLD.generated_content IS DISTINCT FROM NEW.generated_content THEN
    INSERT INTO document_versions (
      document_id,
      version_number,
      content,
      markdown_content,
      changes_summary,
      created_by
    ) VALUES (
      NEW.id,
      COALESCE((
        SELECT MAX(version_number) + 1 
        FROM document_versions 
        WHERE document_id = NEW.id
      ), 1),
      NEW.generated_content,
      NEW.markdown_content,
      'Document updated',
      auth.uid()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically create document versions
CREATE TRIGGER trigger_create_document_version
  AFTER UPDATE ON generated_documents
  FOR EACH ROW
  EXECUTE FUNCTION create_document_version();

-- Grant necessary permissions
GRANT ALL ON document_generator_sessions TO authenticated;
GRANT ALL ON generated_documents TO authenticated;
GRANT ALL ON document_versions TO authenticated;

-- Comments for documentation
COMMENT ON TABLE document_generator_sessions IS 'Stores document generator form sessions with auto-save functionality';
COMMENT ON TABLE generated_documents IS 'Stores generated legal documents with versioning support';
COMMENT ON TABLE document_versions IS 'Tracks versions and changes to generated documents';

COMMENT ON COLUMN document_generator_sessions.form_data IS 'JSONB field storing all form input data for the document generator';
COMMENT ON COLUMN document_generator_sessions.completion_status IS 'Tracks whether the form is draft, completed, or published';
COMMENT ON COLUMN generated_documents.generated_content IS 'Final HTML content of the generated document';
COMMENT ON COLUMN generated_documents.markdown_content IS 'Markdown source of the generated document';
COMMENT ON COLUMN generated_documents.pdf_url IS 'Optional URL to generated PDF version of the document';

// User Consents API Route
// Server-side endpoint to safely fetch user consents

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

interface RouteParams {
  params: {
    userId: string;
  };
}

// GET /api/users/[userId]/consents - Get user consents safely on server-side
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    try {
      // First get the user to find their email and tenant
      const { data: userData, error: userError } = await supabaseAdmin
        .from('users')
        .select('id, email, tenant_id')
        .eq('id', userId)
        .single();

      if (userError || !userData) {
        console.log('User not found, returning empty consents');
        return NextResponse.json({
          success: true,
          data: []
        });
      }

      // Try to get consent data from document management tables if they exist
      let consents = [];
      try {
        const { data: consentData, error: consentError } = await supabaseAdmin
          .from('end_user_consents')
          .select(`
            *,
            document_version:document_versions(
              id,
              version_number,
              template:document_templates(name, template_type)
            )
          `)
          .eq('end_user_identifier', userData.email)
          .eq('tenant_id', userData.tenant_id)
          .order('consent_timestamp', { ascending: false });
        
        if (consentError) {
          console.log('Consent table not available:', consentError.message);
        } else if (consentData) {
          consents = consentData;
        }
      } catch (e) {
        console.log('Document management tables not available yet');
      }

      // Transform consents to match expected format
      const transformedConsents = consents.map((consent: any) => ({
        id: consent.id,
        userId: consent.end_user_identifier,
        dataCategory: consent.consent_type || 'personal_identifiers',
        purpose: 'service_provision',
        granted: consent.consent_given,
        timestamp: new Date(consent.consent_timestamp),
        region: 'US',
        version: consent.document_version?.version_number || '1.0.0',
        documentName: consent.document_version?.template?.name || 'Privacy Policy',
        documentType: consent.document_version?.template?.template_type || 'privacy_policy'
      }));

      return NextResponse.json({
        success: true,
        data: transformedConsents
      });

    } catch (error) {
      console.error('Error fetching consents:', error);
      
      // Return empty array as fallback
      return NextResponse.json({
        success: true,
        data: []
      });
    }
  } catch (error) {
    console.error('Error in consents API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testSupabaseConnection() {
  console.log('🧪 Testing Supabase Connection...\n');
  
  // Check environment variables
  console.log('📊 Environment Variables:');
  console.log('   - NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Missing');
  console.log('   - NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Missing');
  console.log('   - SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing');
  
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.log('\n❌ Supabase environment variables are missing!');
    console.log('📋 Please check your .env.local file and ensure it contains:');
    console.log('   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url');
    console.log('   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key');
    console.log('   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key');
    return;
  }
  
  // Test client connection
  console.log('\n🔗 Testing Client Connection...');
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );
  
  try {
    const { data, error } = await supabase.from('users').select('count').limit(1);
    
    if (error) {
      console.log('❌ Client connection failed:', error.message);
    } else {
      console.log('✅ Client connection successful');
    }
  } catch (error) {
    console.log('❌ Client connection error:', error.message);
  }
  
  // Test admin connection
  console.log('\n🔗 Testing Admin Connection...');
  const supabaseAdmin = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );
  
  try {
    const { data, error } = await supabaseAdmin.from('users').select('count').limit(1);
    
    if (error) {
      console.log('❌ Admin connection failed:', error.message);
    } else {
      console.log('✅ Admin connection successful');
    }
  } catch (error) {
    console.log('❌ Admin connection error:', error.message);
  }
  
  // Test specific user query
  console.log('\n👤 Testing User Query...');
  const testUserId = 'test-user-123';
  
  try {
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', testUserId)
      .single();
    
    if (userError) {
      if (userError.code === 'PGRST116') {
        console.log('⚠️ User not found (this is expected for test user)');
      } else {
        console.log('❌ User query failed:', userError.message);
      }
    } else {
      console.log('✅ User query successful:', userData ? 'User found' : 'No user');
    }
  } catch (error) {
    console.log('❌ User query error:', error.message);
  }
  
  // Test tenant query
  console.log('\n🏢 Testing Tenant Query...');
  const testTenantId = '550e8400-e29b-41d4-a716-446655440000';
  
  try {
    const { data: tenantData, error: tenantError } = await supabaseAdmin
      .from('tenants')
      .select('*')
      .eq('id', testTenantId)
      .single();
    
    if (tenantError) {
      console.log('❌ Tenant query failed:', tenantError.message);
    } else {
      console.log('✅ Tenant query successful:', tenantData ? tenantData.name : 'No tenant');
    }
  } catch (error) {
    console.log('❌ Tenant query error:', error.message);
  }
  
  // Test the privacy service configuration
  console.log('\n🔧 Testing Privacy Service Configuration...');
  
  try {
    // Import the supabase utils
    const { supabaseUtils } = require('./src/lib/supabase.ts');
    
    console.log('✅ Privacy service imports successful');
    console.log('   - Is configured:', supabaseUtils.isConfigured());
    
    if (supabaseUtils.isConfigured()) {
      try {
        const userData = await supabaseUtils.getUserPrivacyProfile(testUserId);
        console.log('✅ getUserPrivacyProfile works (user found)');
      } catch (profileError) {
        if (profileError.message.includes('not found')) {
          console.log('⚠️ getUserPrivacyProfile works (user not found, as expected)');
        } else {
          console.log('❌ getUserPrivacyProfile failed:', profileError.message);
        }
      }
    }
  } catch (importError) {
    console.log('⚠️ Privacy service test skipped (TypeScript compilation needed)');
    console.log('   Error:', importError.message);
  }
  
  console.log('\n🎉 Supabase Connection Test Complete!');
  console.log('\n📋 Summary:');
  console.log('   - If connections are successful, the database is working');
  console.log('   - If user/tenant queries fail, check if the tables exist');
  console.log('   - If privacy service fails, try running: npm run dev');
}

testSupabaseConnection().catch(console.error);

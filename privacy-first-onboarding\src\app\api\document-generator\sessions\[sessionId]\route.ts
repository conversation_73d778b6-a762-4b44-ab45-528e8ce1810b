// API Routes for Individual Document Generator Sessions
import { NextRequest, NextResponse } from 'next/server';
import { DocumentGeneratorService } from '@/lib/document-generator-service';
import { UpdateSessionRequest } from '@/lib/document-generator-types';

// GET /api/document-generator/sessions/[sessionId] - Get a specific session
export async function GET(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenant_id');

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentGeneratorService(tenantId);
    const response = await documentService.getSession(params.sessionId);

    return NextResponse.json(response, {
      status: response.success ? 200 : 404
    });
  } catch (error) {
    console.error('Error in GET /api/document-generator/sessions/[sessionId]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/document-generator/sessions/[sessionId] - Update a session
export async function PUT(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const body = await request.json();
    const { tenant_id, ...updateData } = body as UpdateSessionRequest & { tenant_id: string };

    if (!tenant_id) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    if (!updateData.form_data) {
      return NextResponse.json(
        { success: false, error: 'Form data is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentGeneratorService(tenant_id);
    const response = await documentService.updateSession(params.sessionId, updateData);

    return NextResponse.json(response, {
      status: response.success ? 200 : 400
    });
  } catch (error) {
    console.error('Error in PUT /api/document-generator/sessions/[sessionId]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/document-generator/sessions/[sessionId] - Delete a session
export async function DELETE(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenant_id');

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    // Note: We would need to add a deleteSession method to DocumentGeneratorService
    // For now, return a placeholder response
    return NextResponse.json(
      { success: false, error: 'Delete functionality not yet implemented' },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error in DELETE /api/document-generator/sessions/[sessionId]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

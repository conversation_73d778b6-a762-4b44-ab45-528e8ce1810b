import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Shield, Globe, Lock, Eye, Trash2, Download } from 'lucide-react';

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="p-4 bg-blue-100 rounded-full">
              <Shield className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
          <p className="text-xl text-gray-600 mb-4">
            Your privacy is our priority. This policy explains how we collect, use, and protect your data.
          </p>
          <div className="flex justify-center gap-2">
            <Badge variant="outline">GDPR Compliant</Badge>
            <Badge variant="outline">CCPA Compliant</Badge>
            <Badge variant="outline">DPDP Compliant</Badge>
          </div>
          <p className="text-sm text-gray-500 mt-4">Last updated: December 25, 2024</p>
        </div>

        <div className="space-y-8">
          {/* Data We Collect */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                What Data We Collect
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Personal Identifiers</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Name, email address, and account credentials for service provision.
                  </p>
                  <Badge variant="secondary">Required for Service</Badge>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Usage Analytics</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    How you interact with our service to improve functionality and user experience.
                  </p>
                  <Badge variant="outline">Optional - Consent Based</Badge>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Communication Preferences</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Your preferences for receiving marketing and service communications.
                  </p>
                  <Badge variant="outline">Optional - Consent Based</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* How We Use Data */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                How We Use Your Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-medium">Service Provision</h4>
                    <p className="text-sm text-gray-600">
                      To create and manage your account, provide customer support, and deliver our services.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-medium">Service Improvement</h4>
                    <p className="text-sm text-gray-600">
                      To analyze usage patterns, fix bugs, and enhance user experience (with your consent).
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-600 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-medium">Communication</h4>
                    <p className="text-sm text-gray-600">
                      To send service updates, security notifications, and marketing communications (with your consent).
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Storage & Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Data Storage & Security
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-semibold">Regional Data Storage</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Data stored in your chosen region</li>
                    <li>• Compliance with local privacy laws</li>
                    <li>• No unauthorized cross-border transfers</li>
                    <li>• You control your data location</li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-semibold">Security Measures</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• End-to-end encryption</li>
                    <li>• Regular security audits</li>
                    <li>• Access controls and monitoring</li>
                    <li>• Incident response procedures</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Your Rights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Your Privacy Rights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Eye className="h-5 w-5 text-blue-600" />
                  <div>
                    <h4 className="font-medium">Right to Access</h4>
                    <p className="text-sm text-gray-600">Request a copy of all your personal data</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Download className="h-5 w-5 text-green-600" />
                  <div>
                    <h4 className="font-medium">Right to Data Portability</h4>
                    <p className="text-sm text-gray-600">Export your data in a machine-readable format</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Trash2 className="h-5 w-5 text-red-600" />
                  <div>
                    <h4 className="font-medium">Right to Deletion</h4>
                    <p className="text-sm text-gray-600">Request deletion of your personal data</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Retention */}
          <Card>
            <CardHeader>
              <CardTitle>Data Retention</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                We retain your personal data only as long as necessary for the purposes outlined in this policy:
              </p>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 border rounded">
                  <span className="text-sm">Account Information</span>
                  <Badge variant="outline">Until account deletion</Badge>
                </div>
                <div className="flex justify-between items-center p-2 border rounded">
                  <span className="text-sm">Usage Analytics</span>
                  <Badge variant="outline">2 years maximum</Badge>
                </div>
                <div className="flex justify-between items-center p-2 border rounded">
                  <span className="text-sm">Marketing Data</span>
                  <Badge variant="outline">Until consent withdrawn</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Us</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                If you have questions about this privacy policy or want to exercise your rights:
              </p>
              <div className="space-y-2 text-sm">
                <p><strong>Data Protection Officer:</strong> <EMAIL></p>
                <p><strong>Privacy Team:</strong> +1 (555) 123-4567</p>
                <p><strong>Address:</strong> 123 Privacy Street, Data City, DC 12345</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="text-center mt-12 pt-8 border-t">
          <p className="text-sm text-gray-500">
            This privacy policy is effective as of December 25, 2024. We may update this policy from time to time.
            We will notify you of any material changes via email or through our service.
          </p>
        </div>
      </div>
    </div>
  );
}

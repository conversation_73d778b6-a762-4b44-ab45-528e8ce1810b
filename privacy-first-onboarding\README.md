# Privacy-First User Onboarding Application

A comprehensive React + TypeScript + ShadCN application demonstrating privacy-first user onboarding with multitenant architecture, built according to the [PrivacyFirstImplementation Framework](https://github.com/PrivacyFirstImplementation/Framework).

## 🛡️ Privacy-First Features

### Core Privacy Framework
- **Global Privacy Law Compliance**: GDPR (EU), CCPA/CPRA (US), DPDP (India), PIPEDA (Canada), and more
- **Data Classification**: Automatic categorization of personal data types
- **Consent Management**: Granular consent controls with legal basis tracking
- **User Rights Management**: Complete implementation of privacy rights (access, correction, deletion, portability)
- **Regional Data Residency**: User-controlled data storage location preferences

### User Onboarding Flow
1. **Region Selection**: Choose data storage region with applicable privacy laws
2. **Consent Management**: Transparent consent collection with detailed explanations
3. **Account Creation**: Privacy-aware account setup with data residency preferences
4. **Success Dashboard**: Immediate access to privacy controls and data management

### Privacy Dashboard
- **Consent Preferences**: Toggle data processing consents in real-time
- **Data Export**: One-click data portability (JSON format)
- **Account Deletion**: Complete data erasure with confirmation
- **Privacy Health Score**: Visual representation of privacy configuration
- **Rights Exercise**: Submit requests for data correction, access, or deletion

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 15 with React 18 and TypeScript
- **UI Components**: ShadCN UI with Tailwind CSS
- **Form Handling**: React Hook Form with Zod validation
- **State Management**: React hooks with local state
- **Icons**: Lucide React

### Privacy Framework Components
```
src/
├── lib/
│   ├── privacy-types.ts      # Core privacy data types and schemas
│   ├── privacy-service.ts    # Privacy service layer with mock data
│   └── utils.ts             # Privacy regions, data categories, user rights
├── components/
│   ├── onboarding/          # Multi-step onboarding flow
│   │   ├── region-selector.tsx
│   │   ├── consent-manager.tsx
│   │   ├── account-form.tsx
│   │   ├── onboarding-flow.tsx
│   │   └── onboarding-success.tsx
│   └── dashboard/           # Privacy dashboard
│       └── privacy-dashboard.tsx
└── app/
    ├── page.tsx            # Main onboarding flow
    ├── dashboard/          # Privacy dashboard page
    ├── privacy/            # Privacy policy page
    └── terms/              # Terms and conditions page
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd privacy-first-onboarding
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📱 Usage

### Complete Onboarding Flow
1. **Start**: Visit the homepage to begin onboarding
2. **Select Region**: Choose your preferred data storage region
3. **Manage Consents**: Review and configure data processing preferences
4. **Create Account**: Complete account setup with privacy preferences
5. **Access Dashboard**: Use the generated User ID to access your privacy dashboard

### Privacy Dashboard Access
- Complete the onboarding flow to get a User ID
- Visit `/dashboard` and enter your User ID
- For demo purposes, any ID starting with "user_" will work

### Key Features to Test
- **Region Selection**: See how different regions show different applicable laws
- **Consent Management**: Toggle consents and see real-time updates
- **Data Export**: Download your complete data profile
- **Account Deletion**: Test the right to be forgotten
- **Privacy Health Score**: Monitor your privacy configuration

## 🔒 Privacy Implementation Details

### Data Categories
- Personal Identifiers (name, email)
- Contact Information
- Behavioral Data (usage analytics)
- Location Data
- Sensitive Personal Data
- And more...

### User Rights Implementation
- **Right to Access**: Complete data export functionality
- **Right to Correction**: Request data correction workflow
- **Right to Deletion**: Account and data deletion with confirmation
- **Right to Portability**: JSON data export
- **Right to Opt-Out**: Granular consent withdrawal
- **Right to Non-Discrimination**: Equal service regardless of privacy choices

### Regional Compliance
- **GDPR (EU)**: Explicit consent, data minimization, right to be forgotten
- **CCPA/CPRA (US)**: Opt-out rights, data sale restrictions, transparency
- **DPDP (India)**: Consent management, data localization, user rights
- **PIPEDA (Canada)**: Privacy by design, consent requirements
- **And more**: Extensible framework for additional jurisdictions

## 🧪 Testing

The application includes comprehensive privacy compliance testing:

### Manual Testing Checklist
- [ ] Complete onboarding flow from start to finish
- [ ] Test region selection with different privacy laws
- [ ] Verify consent management functionality
- [ ] Test data export (right to portability)
- [ ] Test account deletion (right to be forgotten)
- [ ] Verify privacy dashboard functionality
- [ ] Test consent withdrawal and re-granting

### Privacy Compliance Validation
- [ ] Consent is freely given, specific, informed, and unambiguous
- [ ] Data minimization principles are followed
- [ ] User rights are easily exercisable
- [ ] Data retention policies are clearly communicated
- [ ] Regional data residency preferences are respected

## 🌍 Multitenant Architecture

The application is designed for multitenant deployment:

### Tenant Configuration
- Region-specific data storage
- Customizable privacy policies
- Configurable consent requirements
- Tenant-specific compliance settings

### Data Isolation
- User data segregated by tenant
- Region-specific data residency
- Consent preferences per tenant
- Customizable retention policies

## 📚 Documentation

### Privacy Framework
Based on the [PrivacyFirstImplementation Framework](https://github.com/PrivacyFirstImplementation/Framework), this application implements:

- **Generic Privacy Framework**: Applicable across multiple privacy laws
- **B2C Scenario**: Consumer-focused privacy controls
- **Regional Compliance**: Automatic compliance with local privacy laws
- **User Rights**: Complete implementation of privacy rights
- **Data Classification**: Systematic categorization of personal data

### API Reference
The privacy service layer provides:
- User registration with privacy preferences
- Consent management (grant, withdraw, update)
- User rights exercise (access, correction, deletion)
- Data export and portability
- Regional compliance utilities

## 🤝 Contributing

This project demonstrates privacy-first development practices. When contributing:

1. Follow privacy-by-design principles
2. Ensure all data collection has clear legal basis
3. Implement user rights from the start
4. Test privacy compliance thoroughly
5. Document privacy implications of changes

## 📄 License

This project is licensed under the GPL-3.0 License - see the [LICENSE](LICENSE) file for details.

## 🔗 Related Projects

- [PrivacyFirstImplementation Framework](https://github.com/PrivacyFirstImplementation/Framework)
- [Privacy Laws Documentation](https://github.com/PrivacyFirstImplementation/Framework/blob/main/README.md)

## 📞 Support

For privacy-related questions or concerns:
- Email: <EMAIL>
- Privacy Dashboard: Built-in support for exercising your rights
- Documentation: Comprehensive privacy policy and terms of service

---

**Built with privacy in mind. Your data, your choice, your control.**

import { z } from 'zod';
import { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from './utils';

// Core Privacy Framework Types
export interface PrivacyConsent {
  id: string;
  userId: string;
  dataCategory: keyof typeof DATA_CATEGORIES;
  purpose: string;
  granted: boolean;
  timestamp: Date;
  expiresAt?: Date;
  region: keyof typeof PRIVACY_REGIONS;
  version: string;
}

export interface DataProcessingActivity {
  id: string;
  name: string;
  description: string;
  dataCategories: (keyof typeof DATA_CATEGORIES)[];
  purposes: string[];
  legalBasis: string;
  retentionPeriod: number; // in days
  regions: (keyof typeof PRIVACY_REGIONS)[];
  thirdParties?: string[];
}

export interface UserPrivacyProfile {
  userId: string;
  preferredRegion: keyof typeof PRIVACY_REGIONS;
  dataResidencyPreference: 'SAME_REGION' | 'GLOBAL' | 'CUSTOM';
  consentPreferences: PrivacyConsent[];
  communicationPreferences: {
    marketing: boolean;
    analytics: boolean;
    personalization: boolean;
    thirdPartySharing: boolean;
  };
  rightsExercised: {
    right: keyof typeof USER_RIGHTS;
    requestedAt: Date;
    completedAt?: Date;
    status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'REJECTED';
  }[];
}

export interface TenantConfiguration {
  tenantId: string;
  name: string;
  regions: (keyof typeof PRIVACY_REGIONS)[];
  defaultRegion: keyof typeof PRIVACY_REGIONS;
  dataRetentionPolicies: {
    [key in keyof typeof DATA_CATEGORIES]?: number; // days
  };
  complianceSettings: {
    requireExplicitConsent: boolean;
    enableRightToBeForgotten: boolean;
    enableDataPortability: boolean;
    enableOptOut: boolean;
    cookieConsentRequired: boolean;
    minorProtection: boolean;
  };
}

// Zod Schemas for Validation
export const PrivacyRegionSchema = z.enum(Object.keys(PRIVACY_REGIONS) as [keyof typeof PRIVACY_REGIONS, ...Array<keyof typeof PRIVACY_REGIONS>]);

export const DataCategorySchema = z.enum(Object.keys(DATA_CATEGORIES) as [keyof typeof DATA_CATEGORIES, ...Array<keyof typeof DATA_CATEGORIES>]);

export const UserRightSchema = z.enum(Object.keys(USER_RIGHTS) as [keyof typeof USER_RIGHTS, ...Array<keyof typeof USER_RIGHTS>]);

export const ConsentSchema = z.object({
  dataCategory: DataCategorySchema,
  purpose: z.string().min(1),
  granted: z.boolean(),
  region: PrivacyRegionSchema,
});

export const OnboardingFormSchema = z.object({
  // Basic Information
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  
  // Privacy Preferences
  preferredRegion: PrivacyRegionSchema,
  dataResidencyPreference: z.enum(['SAME_REGION', 'GLOBAL', 'CUSTOM']),
  
  // Consent Management
  consents: z.array(ConsentSchema),
  
  // Communication Preferences
  communicationPreferences: z.object({
    marketing: z.boolean().default(false),
    analytics: z.boolean().default(false),
    personalization: z.boolean().default(false),
    thirdPartySharing: z.boolean().default(false),
  }),
  
  // Terms and Privacy Policy
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions'
  }),
  acceptPrivacyPolicy: z.boolean().refine(val => val === true, {
    message: 'You must accept the privacy policy'
  }),
  
  // Age Verification
  ageVerification: z.boolean().refine(val => val === true, {
    message: 'You must confirm you are of legal age'
  }),
});

export type OnboardingFormData = z.infer<typeof OnboardingFormSchema>;

// Utility functions for privacy compliance
export const getApplicableLaws = (region: keyof typeof PRIVACY_REGIONS): string[] => {
  return PRIVACY_REGIONS[region].laws;
};

export const getDataRetentionPeriod = (
  dataCategory: keyof typeof DATA_CATEGORIES,
  region: keyof typeof PRIVACY_REGIONS
): number => {
  // Default retention periods based on common privacy law requirements
  const defaultPeriods: Record<keyof typeof DATA_CATEGORIES, number> = {
    PERSONAL_IDENTIFIERS: 2555, // 7 years
    CONTACT_INFO: 1095, // 3 years
    FINANCIAL_INFO: 2555, // 7 years
    BIOMETRIC_DATA: 365, // 1 year
    LOCATION_DATA: 365, // 1 year
    BEHAVIORAL_DATA: 730, // 2 years
    SENSITIVE_PERSONAL: 365, // 1 year
    HEALTH_DATA: 2555, // 7 years
    EMPLOYMENT_DATA: 2555, // 7 years
    EDUCATION_DATA: 2555, // 7 years
  };
  
  return defaultPeriods[dataCategory];
};

export const isConsentRequired = (
  dataCategory: keyof typeof DATA_CATEGORIES,
  region: keyof typeof PRIVACY_REGIONS
): boolean => {
  // GDPR and similar laws require explicit consent for most data processing
  const strictRegions: (keyof typeof PRIVACY_REGIONS)[] = ['EU', 'UK'];
  const sensitiveCategories: (keyof typeof DATA_CATEGORIES)[] = [
    'BIOMETRIC_DATA',
    'SENSITIVE_PERSONAL',
    'HEALTH_DATA',
    'LOCATION_DATA'
  ];
  
  return strictRegions.includes(region) || sensitiveCategories.includes(dataCategory);
};

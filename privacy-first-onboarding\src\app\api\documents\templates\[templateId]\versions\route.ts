// Document Versions API Route
// Server-side endpoint to safely fetch document versions

import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

interface RouteParams {
  params: {
    templateId: string;
  };
}

// GET /api/documents/templates/[templateId]/versions - Get document versions safely on server-side
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { templateId } = await params;

    if (!templateId) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    try {
      // First verify the template exists and belongs to the tenant
      const { data: template, error: templateError } = await supabaseAdmin
        .from('document_templates')
        .select('id, name, tenant_id')
        .eq('id', templateId)
        .eq('tenant_id', tenantId)
        .single();

      if (templateError || !template) {
        console.log('Template not found or access denied');
        return NextResponse.json({
          success: true,
          data: []
        });
      }

      // Get versions for this template
      const { data: versions, error: versionsError } = await supabaseAdmin
        .from('document_versions')
        .select(`
          id,
          template_id,
          tenant_id,
          version_number,
          version_type,
          markdown_content,
          generated_data,
          pdf_url,
          status,
          changelog,
          published_at,
          created_by,
          created_at
        `)
        .eq('template_id', templateId)
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (versionsError) {
        console.log('Versions table not available:', versionsError.message);
        return NextResponse.json({
          success: true,
          data: []
        });
      }

      return NextResponse.json({
        success: true,
        data: versions || []
      });

    } catch (error) {
      console.error('Error fetching versions:', error);
      
      // Return empty array as fallback
      return NextResponse.json({
        success: true,
        data: []
      });
    }
  } catch (error) {
    console.error('Error in versions API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

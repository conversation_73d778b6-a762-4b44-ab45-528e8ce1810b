// User Profile API Route
// Server-side endpoint to safely fetch user profiles

import { NextRequest, NextResponse } from 'next/server';
import { supabaseUtils } from '@/lib/supabase';

interface RouteParams {
  params: {
    userId: string;
  };
}

// GET /api/users/[userId]/profile - Get user profile safely on server-side
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check if Supabase is configured
    if (!supabaseUtils.isConfigured()) {
      console.log('Supabase not configured, returning mock data');
      return NextResponse.json({
        success: true,
        data: {
          id: userId,
          email: '<EMAIL>',
          first_name: 'Demo',
          last_name: 'User',
          tenant_id: '550e8400-e29b-41d4-a716-446655440000',
          preferred_region: 'US',
          data_residency_preference: 'SAME_REGION',
          marketing_consent: false,
          analytics_consent: true,
          personalization_consent: false,
          third_party_sharing: false,
          is_active: true,
          email_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          tenant: {
            id: '550e8400-e29b-41d4-a716-446655440000',
            name: 'Privacy First Application',
            region: 'US',
            created_at: new Date().toISOString()
          },
          consents: [],
          privacy_requests: []
        }
      });
    }

    try {
      // Use server-side supabase utils to fetch user profile
      const userData = await supabaseUtils.getUserPrivacyProfile(userId);
      
      return NextResponse.json({
        success: true,
        data: userData
      });
    } catch (error) {
      console.error('Error fetching user profile:', error);
      
      // Return mock data as fallback
      return NextResponse.json({
        success: true,
        data: {
          id: userId,
          email: '<EMAIL>',
          first_name: 'Demo',
          last_name: 'User',
          tenant_id: '550e8400-e29b-41d4-a716-446655440000',
          preferred_region: 'US',
          data_residency_preference: 'SAME_REGION',
          marketing_consent: false,
          analytics_consent: true,
          personalization_consent: false,
          third_party_sharing: false,
          is_active: true,
          email_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          tenant: {
            id: '550e8400-e29b-41d4-a716-446655440000',
            name: 'Privacy First Application',
            region: 'US',
            created_at: new Date().toISOString()
          },
          consents: [],
          privacy_requests: []
        }
      });
    }
  } catch (error) {
    console.error('Error in user profile API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

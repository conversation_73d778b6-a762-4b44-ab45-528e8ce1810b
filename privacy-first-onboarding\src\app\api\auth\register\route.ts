import { NextRequest, NextResponse } from 'next/server';
import { prisma, createAuditLog } from '@/lib/database';
import { hashPassword } from '@/lib/auth';
import { z } from 'zod';

const registerSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  tenantSlug: z.string().default('default'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = registerSchema.parse(body);

    // Find tenant
    const tenant = await prisma.tenant.findUnique({
      where: { slug: validatedData.tenantSlug },
    });

    if (!tenant) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        tenantId_email: {
          tenantId: tenant.id,
          email: validatedData.email,
        },
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists with this email' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);

    // Create user
    const user = await prisma.user.create({
      data: {
        tenantId: tenant.id,
        email: validatedData.email,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        hashedPassword,
        preferredRegion: tenant.defaultRegion,
        dataResidencyPreference: 'SAME_REGION',
      },
    });

    // Assign default user role
    const userRole = await prisma.role.findUnique({
      where: { name: 'user' },
    });

    if (userRole) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: userRole.id,
          tenantId: tenant.id,
        },
      });
    }

    // Create audit log
    await createAuditLog(
      'USER_REGISTRATION',
      'CREATE',
      'user',
      user.id,
      user.id,
      null,
      { 
        email: validatedData.email, 
        registrationMethod: 'web_form',
        tenantSlug: validatedData.tenantSlug 
      },
      { 
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
      },
      tenant.defaultRegion,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      request.headers.get('user-agent') || undefined
    );

    return NextResponse.json(
      { 
        message: 'User created successfully',
        userId: user.id 
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Registration error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

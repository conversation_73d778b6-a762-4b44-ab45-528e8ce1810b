'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Download,
  RefreshCw,
  Globe,
  Scale,
  FileText,
  Clock
} from 'lucide-react';
import { complianceEngine } from '@/lib/compliance-engine';
import { PRIVACY_REGIONS } from '@/lib/utils';

interface ComplianceDashboardProps {
  tenantId: string;
}

export function ComplianceDashboard({ tenantId }: ComplianceDashboardProps) {
  const [complianceReport, setComplianceReport] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<string>('all');

  useEffect(() => {
    loadComplianceReport();
  }, [tenantId]);

  const loadComplianceReport = async () => {
    try {
      setIsLoading(true);
      const report = await complianceEngine.generateComplianceReport(tenantId);
      setComplianceReport(report);
    } catch (error) {
      console.error('Failed to load compliance report:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadComplianceReport();
    setIsRefreshing(false);
  };

  const handleDownloadReport = () => {
    if (!complianceReport) return;

    const blob = new Blob([JSON.stringify(complianceReport, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `compliance-report-${tenantId}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Generating compliance report...</p>
        </div>
      </div>
    );
  }

  if (!complianceReport) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load compliance report. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  const filteredAssessments = selectedRegion === 'all' 
    ? complianceReport.assessments 
    : complianceReport.assessments.filter((a: any) => a.region === selectedRegion);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 90) return 'default';
    if (score >= 80) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Compliance Dashboard</h2>
          <p className="text-gray-600">
            Monitor compliance with global privacy laws and regulations
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleDownloadReport}>
            <Download className="h-4 w-4 mr-2" />
            Download Report
          </Button>
        </div>
      </div>

      {/* Overall Compliance Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Overall Compliance Score
          </CardTitle>
          <CardDescription>
            Generated on {new Date(complianceReport.generatedAt).toLocaleString()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className={`text-4xl font-bold ${getScoreColor(complianceReport.overallScore)}`}>
                {complianceReport.overallScore}%
              </div>
              <p className="text-sm text-gray-600 mt-1">Overall Score</p>
              <Badge 
                variant={getScoreBadgeVariant(complianceReport.overallScore)}
                className="mt-2"
              >
                {complianceReport.overallCompliant ? 'Compliant' : 'Non-Compliant'}
              </Badge>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {complianceReport.summary.compliantRules}
              </div>
              <p className="text-sm text-gray-600 mt-1">
                of {complianceReport.summary.totalRules} Rules
              </p>
              <p className="text-xs text-gray-500 mt-1">Compliant</p>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {complianceReport.summary.criticalIssues}
              </div>
              <p className="text-sm text-gray-600 mt-1">Critical Issues</p>
              <p className="text-xs text-gray-500 mt-1">Need Attention</p>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {complianceReport.regions.length}
              </div>
              <p className="text-sm text-gray-600 mt-1">Regions</p>
              <p className="text-xs text-gray-500 mt-1">Monitored</p>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex justify-between text-sm mb-2">
              <span>Compliance Progress</span>
              <span>{complianceReport.overallScore}%</span>
            </div>
            <Progress value={complianceReport.overallScore} className="h-3" />
          </div>
        </CardContent>
      </Card>

      {/* Regional Compliance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Regional Compliance
          </CardTitle>
          <CardDescription>
            Compliance status by region and applicable laws
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedRegion} onValueChange={setSelectedRegion}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">All Regions</TabsTrigger>
              <TabsTrigger value="EU">EU (GDPR)</TabsTrigger>
              <TabsTrigger value="US">US (CCPA)</TabsTrigger>
              <TabsTrigger value="IN">India (DPDP)</TabsTrigger>
              <TabsTrigger value="UK">UK (UK GDPR)</TabsTrigger>
            </TabsList>

            <TabsContent value={selectedRegion} className="mt-6">
              <div className="grid gap-4">
                {filteredAssessments.map((assessment: any, index: number) => (
                  <Card key={index} className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">
                            {PRIVACY_REGIONS[assessment.region as keyof typeof PRIVACY_REGIONS]?.name} - {assessment.law}
                          </CardTitle>
                          <CardDescription>
                            {assessment.findings.length} compliance rules assessed
                          </CardDescription>
                        </div>
                        <div className="text-right">
                          <div className={`text-2xl font-bold ${getScoreColor(assessment.score)}`}>
                            {assessment.score}%
                          </div>
                          <Badge variant={getScoreBadgeVariant(assessment.score)}>
                            {assessment.compliant ? 'Compliant' : 'Non-Compliant'}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {assessment.findings.map((finding: any, findingIndex: number) => (
                          <div key={findingIndex} className="flex items-start gap-3 p-3 border rounded-lg">
                            <div className="mt-0.5">
                              {finding.compliant ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-600" />
                              )}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium">{finding.requirement}</h4>
                              {finding.issues.length > 0 && (
                                <div className="mt-1">
                                  {finding.issues.map((issue: string, issueIndex: number) => (
                                    <p key={issueIndex} className="text-sm text-red-600">
                                      • {issue}
                                    </p>
                                  ))}
                                </div>
                              )}
                              {finding.evidence.length > 0 && (
                                <div className="mt-1">
                                  {finding.evidence.map((evidence: any, evidenceIndex: number) => (
                                    <p key={evidenceIndex} className="text-sm text-green-600">
                                      ✓ {evidence.value}
                                    </p>
                                  ))}
                                </div>
                              )}
                            </div>
                            <div className="text-right">
                              <span className={`text-sm font-medium ${getScoreColor(finding.score)}`}>
                                {finding.score}%
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>

                      {assessment.recommendations.length > 0 && (
                        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <h4 className="font-medium text-yellow-800 mb-2">Recommendations</h4>
                          <ul className="space-y-1">
                            {assessment.recommendations.map((rec: string, recIndex: number) => (
                              <li key={recIndex} className="text-sm text-yellow-700">
                                • {rec}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Action Items */}
      {complianceReport.summary.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              Priority Action Items
            </CardTitle>
            <CardDescription>
              Recommended actions to improve compliance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {complianceReport.summary.recommendations.slice(0, 10).map((recommendation: string, index: number) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  <Clock className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <span className="text-sm">{recommendation}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

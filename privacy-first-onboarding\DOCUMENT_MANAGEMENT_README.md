# Privacy-First Document Management System

A comprehensive document management system built on top of the privacy-first onboarding application, featuring document versioning, consent tracking, multi-channel notifications, and GDPR compliance.

## 🚀 Features

### 📄 Document Management
- **Template System**: Create reusable document templates for privacy policies, terms of service, and cookie policies
- **Version Control**: Full versioning system with semantic versioning (major.minor.patch)
- **Multi-Format Support**: Markdown templates with PDF generation capabilities
- **Compliance Frameworks**: Support for GDPR, CCPA, CPRA, PIPEDA, LGPD, and more

### 🔒 Privacy & Consent
- **Consent Tracking**: Track user consent with detailed audit trails
- **Embeddable Widgets**: JavaScript widgets for website integration
- **Multi-Channel Notifications**: Email, SMS, and webhook notifications
- **GDPR Compliance**: Built-in data export, deletion, and audit capabilities

### 🏢 Multi-Tenant Architecture
- **Tenant Isolation**: Complete data isolation using Row Level Security (RLS)
- **Regional Compliance**: Support for region-specific data residency requirements
- **Scalable Design**: PostgreSQL-based architecture with Supabase

## 🛠 Installation & Setup

### Prerequisites
- Node.js 18+ and npm/yarn
- Supabase account and project
- Environment variables configured

### Database Setup
1. Run the database migrations:
```bash
# Apply document management schema
node -e "
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function runMigrations() {
  const migrations = [
    'supabase/migrations/20241226_document_management_schema.sql',
    'supabase/migrations/20241226_document_management_rls.sql',
    'supabase/migrations/20241226_document_management_functions.sql'
  ];
  
  for (const migration of migrations) {
    const sql = fs.readFileSync(migration, 'utf8');
    const statements = sql.split(';').filter(stmt => stmt.trim());
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });
        if (error) console.error('Error:', error.message);
      }
    }
  }
  console.log('Migrations completed!');
}

runMigrations().catch(console.error);
"
```

2. Seed initial data:
```bash
# Load sample templates and data
psql -h your-supabase-host -U postgres -d postgres -f supabase/seed_document_management.sql
```

### Application Setup
1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Access the document management system:
```
http://localhost:3001/documents
```

## 📚 API Reference

### Document Templates
```typescript
// Create template
POST /api/documents?tenantId={tenantId}
{
  "template_type": "privacy_policy",
  "name": "Standard Privacy Policy",
  "description": "GDPR compliant privacy policy",
  "markdown_template": "# Privacy Policy...",
  "compliance_frameworks": ["GDPR", "CCPA"]
}

// Get templates
GET /api/documents?tenantId={tenantId}

// Update template
PUT /api/documents/{templateId}?tenantId={tenantId}
```

### Document Generation
```typescript
// Generate new version
POST /api/documents/generate?tenantId={tenantId}
{
  "template_id": "uuid",
  "template_data": {
    "company_name": "Your Company",
    "contact_email": "<EMAIL>"
  },
  "version_type": "minor",
  "auto_publish": true
}
```

### Consent Tracking
```typescript
// Track consent
POST /api/consent/track?tenantId={tenantId}
{
  "document_version_id": "uuid",
  "end_user_identifier": "<EMAIL>",
  "consent_given": true,
  "consent_method": "embedded_widget"
}

// Get user consent history
GET /api/consent/track?tenantId={tenantId}&userEmail={email}
```

### GDPR Compliance
```typescript
// Export user data
GET /api/gdpr/export?tenantId={tenantId}&userEmail={email}

// Delete user data
DELETE /api/gdpr/delete?tenantId={tenantId}&userEmail={email}&confirm=true
```

## 🎨 UI Components

### Document Management Dashboard
```tsx
import { DocumentManagement } from '@/components/documents/document-management';

<DocumentManagement tenantId="your-tenant-id" />
```

### Embeddable Consent Widget
```tsx
import { ConsentWidget } from '@/components/embed/consent-widget';

<ConsentWidget
  documentVersionId="version-uuid"
  tenantId="tenant-uuid"
  config={{
    widget_type: 'inline',
    styling: {
      background_color: '#ffffff',
      button_color: '#3b82f6'
    }
  }}
/>
```

### GDPR Compliance Dashboard
```tsx
import { GDPRCompliance } from '@/components/documents/gdpr-compliance';

<GDPRCompliance tenantId="your-tenant-id" />
```

## 🔧 Configuration

### Environment Variables
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application Configuration
NEXT_PUBLIC_BASE_URL=http://localhost:3001
```

### Notification Settings
Configure email, SMS, and webhook notifications through the UI or API:

```typescript
// Email configuration
{
  "notification_type": "email",
  "configuration": {
    "smtp_host": "smtp.example.com",
    "smtp_port": 587,
    "from_email": "<EMAIL>",
    "subject_template": "{{document_type}} Updated",
    "include_pdf": true
  }
}
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
node test-document-management-system.js
```

This tests:
- ✅ Document templates and versions
- ✅ Consent tracking and analytics
- ✅ Notification queue processing
- ✅ Audit logging and compliance
- ✅ GDPR data export/deletion
- ✅ RLS policies and security
- ✅ Database functions and triggers

## 🔐 Security Features

### Row Level Security (RLS)
- Complete tenant data isolation
- Policy-based access control
- Automatic tenant context enforcement

### Audit Logging
- Complete audit trail of all actions
- IP address and user agent tracking
- Compliance reporting and analytics

### Data Protection
- Encrypted data at rest and in transit
- GDPR-compliant data handling
- Automated data retention policies

## 📊 Database Schema

### Core Tables
- `document_templates` - Document template definitions
- `document_versions` - Version history and content
- `end_user_consents` - Consent tracking records
- `notification_preferences` - Multi-channel notification settings
- `notification_history` - Delivery tracking and logs
- `document_audit_log` - Complete audit trail

### Key Relationships
```sql
document_templates (1) -> (many) document_versions
document_versions (1) -> (many) end_user_consents
tenants (1) -> (many) document_templates
```

## 🚀 Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] RLS policies enabled
- [ ] SSL certificates installed
- [ ] Monitoring and logging configured
- [ ] Backup strategy implemented

### Scaling Considerations
- Use read replicas for analytics queries
- Implement caching for frequently accessed templates
- Consider CDN for PDF document delivery
- Monitor database performance and optimize queries

## 📈 Analytics & Reporting

The system provides comprehensive analytics:
- Document performance metrics
- Consent rates and trends
- User engagement analytics
- Compliance reporting
- Audit trail analysis

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Run the test suite to verify setup
- Review the audit logs for troubleshooting
- Contact the development team

---

**Built with ❤️ for privacy-first applications**

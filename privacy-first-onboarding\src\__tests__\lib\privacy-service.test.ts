import { privacyService } from '@/lib/privacy-service';
import { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from '@/lib/utils';

describe('Privacy Service', () => {
  beforeEach(() => {
    // Reset the service state before each test
    jest.clearAllMocks();
  });

  describe('User Registration', () => {
    it('should register a user with valid data', async () => {
      const formData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        preferredRegion: 'US' as const,
        dataResidencyPreference: 'SAME_REGION' as const,
        consents: [
          {
            dataCategory: 'PERSONAL_IDENTIFIERS' as const,
            purpose: 'Account Creation',
            granted: true,
            region: 'US' as const,
          },
        ],
        communicationPreferences: {
          marketing: false,
          analytics: true,
          personalization: false,
          thirdPartySharing: false,
        },
        acceptTerms: true,
        acceptPrivacyPolicy: true,
        ageVerification: true,
      };

      const result = await privacyService.registerUser(formData);

      expect(result.success).toBe(true);
      expect(result.userId).toBeDefined();
      expect(typeof result.userId).toBe('string');
    });

    it('should create consents during registration', async () => {
      const formData = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        preferredRegion: 'EU' as const,
        dataResidencyPreference: 'SAME_REGION' as const,
        consents: [
          {
            dataCategory: 'PERSONAL_IDENTIFIERS' as const,
            purpose: 'Account Creation',
            granted: true,
            region: 'EU' as const,
          },
          {
            dataCategory: 'CONTACT_INFO' as const,
            purpose: 'Marketing',
            granted: false,
            region: 'EU' as const,
          },
        ],
        communicationPreferences: {
          marketing: false,
          analytics: false,
          personalization: false,
          thirdPartySharing: false,
        },
        acceptTerms: true,
        acceptPrivacyPolicy: true,
        ageVerification: true,
      };

      const result = await privacyService.registerUser(formData);
      const consents = await privacyService.getConsents(result.userId);

      expect(consents).toHaveLength(2);
      expect(consents[0].granted).toBe(true);
      expect(consents[1].granted).toBe(false);
    });
  });

  describe('Consent Management', () => {
    let userId: string;

    beforeEach(async () => {
      const formData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        preferredRegion: 'US' as const,
        dataResidencyPreference: 'SAME_REGION' as const,
        consents: [
          {
            dataCategory: 'PERSONAL_IDENTIFIERS' as const,
            purpose: 'Account Creation',
            granted: true,
            region: 'US' as const,
          },
        ],
        communicationPreferences: {
          marketing: false,
          analytics: false,
          personalization: false,
          thirdPartySharing: false,
        },
        acceptTerms: true,
        acceptPrivacyPolicy: true,
        ageVerification: true,
      };

      const result = await privacyService.registerUser(formData);
      userId = result.userId;
    });

    it('should retrieve user consents', async () => {
      const consents = await privacyService.getConsents(userId);
      
      expect(Array.isArray(consents)).toBe(true);
      expect(consents.length).toBeGreaterThan(0);
      expect(consents[0]).toHaveProperty('id');
      expect(consents[0]).toHaveProperty('dataCategory');
      expect(consents[0]).toHaveProperty('purpose');
      expect(consents[0]).toHaveProperty('granted');
    });

    it('should update consent status', async () => {
      const consents = await privacyService.getConsents(userId);
      const consentId = consents[0].id;
      const originalStatus = consents[0].granted;

      const updateResult = await privacyService.updateConsent(userId, consentId, !originalStatus);
      expect(updateResult).toBe(true);

      const updatedConsents = await privacyService.getConsents(userId);
      const updatedConsent = updatedConsents.find(c => c.id === consentId);
      expect(updatedConsent?.granted).toBe(!originalStatus);
    });

    it('should grant new consent', async () => {
      const consentId = await privacyService.grantConsent(
        userId,
        'BEHAVIORAL_DATA',
        'Analytics',
        'US'
      );

      expect(consentId).toBeDefined();
      expect(typeof consentId).toBe('string');

      const consents = await privacyService.getConsents(userId);
      const newConsent = consents.find(c => c.id === consentId);
      expect(newConsent).toBeDefined();
      expect(newConsent?.granted).toBe(true);
    });
  });

  describe('User Rights Management', () => {
    let userId: string;

    beforeEach(async () => {
      const formData = {
        firstName: 'Rights',
        lastName: 'Test',
        email: '<EMAIL>',
        preferredRegion: 'EU' as const,
        dataResidencyPreference: 'SAME_REGION' as const,
        consents: [
          {
            dataCategory: 'PERSONAL_IDENTIFIERS' as const,
            purpose: 'Account Creation',
            granted: true,
            region: 'EU' as const,
          },
        ],
        communicationPreferences: {
          marketing: false,
          analytics: false,
          personalization: false,
          thirdPartySharing: false,
        },
        acceptTerms: true,
        acceptPrivacyPolicy: true,
        ageVerification: true,
      };

      const result = await privacyService.registerUser(formData);
      userId = result.userId;
    });

    it('should exercise right to access', async () => {
      const requestId = await privacyService.exerciseUserRight(userId, 'ACCESS');
      
      expect(requestId).toBeDefined();
      expect(typeof requestId).toBe('string');
    });

    it('should export user data', async () => {
      const exportData = await privacyService.exportUserData(userId);
      
      expect(exportData).toBeDefined();
      expect(exportData).toHaveProperty('profile');
      expect(exportData).toHaveProperty('consents');
      expect(exportData).toHaveProperty('exportedAt');
      expect(exportData.format).toBe('JSON');
    });

    it('should delete user data', async () => {
      const deleteResult = await privacyService.deleteUserData(userId);
      expect(deleteResult).toBe(true);

      // Verify data is deleted by trying to get privacy dashboard data
      const dashboardData = await privacyService.getPrivacyDashboardData(userId);
      expect(dashboardData.profile).toBeNull();
    });
  });

  describe('Privacy Dashboard', () => {
    let userId: string;

    beforeEach(async () => {
      const formData = {
        firstName: 'Dashboard',
        lastName: 'Test',
        email: '<EMAIL>',
        preferredRegion: 'UK' as const,
        dataResidencyPreference: 'SAME_REGION' as const,
        consents: [
          {
            dataCategory: 'PERSONAL_IDENTIFIERS' as const,
            purpose: 'Account Creation',
            granted: true,
            region: 'UK' as const,
          },
          {
            dataCategory: 'CONTACT_INFO' as const,
            purpose: 'Marketing',
            granted: false,
            region: 'UK' as const,
          },
        ],
        communicationPreferences: {
          marketing: false,
          analytics: true,
          personalization: false,
          thirdPartySharing: false,
        },
        acceptTerms: true,
        acceptPrivacyPolicy: true,
        ageVerification: true,
      };

      const result = await privacyService.registerUser(formData);
      userId = result.userId;
    });

    it('should return complete dashboard data', async () => {
      const dashboardData = await privacyService.getPrivacyDashboardData(userId);
      
      expect(dashboardData.profile).toBeDefined();
      expect(dashboardData.consents).toBeDefined();
      expect(dashboardData.applicableLaws).toBeDefined();
      expect(dashboardData.dataRetentionInfo).toBeDefined();

      expect(dashboardData.profile?.userId).toBe(userId);
      expect(dashboardData.profile?.preferredRegion).toBe('UK');
      expect(dashboardData.applicableLaws).toContain('UK GDPR');
    });

    it('should return applicable laws for region', async () => {
      const dashboardData = await privacyService.getPrivacyDashboardData(userId);
      const ukLaws = PRIVACY_REGIONS.UK.laws;
      
      expect(dashboardData.applicableLaws).toEqual(ukLaws);
    });
  });

  describe('Region Detection', () => {
    it('should detect region from IP address', async () => {
      const region = await privacyService.detectRegionFromIP('***********');
      
      expect(region).toBeDefined();
      expect(Object.keys(PRIVACY_REGIONS)).toContain(region);
    });
  });

  describe('Tenant Configuration', () => {
    it('should return tenant configuration', async () => {
      const config = await privacyService.getTenantConfig('default');
      
      expect(config).toBeDefined();
      expect(config?.tenantId).toBe('default');
      expect(config?.complianceSettings).toBeDefined();
      expect(config?.dataRetentionPolicies).toBeDefined();
    });

    it('should return null for non-existent tenant', async () => {
      const config = await privacyService.getTenantConfig('non-existent');
      expect(config).toBeNull();
    });
  });

  describe('Processing Activities', () => {
    it('should return applicable processing activities for region', async () => {
      const activities = await privacyService.getApplicableProcessingActivities('EU');
      
      expect(Array.isArray(activities)).toBe(true);
      activities.forEach(activity => {
        expect(activity.regions).toContain('EU');
      });
    });
  });
});

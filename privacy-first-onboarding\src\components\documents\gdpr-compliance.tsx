'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Shield, 
  Download, 
  Trash2, 
  Search, 
  FileText, 
  Clock, 
  User,
  AlertTriangle,
  CheckCircle,
  Eye,
  Mail,
  Calendar
} from 'lucide-react';
import { GDPRDataExport } from '@/lib/document-types';

interface GDPRComplianceProps {
  tenantId: string;
}

interface DataSubjectRequest {
  id: string;
  type: 'access' | 'correction' | 'deletion' | 'portability';
  userEmail: string;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  requestedAt: string;
  completedAt?: string;
  description?: string;
}

interface AuditLogEntry {
  id: string;
  action: string;
  resourceType: string;
  resourceId: string;
  userId?: string;
  userEmail?: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  details?: any;
}

export function GDPRCompliance({ tenantId }: GDPRComplianceProps) {
  const [searchEmail, setSearchEmail] = useState('');
  const [exportData, setExportData] = useState<GDPRDataExport | null>(null);
  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState<DataSubjectRequest[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteEmail, setDeleteEmail] = useState('');
  const [deleteReason, setDeleteReason] = useState('');

  useEffect(() => {
    loadRequests();
    loadAuditLogs();
  }, [tenantId]);

  const loadRequests = async () => {
    // Mock data for demonstration
    const mockRequests: DataSubjectRequest[] = [
      {
        id: '1',
        type: 'access',
        userEmail: '<EMAIL>',
        status: 'completed',
        requestedAt: '2024-01-15T10:30:00Z',
        completedAt: '2024-01-16T14:20:00Z',
        description: 'User requested access to all personal data'
      },
      {
        id: '2',
        type: 'deletion',
        userEmail: '<EMAIL>',
        status: 'pending',
        requestedAt: '2024-01-20T09:15:00Z',
        description: 'User requested account deletion'
      },
      {
        id: '3',
        type: 'correction',
        userEmail: '<EMAIL>',
        status: 'processing',
        requestedAt: '2024-01-22T16:45:00Z',
        description: 'User requested correction of email address'
      }
    ];
    setRequests(mockRequests);
  };

  const loadAuditLogs = async () => {
    // Mock data for demonstration
    const mockLogs: AuditLogEntry[] = [
      {
        id: '1',
        action: 'DATA_EXPORT',
        resourceType: 'user_data',
        resourceId: '<EMAIL>',
        userId: 'admin-1',
        userEmail: '<EMAIL>',
        timestamp: '2024-01-16T14:20:00Z',
        ipAddress: '*************',
        details: { exportType: 'full', recordCount: 45 }
      },
      {
        id: '2',
        action: 'CONSENT_GRANTED',
        resourceType: 'consent',
        resourceId: 'consent-123',
        userEmail: '<EMAIL>',
        timestamp: '2024-01-22T10:15:00Z',
        ipAddress: '*************',
        details: { documentType: 'privacy_policy', version: '1.2.0' }
      },
      {
        id: '3',
        action: 'DOCUMENT_PUBLISHED',
        resourceType: 'document_version',
        resourceId: 'version-456',
        userId: 'admin-1',
        userEmail: '<EMAIL>',
        timestamp: '2024-01-23T09:30:00Z',
        ipAddress: '*************',
        details: { documentType: 'terms_of_service', version: '1.1.0', notifiedUsers: 42 }
      }
    ];
    setAuditLogs(mockLogs);
  };

  const handleDataExport = async () => {
    if (!searchEmail.trim()) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/gdpr/export?tenantId=${tenantId}&userEmail=${encodeURIComponent(searchEmail)}`);
      
      if (response.ok) {
        const data = await response.json();
        setExportData(data);
      } else {
        console.error('Export failed');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDataDeletion = async () => {
    if (!deleteEmail.trim()) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/gdpr/delete?tenantId=${tenantId}&userEmail=${encodeURIComponent(deleteEmail)}&confirm=true`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('Deletion completed:', data);
        setShowDeleteConfirm(false);
        setDeleteEmail('');
        setDeleteReason('');
        // Refresh requests
        loadRequests();
      } else {
        console.error('Deletion failed');
      }
    } catch (error) {
      console.error('Error deleting data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRequestTypeIcon = (type: string) => {
    switch (type) {
      case 'access':
        return <Eye className="h-4 w-4" />;
      case 'correction':
        return <FileText className="h-4 w-4" />;
      case 'deletion':
        return <Trash2 className="h-4 w-4" />;
      case 'portability':
        return <Download className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getRequestTypeName = (type: string) => {
    switch (type) {
      case 'access':
        return 'Data Access';
      case 'correction':
        return 'Data Correction';
      case 'deletion':
        return 'Data Deletion';
      case 'portability':
        return 'Data Portability';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">GDPR Compliance</h2>
        <p className="text-muted-foreground">
          Manage data subject rights, audit trails, and compliance reporting
        </p>
      </div>

      <Tabs defaultValue="data-export" className="space-y-4">
        <TabsList>
          <TabsTrigger value="data-export">Data Export</TabsTrigger>
          <TabsTrigger value="requests">Subject Requests</TabsTrigger>
          <TabsTrigger value="audit-logs">Audit Logs</TabsTrigger>
          <TabsTrigger value="compliance">Compliance Report</TabsTrigger>
        </TabsList>

        <TabsContent value="data-export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Data Export (Right to Access)
              </CardTitle>
              <CardDescription>
                Export all data for a specific user in compliance with GDPR Article 15
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Label htmlFor="export-email">User Email</Label>
                  <Input
                    id="export-email"
                    type="email"
                    value={searchEmail}
                    onChange={(e) => setSearchEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={handleDataExport} disabled={loading || !searchEmail.trim()}>
                    <Search className="h-4 w-4 mr-2" />
                    {loading ? 'Exporting...' : 'Export Data'}
                  </Button>
                </div>
              </div>

              {exportData && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Export Results</CardTitle>
                    <CardDescription>
                      Data export for {exportData.user_email} on {new Date(exportData.export_date).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">Consent Records</h4>
                        <p className="text-2xl font-bold text-blue-600">{exportData.consents.length}</p>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2">Notifications</h4>
                        <p className="text-2xl font-bold text-green-600">{exportData.notifications.length}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Consent History</h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {exportData.consents.map((consent, index) => (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <span className="font-medium">{consent.document_name}</span>
                              <span className="text-sm text-muted-foreground ml-2">v{consent.document_version}</span>
                            </div>
                            <Badge variant={consent.consent_given ? 'default' : 'secondary'}>
                              {consent.consent_given ? 'Granted' : 'Declined'}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Button
                      onClick={() => {
                        const dataStr = JSON.stringify(exportData, null, 2);
                        const dataBlob = new Blob([dataStr], { type: 'application/json' });
                        const url = URL.createObjectURL(dataBlob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `gdpr-export-${exportData.user_email}-${new Date().toISOString().split('T')[0]}.json`;
                        link.click();
                      }}
                      className="w-full"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Export File
                    </Button>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trash2 className="h-5 w-5" />
                Data Deletion (Right to be Forgotten)
              </CardTitle>
              <CardDescription>
                Permanently delete all user data in compliance with GDPR Article 17
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive" className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Warning:</strong> Data deletion is permanent and cannot be undone. 
                  Ensure you have proper authorization before proceeding.
                </AlertDescription>
              </Alert>

              <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
                <DialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete User Data
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Confirm Data Deletion</DialogTitle>
                    <DialogDescription>
                      This action will permanently delete all data for the specified user.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="delete-email">User Email</Label>
                      <Input
                        id="delete-email"
                        type="email"
                        value={deleteEmail}
                        onChange={(e) => setDeleteEmail(e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="delete-reason">Reason for Deletion</Label>
                      <Textarea
                        id="delete-reason"
                        value={deleteReason}
                        onChange={(e) => setDeleteReason(e.target.value)}
                        placeholder="User requested deletion under GDPR Article 17"
                        rows={3}
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => setShowDeleteConfirm(false)}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleDataDeletion}
                        disabled={loading || !deleteEmail.trim()}
                        className="flex-1"
                      >
                        {loading ? 'Deleting...' : 'Confirm Deletion'}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Data Subject Requests
              </CardTitle>
              <CardDescription>
                Track and manage all data subject rights requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {requests.map((request) => (
                  <div key={request.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {getRequestTypeIcon(request.type)}
                          <h4 className="font-medium">{getRequestTypeName(request.type)}</h4>
                          <Badge className={getStatusColor(request.status)}>
                            {request.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          <Mail className="h-3 w-3 inline mr-1" />
                          {request.userEmail}
                        </p>
                        <p className="text-sm text-muted-foreground mb-2">
                          <Calendar className="h-3 w-3 inline mr-1" />
                          Requested: {new Date(request.requestedAt).toLocaleDateString()}
                          {request.completedAt && (
                            <span className="ml-4">
                              <CheckCircle className="h-3 w-3 inline mr-1" />
                              Completed: {new Date(request.completedAt).toLocaleDateString()}
                            </span>
                          )}
                        </p>
                        {request.description && (
                          <p className="text-sm">{request.description}</p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        {request.status === 'pending' && (
                          <Button size="sm">
                            Process
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit-logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Audit Logs
              </CardTitle>
              <CardDescription>
                Complete audit trail of all privacy-related actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {auditLogs.map((log) => (
                  <div key={log.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline">{log.action}</Badge>
                          <span className="text-sm text-muted-foreground">
                            {log.resourceType}
                          </span>
                        </div>
                        <div className="text-sm space-y-1">
                          {log.userEmail && (
                            <p>
                              <User className="h-3 w-3 inline mr-1" />
                              User: {log.userEmail}
                            </p>
                          )}
                          <p>
                            <Clock className="h-3 w-3 inline mr-1" />
                            {new Date(log.timestamp).toLocaleString()}
                          </p>
                          {log.ipAddress && (
                            <p className="text-muted-foreground">
                              IP: {log.ipAddress}
                            </p>
                          )}
                          {log.details && (
                            <p className="text-muted-foreground">
                              Details: {JSON.stringify(log.details)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                    <p className="text-2xl font-bold">{requests.length}</p>
                  </div>
                  <User className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Completed</p>
                    <p className="text-2xl font-bold text-green-600">
                      {requests.filter(r => r.status === 'completed').length}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Pending</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {requests.filter(r => r.status === 'pending').length}
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Audit Entries</p>
                    <p className="text-2xl font-bold">{auditLogs.length}</p>
                  </div>
                  <Shield className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Compliance Summary</CardTitle>
              <CardDescription>
                Overview of GDPR compliance status and metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Data Processing Records</h4>
                    <p className="text-sm text-muted-foreground">
                      Article 30 - Records of processing activities
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Compliant
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Data Subject Rights</h4>
                    <p className="text-sm text-muted-foreground">
                      Articles 15-22 - Individual rights implementation
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Compliant
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Consent Management</h4>
                    <p className="text-sm text-muted-foreground">
                      Article 7 - Conditions for consent
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Compliant
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Data Breach Procedures</h4>
                    <p className="text-sm text-muted-foreground">
                      Articles 33-34 - Breach notification
                    </p>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Review Required
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  ArrowLeft,
  Plus,
  Eye,
  Download,
  Share,
  Clock,
  Users,
  CheckCircle,
  AlertCircle,
  FileText,
  GitBranch,
  Send,
  FolderArchive
} from 'lucide-react';
import { DocumentTemplate, DocumentVersion } from '@/lib/document-types';
import { DocumentManagementService } from '@/lib/document-service';
import { DocumentGenerator } from './document-generator';
import { VersionPublisher } from './version-publisher';

interface VersionHistoryProps {
  template: DocumentTemplate;
  tenantId: string;
  onBack: () => void;
}

export function VersionHistory({ template, tenantId, onBack }: VersionHistoryProps) {
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [selectedVersion, setSelectedVersion] = useState<DocumentVersion | null>(null);
  const [showGenerator, setShowGenerator] = useState(false);
  const [showPublisher, setShowPublisher] = useState(false);
  const [loading, setLoading] = useState(true);
  const [previewContent, setPreviewContent] = useState('');

  const documentService = new DocumentManagementService(tenantId);

  useEffect(() => {
    loadVersions();
  }, [template.id]);

  const loadVersions = async () => {
    setLoading(true);
    try {
      const response = await documentService.getVersions(template.id);
      if (response.success && response.data) {
        setVersions(response.data);
      }
    } catch (error) {
      console.error('Error loading versions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateVersion = () => {
    setShowGenerator(true);
  };

  const handlePublishVersion = (version: DocumentVersion) => {
    setSelectedVersion(version);
    setShowPublisher(true);
  };

  const handleViewVersion = async (version: DocumentVersion) => {
    setSelectedVersion(version);
    setPreviewContent(version.markdown_content);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="h-4 w-4" />;
      case 'draft':
        return <Clock className="h-4 w-4" />;
      case 'archived':
        return <FolderArchive className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getVersionTypeColor = (type: string) => {
    switch (type) {
      case 'major':
        return 'bg-red-100 text-red-800';
      case 'minor':
        return 'bg-blue-100 text-blue-800';
      case 'patch':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (showGenerator) {
    return (
      <DocumentGenerator
        template={template}
        tenantId={tenantId}
        onSave={() => {
          setShowGenerator(false);
          loadVersions();
        }}
        onCancel={() => setShowGenerator(false)}
      />
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{template.name}</h1>
            <p className="text-muted-foreground">Version History & Management</p>
          </div>
        </div>
        <Button onClick={handleCreateVersion} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Version
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Version List */}
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Version History
              </CardTitle>
              <CardDescription>
                All versions of {template.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-16 bg-gray-200 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : versions.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No versions yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Create your first version to get started.
                  </p>
                  <Button onClick={handleCreateVersion}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Version
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {versions.map((version) => (
                    <div
                      key={version.id}
                      className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={getVersionTypeColor(version.version_type)}>
                              v{version.version_number}
                            </Badge>
                            <Badge className={getStatusColor(version.status)}>
                              <span className="flex items-center gap-1">
                                {getStatusIcon(version.status)}
                                {version.status}
                              </span>
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-2">
                            {version.changelog || 'No changelog provided'}
                          </p>
                          
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>Created: {new Date(version.created_at).toLocaleDateString()}</span>
                            {version.published_at && (
                              <span>Published: {new Date(version.published_at).toLocaleDateString()}</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewVersion(version)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                          
                          {version.status === 'draft' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePublishVersion(version)}
                            >
                              <Send className="h-3 w-3 mr-1" />
                              Publish
                            </Button>
                          )}
                          
                          {version.pdf_url && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(version.pdf_url, '_blank')}
                            >
                              <Download className="h-3 w-3 mr-1" />
                              PDF
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Version Preview */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Preview
              </CardTitle>
              <CardDescription>
                {selectedVersion 
                  ? `Version ${selectedVersion.version_number}` 
                  : 'Select a version to preview'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedVersion ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(selectedVersion.status)}>
                      {selectedVersion.status}
                    </Badge>
                    <Badge className={getVersionTypeColor(selectedVersion.version_type)}>
                      {selectedVersion.version_type}
                    </Badge>
                  </div>
                  
                  <div className="prose prose-sm max-w-none">
                    <pre className="whitespace-pre-wrap text-xs bg-muted p-3 rounded max-h-96 overflow-y-auto">
                      {previewContent}
                    </pre>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        navigator.clipboard.writeText(previewContent);
                      }}
                    >
                      Copy Content
                    </Button>
                    {selectedVersion.pdf_url && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(selectedVersion.pdf_url, '_blank')}
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-8 w-8 mx-auto mb-2" />
                  <p>Select a version to preview its content</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Version Publisher Dialog */}
      {showPublisher && selectedVersion && (
        <VersionPublisher
          version={selectedVersion}
          tenantId={tenantId}
          onPublish={() => {
            setShowPublisher(false);
            loadVersions();
          }}
          onCancel={() => setShowPublisher(false)}
        />
      )}
    </div>
  );
}

// API Route for Document Generation
import { NextRequest, NextResponse } from 'next/server';
import { DocumentGeneratorService } from '@/lib/document-generator-service';
import { GenerateDocumentRequest } from '@/lib/document-generator-types';

// POST /api/document-generator/generate - Generate a document from a session
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tenant_id, ...generateData } = body as GenerateDocumentRequest & { tenant_id: string };

    if (!tenant_id) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    if (!generateData.session_id || !generateData.title) {
      return NextResponse.json(
        { success: false, error: 'Session ID and title are required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentGeneratorService(tenant_id);
    const response = await documentService.generateDocument(generateData);

    return NextResponse.json(response, {
      status: response.success ? 201 : 400
    });
  } catch (error) {
    console.error('Error in POST /api/document-generator/generate:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Privacy Framework Utilities\nexport const PRIVACY_REGIONS = {\n  US: { code: 'US', name: 'United States', laws: ['CCPA', 'CPRA'] },\n  EU: { code: 'EU', name: 'European Union', laws: ['GDPR'] },\n  UK: { code: 'UK', name: 'United Kingdom', laws: ['UK GDPR', 'DPA 2018'] },\n  CA: { code: 'CA', name: 'Canada', laws: ['PIPEDA', 'CPPA'] },\n  IN: { code: 'IN', name: 'India', laws: ['DPDP'] },\n  SG: { code: 'SG', name: 'Singapore', laws: ['PDPA'] },\n  JP: { code: 'JP', name: 'Japan', laws: ['APPI'] },\n  AU: { code: 'AU', name: 'Australia', laws: ['Privacy Act'] },\n  BR: { code: 'BR', name: 'Brazil', laws: ['LGPD'] },\n  CN: { code: 'CN', name: 'China', laws: ['PIPL'] },\n} as const;\n\nexport type PrivacyRegion = keyof typeof PRIVACY_REGIONS;\n\nexport const DATA_CATEGORIES = {\n  PERSONAL_IDENTIFIERS: 'Personal Identifiers',\n  CONTACT_INFO: 'Contact Information',\n  FINANCIAL_INFO: 'Financial Information',\n  BIOMETRIC_DATA: 'Biometric Data',\n  LOCATION_DATA: 'Location Data',\n  BEHAVIORAL_DATA: 'Behavioral Data',\n  SENSITIVE_PERSONAL: 'Sensitive Personal Data',\n  HEALTH_DATA: 'Health Data',\n  EMPLOYMENT_DATA: 'Employment Data',\n  EDUCATION_DATA: 'Education Data',\n} as const;\n\nexport type DataCategory = keyof typeof DATA_CATEGORIES;\n\nexport const USER_RIGHTS = {\n  ACCESS: 'Right to Access',\n  CORRECTION: 'Right to Correction',\n  DELETION: 'Right to Deletion',\n  PORTABILITY: 'Right to Data Portability',\n  OPT_OUT: 'Right to Opt-Out',\n  RESTRICT_PROCESSING: 'Right to Restrict Processing',\n  OBJECT: 'Right to Object',\n  NON_DISCRIMINATION: 'Right to Non-Discrimination',\n} as const;\n\nexport type UserRight = keyof typeof USER_RIGHTS;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QAAE,MAAM;QAAM,MAAM;QAAiB,MAAM;YAAC;YAAQ;SAAO;IAAC;IAChE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;SAAO;IAAC;IACzD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;YAAW;SAAW;IAAC;IACxE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;YAAU;SAAO;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAO;IAAC;IACpD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAc;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;SAAO;IAAC;IACjD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;AAClD;AAIO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;AAClB;AAIO,MAAM,cAAc;IACzB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,aAAa;IACb,SAAS;IACT,qBAAqB;IACrB,QAAQ;IACR,oBAAoB;AACtB", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/onboarding/region-selector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Label } from '@/components/ui/label';\nimport { Globe, Shield, MapPin } from 'lucide-react';\nimport { PRIVACY_REGIONS } from '@/lib/utils';\nimport type { PrivacyRegion } from '@/lib/utils';\n\ninterface RegionSelectorProps {\n  selectedRegion: PrivacyRegion | null;\n  onRegionSelect: (region: PrivacyRegion) => void;\n  onNext: () => void;\n}\n\nexport function RegionSelector({ selectedRegion, onRegionSelect, onNext }: RegionSelectorProps) {\n  const [detectedRegion] = useState<PrivacyRegion>('US'); // In production, detect from IP\n\n  const handleRegionChange = (value: string) => {\n    onRegionSelect(value as PrivacyRegion);\n  };\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader className=\"text-center\">\n        <div className=\"flex justify-center mb-4\">\n          <div className=\"p-3 bg-blue-100 rounded-full\">\n            <Globe className=\"h-8 w-8 text-blue-600\" />\n          </div>\n        </div>\n        <CardTitle className=\"text-2xl\">Choose Your Data Region</CardTitle>\n        <CardDescription className=\"text-lg\">\n          Select where you'd like your personal data to be stored and processed. \n          This ensures compliance with your local privacy laws.\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-6\">\n        {/* Detected Region Info */}\n        <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\n          <div className=\"flex items-center gap-2 mb-2\">\n            <MapPin className=\"h-4 w-4 text-blue-600\" />\n            <span className=\"font-medium text-blue-900\">Detected Location</span>\n          </div>\n          <p className=\"text-sm text-blue-700\">\n            Based on your location, we recommend: <strong>{PRIVACY_REGIONS[detectedRegion].name}</strong>\n          </p>\n        </div>\n\n        {/* Region Selection */}\n        <div className=\"space-y-3\">\n          <Label htmlFor=\"region-select\" className=\"text-base font-medium\">\n            Select Your Preferred Data Region\n          </Label>\n          <Select value={selectedRegion || ''} onValueChange={handleRegionChange}>\n            <SelectTrigger id=\"region-select\" className=\"h-12\">\n              <SelectValue placeholder=\"Choose a region...\" />\n            </SelectTrigger>\n            <SelectContent>\n              {Object.entries(PRIVACY_REGIONS).map(([code, region]) => (\n                <SelectItem key={code} value={code}>\n                  <div className=\"flex items-center justify-between w-full\">\n                    <span>{region.name}</span>\n                    <div className=\"flex gap-1 ml-2\">\n                      {region.laws.map((law) => (\n                        <Badge key={law} variant=\"secondary\" className=\"text-xs\">\n                          {law}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Selected Region Info */}\n        {selectedRegion && (\n          <div className=\"bg-green-50 p-4 rounded-lg border border-green-200\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <Shield className=\"h-4 w-4 text-green-600\" />\n              <span className=\"font-medium text-green-900\">Privacy Protection</span>\n            </div>\n            <p className=\"text-sm text-green-700 mb-2\">\n              Your data will be protected under the following privacy laws:\n            </p>\n            <div className=\"flex flex-wrap gap-2\">\n              {PRIVACY_REGIONS[selectedRegion].laws.map((law) => (\n                <Badge key={law} variant=\"outline\" className=\"text-green-700 border-green-300\">\n                  {law}\n                </Badge>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Privacy Information */}\n        <div className=\"bg-gray-50 p-4 rounded-lg\">\n          <h4 className=\"font-medium mb-2\">What this means:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• Your personal data will be stored in data centers within your selected region</li>\n            <li>• We'll comply with the privacy laws applicable to your region</li>\n            <li>• You can change this preference later in your privacy settings</li>\n            <li>• Non-personal application data may be stored globally for performance</li>\n          </ul>\n        </div>\n\n        {/* Continue Button */}\n        <Button \n          onClick={onNext} \n          disabled={!selectedRegion}\n          className=\"w-full h-12 text-base\"\n        >\n          Continue to Privacy Preferences\n        </Button>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAkBO,SAAS,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,EAAuB;IAC5F,MAAM,CAAC,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,gCAAgC;IAExF,MAAM,qBAAqB,CAAC;QAC1B,eAAe;IACjB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGrB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;kCAChC,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAU;;;;;;;;;;;;0BAMvC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;;;;;;;0CAE9C,8OAAC;gCAAE,WAAU;;oCAAwB;kDACG,8OAAC;kDAAQ,mHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI;;;;;;;;;;;;;;;;;;kCAKvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAgB,WAAU;0CAAwB;;;;;;0CAGjE,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,kBAAkB;gCAAI,eAAe;;kDAClD,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,IAAG;wCAAgB,WAAU;kDAC1C,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;kDACX,OAAO,OAAO,CAAC,mHAAA,CAAA,kBAAe,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,iBAClD,8OAAC,kIAAA,CAAA,aAAU;gDAAY,OAAO;0DAC5B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,OAAO,IAAI;;;;;;sEAClB,8OAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,8OAAC,iIAAA,CAAA,QAAK;oEAAW,SAAQ;oEAAY,WAAU;8EAC5C;mEADS;;;;;;;;;;;;;;;;+CALH;;;;;;;;;;;;;;;;;;;;;;oBAkBxB,gCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;0CAE/C,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAG3C,8OAAC;gCAAI,WAAU;0CACZ,mHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,oBACzC,8OAAC,iIAAA,CAAA,QAAK;wCAAW,SAAQ;wCAAU,WAAU;kDAC1C;uCADS;;;;;;;;;;;;;;;;kCASpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;0CACjC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAKR,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/onboarding/consent-manager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Shield, Info, CheckCircle, XCircle } from 'lucide-react';\nimport { DATA_CATEGORIES, PRIVACY_REGIONS } from '@/lib/utils';\nimport type { PrivacyRegion, DataCategory } from '@/lib/utils';\n\ninterface ConsentItem {\n  dataCategory: DataCategory;\n  purpose: string;\n  description: string;\n  required: boolean;\n  legalBasis: string;\n}\n\ninterface ConsentManagerProps {\n  selectedRegion: PrivacyRegion;\n  consents: Record<string, boolean>;\n  onConsentChange: (consentKey: string, granted: boolean) => void;\n  onNext: () => void;\n  onBack: () => void;\n}\n\nexport function ConsentManager({ \n  selectedRegion, \n  consents, \n  onConsentChange, \n  onNext, \n  onBack \n}: ConsentManagerProps) {\n  const [showDetails, setShowDetails] = useState<Record<string, boolean>>({});\n\n  // Define consent items based on region\n  const consentItems: ConsentItem[] = [\n    {\n      dataCategory: 'PERSONAL_IDENTIFIERS',\n      purpose: 'Account Creation and Management',\n      description: 'We need your name and email to create and manage your account, provide customer support, and ensure account security.',\n      required: true,\n      legalBasis: 'Contract Performance'\n    },\n    {\n      dataCategory: 'CONTACT_INFO',\n      purpose: 'Service Communications',\n      description: 'We use your contact information to send important service updates, security notifications, and account-related communications.',\n      required: true,\n      legalBasis: 'Contract Performance'\n    },\n    {\n      dataCategory: 'BEHAVIORAL_DATA',\n      purpose: 'Service Improvement and Analytics',\n      description: 'We analyze how you use our service to improve functionality, fix bugs, and enhance user experience.',\n      required: false,\n      legalBasis: 'Legitimate Interest'\n    },\n    {\n      dataCategory: 'CONTACT_INFO',\n      purpose: 'Marketing Communications',\n      description: 'We would like to send you promotional emails, product updates, and personalized offers.',\n      required: false,\n      legalBasis: 'Consent'\n    },\n    {\n      dataCategory: 'BEHAVIORAL_DATA',\n      purpose: 'Personalization',\n      description: 'We use your usage patterns to personalize your experience and show relevant content.',\n      required: false,\n      legalBasis: 'Consent'\n    },\n    {\n      dataCategory: 'LOCATION_DATA',\n      purpose: 'Location-based Services',\n      description: 'We may use your approximate location to provide region-specific features and comply with local laws.',\n      required: false,\n      legalBasis: 'Consent'\n    }\n  ];\n\n  const toggleDetails = (key: string) => {\n    setShowDetails(prev => ({ ...prev, [key]: !prev[key] }));\n  };\n\n  const getConsentKey = (item: ConsentItem) => {\n    return `${item.dataCategory}_${item.purpose.replace(/\\s+/g, '_').toLowerCase()}`;\n  };\n\n  const requiredConsentsGranted = consentItems\n    .filter(item => item.required)\n    .every(item => consents[getConsentKey(item)] === true);\n\n  const canProceed = requiredConsentsGranted;\n\n  return (\n    <Card className=\"w-full max-w-4xl mx-auto\">\n      <CardHeader className=\"text-center\">\n        <div className=\"flex justify-center mb-4\">\n          <div className=\"p-3 bg-green-100 rounded-full\">\n            <Shield className=\"h-8 w-8 text-green-600\" />\n          </div>\n        </div>\n        <CardTitle className=\"text-2xl\">Privacy Preferences</CardTitle>\n        <CardDescription className=\"text-lg\">\n          Choose how we can use your data. You can change these preferences anytime.\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-6\">\n        {/* Region Info */}\n        <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\n          <div className=\"flex items-center gap-2 mb-2\">\n            <Info className=\"h-4 w-4 text-blue-600\" />\n            <span className=\"font-medium text-blue-900\">\n              Data Processing in {PRIVACY_REGIONS[selectedRegion].name}\n            </span>\n          </div>\n          <p className=\"text-sm text-blue-700\">\n            Your data will be processed according to{' '}\n            {PRIVACY_REGIONS[selectedRegion].laws.join(', ')} regulations.\n          </p>\n        </div>\n\n        {/* Consent Items */}\n        <div className=\"space-y-4\">\n          {consentItems.map((item) => {\n            const consentKey = getConsentKey(item);\n            const isGranted = consents[consentKey] || false;\n            const isDetailVisible = showDetails[consentKey];\n\n            return (\n              <div key={consentKey} className=\"border rounded-lg p-4\">\n                <div className=\"flex items-start justify-between gap-4\">\n                  <div className=\"flex-1 space-y-2\">\n                    <div className=\"flex items-center gap-2\">\n                      <h4 className=\"font-medium\">{item.purpose}</h4>\n                      {item.required && (\n                        <Badge variant=\"destructive\" className=\"text-xs\">\n                          Required\n                        </Badge>\n                      )}\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {DATA_CATEGORIES[item.dataCategory]}\n                      </Badge>\n                    </div>\n                    \n                    <p className=\"text-sm text-gray-600\">{item.description}</p>\n                    \n                    <div className=\"flex items-center gap-4 text-xs text-gray-500\">\n                      <span>Legal Basis: {item.legalBasis}</span>\n                      <Button\n                        variant=\"link\"\n                        size=\"sm\"\n                        className=\"h-auto p-0 text-xs\"\n                        onClick={() => toggleDetails(consentKey)}\n                      >\n                        {isDetailVisible ? 'Hide Details' : 'Show Details'}\n                      </Button>\n                    </div>\n\n                    {isDetailVisible && (\n                      <div className=\"mt-3 p-3 bg-gray-50 rounded text-xs space-y-2\">\n                        <div><strong>Data Category:</strong> {DATA_CATEGORIES[item.dataCategory]}</div>\n                        <div><strong>Retention Period:</strong> As per {PRIVACY_REGIONS[selectedRegion].name} regulations</div>\n                        <div><strong>Your Rights:</strong> Access, correction, deletion, portability (where applicable)</div>\n                        <div><strong>Third Parties:</strong> We do not sell your data to third parties</div>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center gap-2\">\n                    {item.required ? (\n                      <div className=\"flex items-center gap-2 text-green-600\">\n                        <CheckCircle className=\"h-4 w-4\" />\n                        <span className=\"text-sm font-medium\">Required</span>\n                      </div>\n                    ) : (\n                      <div className=\"flex items-center space-x-2\">\n                        <Switch\n                          id={consentKey}\n                          checked={isGranted}\n                          onCheckedChange={(checked) => onConsentChange(consentKey, checked)}\n                        />\n                        <Label htmlFor={consentKey} className=\"sr-only\">\n                          {item.purpose}\n                        </Label>\n                        {isGranted ? (\n                          <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                        ) : (\n                          <XCircle className=\"h-4 w-4 text-gray-400\" />\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Required Consents Warning */}\n        {!requiredConsentsGranted && (\n          <Alert>\n            <Info className=\"h-4 w-4\" />\n            <AlertDescription>\n              Some data processing is required for basic service functionality. \n              These cannot be disabled but you can delete your account at any time.\n            </AlertDescription>\n          </Alert>\n        )}\n\n        <Separator />\n\n        {/* Your Rights */}\n        <div className=\"bg-gray-50 p-4 rounded-lg\">\n          <h4 className=\"font-medium mb-2\">Your Privacy Rights</h4>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600\">\n            <div>• Right to access your data</div>\n            <div>• Right to correct inaccurate data</div>\n            <div>• Right to delete your data</div>\n            <div>• Right to data portability</div>\n            <div>• Right to withdraw consent</div>\n            <div>• Right to object to processing</div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex gap-4\">\n          <Button variant=\"outline\" onClick={onBack} className=\"flex-1\">\n            Back\n          </Button>\n          <Button \n            onClick={onNext} \n            disabled={!canProceed}\n            className=\"flex-1\"\n          >\n            Continue to Account Details\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAXA;;;;;;;;;;;;AA8BO,SAAS,eAAe,EAC7B,cAAc,EACd,QAAQ,EACR,eAAe,EACf,MAAM,EACN,MAAM,EACc;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAEzE,uCAAuC;IACvC,MAAM,eAA8B;QAClC;YACE,cAAc;YACd,SAAS;YACT,aAAa;YACb,UAAU;YACV,YAAY;QACd;QACA;YACE,cAAc;YACd,SAAS;YACT,aAAa;YACb,UAAU;YACV,YAAY;QACd;QACA;YACE,cAAc;YACd,SAAS;YACT,aAAa;YACb,UAAU;YACV,YAAY;QACd;QACA;YACE,cAAc;YACd,SAAS;YACT,aAAa;YACb,UAAU;YACV,YAAY;QACd;QACA;YACE,cAAc;YACd,SAAS;YACT,aAAa;YACb,UAAU;YACV,YAAY;QACd;QACA;YACE,cAAc;YACd,SAAS;YACT,aAAa;YACb,UAAU;YACV,YAAY;QACd;KACD;IAED,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI;YAAC,CAAC;IACxD;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,GAAG,KAAK,YAAY,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW,IAAI;IAClF;IAEA,MAAM,0BAA0B,aAC7B,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAC5B,KAAK,CAAC,CAAA,OAAQ,QAAQ,CAAC,cAAc,MAAM,KAAK;IAEnD,MAAM,aAAa;IAEnB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGtB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;kCAChC,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAU;;;;;;;;;;;;0BAKvC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;;4CAA4B;4CACtB,mHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI;;;;;;;;;;;;;0CAG5D,8OAAC;gCAAE,WAAU;;oCAAwB;oCACM;oCACxC,mHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;oCAAM;;;;;;;;;;;;;kCAKrD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC;4BACjB,MAAM,aAAa,cAAc;4BACjC,MAAM,YAAY,QAAQ,CAAC,WAAW,IAAI;4BAC1C,MAAM,kBAAkB,WAAW,CAAC,WAAW;4BAE/C,qBACE,8OAAC;gCAAqB,WAAU;0CAC9B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAe,KAAK,OAAO;;;;;;wDACxC,KAAK,QAAQ,kBACZ,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAc,WAAU;sEAAU;;;;;;sEAInD,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,mHAAA,CAAA,kBAAe,CAAC,KAAK,YAAY,CAAC;;;;;;;;;;;;8DAIvC,8OAAC;oDAAE,WAAU;8DAAyB,KAAK,WAAW;;;;;;8DAEtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAK;gEAAc,KAAK,UAAU;;;;;;;sEACnC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,cAAc;sEAE5B,kBAAkB,iBAAiB;;;;;;;;;;;;gDAIvC,iCACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAAuB;gEAAE,mHAAA,CAAA,kBAAe,CAAC,KAAK,YAAY,CAAC;;;;;;;sEACxE,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAA0B;gEAAS,mHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI;gEAAC;;;;;;;sEACrF,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAAqB;;;;;;;sEAClC,8OAAC;;8EAAI,8OAAC;8EAAO;;;;;;gEAAuB;;;;;;;;;;;;;;;;;;;sDAK1C,8OAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;qEAGxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,IAAI;wDACJ,SAAS;wDACT,iBAAiB,CAAC,UAAY,gBAAgB,YAAY;;;;;;kEAE5D,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS;wDAAY,WAAU;kEACnC,KAAK,OAAO;;;;;;oDAEd,0BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,4MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA1DrB;;;;;wBAkEd;;;;;;oBAID,CAAC,yCACA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAOtB,8OAAC,qIAAA,CAAA,YAAS;;;;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmB;;;;;;0CACjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAQ,WAAU;0CAAS;;;;;;0CAG9D,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1700, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1923, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/onboarding/account-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { User, Mail, Shield, AlertCircle } from 'lucide-react';\nimport type { PrivacyRegion } from '@/lib/utils';\n\nconst accountFormSchema = z.object({\n  firstName: z.string().min(2, 'First name must be at least 2 characters'),\n  lastName: z.string().min(2, 'Last name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  dataResidencyPreference: z.enum(['SAME_REGION', 'GLOBAL', 'CUSTOM']),\n  marketingEmails: z.boolean().default(false),\n  analyticsOptIn: z.boolean().default(false),\n  acceptTerms: z.boolean().refine(val => val === true, {\n    message: 'You must accept the terms and conditions'\n  }),\n  acceptPrivacyPolicy: z.boolean().refine(val => val === true, {\n    message: 'You must accept the privacy policy'\n  }),\n  ageVerification: z.boolean().refine(val => val === true, {\n    message: 'You must confirm you are of legal age'\n  }),\n});\n\ntype AccountFormData = z.infer<typeof accountFormSchema>;\n\ninterface AccountFormProps {\n  selectedRegion: PrivacyRegion;\n  onSubmit: (data: AccountFormData) => void;\n  onBack: () => void;\n  isLoading?: boolean;\n}\n\nexport function AccountForm({ selectedRegion, onSubmit, onBack, isLoading = false }: AccountFormProps) {\n  const [showDataResidencyInfo, setShowDataResidencyInfo] = useState(false);\n\n  const form = useForm<AccountFormData>({\n    resolver: zodResolver(accountFormSchema),\n    defaultValues: {\n      firstName: '',\n      lastName: '',\n      email: '',\n      dataResidencyPreference: 'SAME_REGION',\n      marketingEmails: false,\n      analyticsOptIn: false,\n      acceptTerms: false,\n      acceptPrivacyPolicy: false,\n      ageVerification: false,\n    },\n  });\n\n  const handleSubmit = (data: AccountFormData) => {\n    onSubmit(data);\n  };\n\n  const dataResidencyOptions = [\n    {\n      value: 'SAME_REGION',\n      label: 'Same Region Only',\n      description: `Store all data in ${selectedRegion} region only`\n    },\n    {\n      value: 'GLOBAL',\n      label: 'Global (Optimized)',\n      description: 'Allow global storage for better performance (with privacy protections)'\n    },\n    {\n      value: 'CUSTOM',\n      label: 'Custom Configuration',\n      description: 'Configure specific data storage preferences'\n    }\n  ];\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader className=\"text-center\">\n        <div className=\"flex justify-center mb-4\">\n          <div className=\"p-3 bg-purple-100 rounded-full\">\n            <User className=\"h-8 w-8 text-purple-600\" />\n          </div>\n        </div>\n        <CardTitle className=\"text-2xl\">Create Your Account</CardTitle>\n        <CardDescription className=\"text-lg\">\n          Complete your privacy-first account setup\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent>\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-6\">\n            {/* Personal Information */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-medium\">Personal Information</h3>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <FormField\n                  control={form.control}\n                  name=\"firstName\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>First Name</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"Enter your first name\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n                \n                <FormField\n                  control={form.control}\n                  name=\"lastName\"\n                  render={({ field }) => (\n                    <FormItem>\n                      <FormLabel>Last Name</FormLabel>\n                      <FormControl>\n                        <Input placeholder=\"Enter your last name\" {...field} />\n                      </FormControl>\n                      <FormMessage />\n                    </FormItem>\n                  )}\n                />\n              </div>\n\n              <FormField\n                control={form.control}\n                name=\"email\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Email Address</FormLabel>\n                    <FormControl>\n                      <div className=\"relative\">\n                        <Mail className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                        <Input \n                          type=\"email\" \n                          placeholder=\"Enter your email address\" \n                          className=\"pl-10\"\n                          {...field} \n                        />\n                      </div>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Data Residency Preferences */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-2\">\n                <h3 className=\"text-lg font-medium\">Data Storage Preferences</h3>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setShowDataResidencyInfo(!showDataResidencyInfo)}\n                >\n                  <Shield className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              {showDataResidencyInfo && (\n                <Alert>\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <AlertDescription>\n                    Data residency determines where your personal information is physically stored. \n                    This affects which privacy laws apply and data transfer restrictions.\n                  </AlertDescription>\n                </Alert>\n              )}\n\n              <FormField\n                control={form.control}\n                name=\"dataResidencyPreference\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Data Storage Preference</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"Select data storage preference\" />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        {dataResidencyOptions.map((option) => (\n                          <SelectItem key={option.value} value={option.value}>\n                            <div>\n                              <div className=\"font-medium\">{option.label}</div>\n                              <div className=\"text-sm text-gray-500\">{option.description}</div>\n                            </div>\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Communication Preferences */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-medium\">Communication Preferences</h3>\n              \n              <FormField\n                control={form.control}\n                name=\"marketingEmails\"\n                render={({ field }) => (\n                  <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\n                    <FormControl>\n                      <Checkbox\n                        checked={field.value}\n                        onCheckedChange={field.onChange}\n                      />\n                    </FormControl>\n                    <div className=\"space-y-1 leading-none\">\n                      <FormLabel>\n                        Marketing Communications\n                      </FormLabel>\n                      <FormDescription>\n                        Receive promotional emails, product updates, and special offers\n                      </FormDescription>\n                    </div>\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"analyticsOptIn\"\n                render={({ field }) => (\n                  <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\n                    <FormControl>\n                      <Checkbox\n                        checked={field.value}\n                        onCheckedChange={field.onChange}\n                      />\n                    </FormControl>\n                    <div className=\"space-y-1 leading-none\">\n                      <FormLabel>\n                        Usage Analytics\n                      </FormLabel>\n                      <FormDescription>\n                        Help us improve our service by sharing anonymous usage data\n                      </FormDescription>\n                    </div>\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Legal Agreements */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-medium\">Legal Agreements</h3>\n              \n              <FormField\n                control={form.control}\n                name=\"acceptTerms\"\n                render={({ field }) => (\n                  <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\n                    <FormControl>\n                      <Checkbox\n                        checked={field.value}\n                        onCheckedChange={field.onChange}\n                      />\n                    </FormControl>\n                    <div className=\"space-y-1 leading-none\">\n                      <FormLabel>\n                        I accept the <a href=\"/terms\" className=\"text-blue-600 hover:underline\">Terms and Conditions</a>\n                      </FormLabel>\n                      <FormMessage />\n                    </div>\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"acceptPrivacyPolicy\"\n                render={({ field }) => (\n                  <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\n                    <FormControl>\n                      <Checkbox\n                        checked={field.value}\n                        onCheckedChange={field.onChange}\n                      />\n                    </FormControl>\n                    <div className=\"space-y-1 leading-none\">\n                      <FormLabel>\n                        I accept the <a href=\"/privacy\" className=\"text-blue-600 hover:underline\">Privacy Policy</a>\n                      </FormLabel>\n                      <FormMessage />\n                    </div>\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"ageVerification\"\n                render={({ field }) => (\n                  <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\n                    <FormControl>\n                      <Checkbox\n                        checked={field.value}\n                        onCheckedChange={field.onChange}\n                      />\n                    </FormControl>\n                    <div className=\"space-y-1 leading-none\">\n                      <FormLabel>\n                        I confirm that I am at least 16 years old (or the minimum age in my jurisdiction)\n                      </FormLabel>\n                      <FormMessage />\n                    </div>\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Navigation */}\n            <div className=\"flex gap-4 pt-4\">\n              <Button type=\"button\" variant=\"outline\" onClick={onBack} className=\"flex-1\">\n                Back\n              </Button>\n              <Button type=\"submit\" disabled={isLoading} className=\"flex-1\">\n                {isLoading ? 'Creating Account...' : 'Create Account'}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;;;;;AAiBA,MAAM,oBAAoB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,yBAAyB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAU;KAAS;IACnE,iBAAiB,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACrC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACpC,aAAa,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA,MAAO,QAAQ,MAAM;QACnD,SAAS;IACX;IACA,qBAAqB,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA,MAAO,QAAQ,MAAM;QAC3D,SAAS;IACX;IACA,iBAAiB,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA,MAAO,QAAQ,MAAM;QACvD,SAAS;IACX;AACF;AAWO,SAAS,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,KAAK,EAAoB;IACnG,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW;YACX,UAAU;YACV,OAAO;YACP,yBAAyB;YACzB,iBAAiB;YACjB,gBAAgB;YAChB,aAAa;YACb,qBAAqB;YACrB,iBAAiB;QACnB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,SAAS;IACX;IAEA,MAAM,uBAAuB;QAC3B;YACE,OAAO;YACP,OAAO;YACP,aAAa,CAAC,kBAAkB,EAAE,eAAe,YAAY,CAAC;QAChE;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;kCAChC,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAU;;;;;;;;;;;;0BAKvC,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAe,WAAU;;0CAEzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAyB,GAAG,KAAK;;;;;;;;;;;0EAEtD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAwB,GAAG,KAAK;;;;;;;;;;;0EAErD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,aAAY;oEACZ,WAAU;oEACT,GAAG,KAAK;;;;;;;;;;;;;;;;;kEAIf,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAOpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,yBAAyB,CAAC;0DAEzC,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAIrB,uCACC,8OAAC,iIAAA,CAAA,QAAK;;0DACJ,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC,iIAAA,CAAA,mBAAgB;0DAAC;;;;;;;;;;;;kDAOtB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,kIAAA,CAAA,SAAM;wDAAC,eAAe,MAAM,QAAQ;wDAAE,cAAc,MAAM,KAAK;;0EAC9D,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;;;;;;0EAG7B,8OAAC,kIAAA,CAAA,gBAAa;0EACX,qBAAqB,GAAG,CAAC,CAAC,uBACzB,8OAAC,kIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAChD,cAAA,8OAAC;;8FACC,8OAAC;oFAAI,WAAU;8FAAe,OAAO,KAAK;;;;;;8FAC1C,8OAAC;oFAAI,WAAU;8FAAyB,OAAO,WAAW;;;;;;;;;;;;uEAH7C,OAAO,KAAK;;;;;;;;;;;;;;;;kEASnC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAOpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,SAAS,MAAM,KAAK;4DACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EAGX,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;;;;;;;;;;;;kDAQzB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,SAAS,MAAM,KAAK;4DACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EAGX,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,SAAS,MAAM,KAAK;4DACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,YAAS;;oEAAC;kFACI,8OAAC;wEAAE,MAAK;wEAAS,WAAU;kFAAgC;;;;;;;;;;;;0EAE1E,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,SAAS,MAAM,KAAK;4DACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,YAAS;;oEAAC;kFACI,8OAAC;wEAAE,MAAK;wEAAW,WAAU;kFAAgC;;;;;;;;;;;;0EAE5E,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,SAAS,MAAM,KAAK;4DACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;kEAGnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EAGX,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;wCAAQ,WAAU;kDAAS;;;;;;kDAG5E,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;wCAAW,WAAU;kDAClD,YAAY,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 2778, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/privacy-service.ts"], "sourcesContent": ["import { \n  PrivacyConsent, \n  UserPrivacyProfile, \n  TenantConfiguration,\n  OnboardingFormData,\n  DataProcessingActivity \n} from './privacy-types';\nimport { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from './utils';\n\n// Mock data storage - In production, this would connect to your database\nclass PrivacyService {\n  private consents: Map<string, PrivacyConsent[]> = new Map();\n  private userProfiles: Map<string, UserPrivacyProfile> = new Map();\n  private tenantConfigs: Map<string, TenantConfiguration> = new Map();\n  private processingActivities: DataProcessingActivity[] = [];\n\n  constructor() {\n    this.initializeDefaultData();\n  }\n\n  private initializeDefaultData() {\n    // Initialize default tenant configuration\n    const defaultTenant: TenantConfiguration = {\n      tenantId: 'default',\n      name: 'Privacy First Application',\n      regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n      defaultRegion: 'US',\n      dataRetentionPolicies: {\n        PERSONAL_IDENTIFIERS: 2555, // 7 years\n        CONTACT_INFO: 1095, // 3 years\n        FINANCIAL_INFO: 2555, // 7 years\n        BIOMETRIC_DATA: 365, // 1 year\n        LOCATION_DATA: 365, // 1 year\n        BEHAVIORAL_DATA: 730, // 2 years\n        SENSITIVE_PERSONAL: 365, // 1 year\n        HEALTH_DATA: 2555, // 7 years\n        EMPLOYMENT_DATA: 2555, // 7 years\n        EDUCATION_DATA: 2555, // 7 years\n      },\n      complianceSettings: {\n        requireExplicitConsent: true,\n        enableRightToBeForgotten: true,\n        enableDataPortability: true,\n        enableOptOut: true,\n        cookieConsentRequired: true,\n        minorProtection: true,\n      }\n    };\n\n    this.tenantConfigs.set('default', defaultTenant);\n\n    // Initialize default processing activities\n    this.processingActivities = [\n      {\n        id: 'user-account',\n        name: 'User Account Management',\n        description: 'Creating and managing user accounts',\n        dataCategories: ['PERSONAL_IDENTIFIERS', 'CONTACT_INFO'],\n        purposes: ['Account Creation', 'Authentication', 'User Support'],\n        legalBasis: 'Contract Performance',\n        retentionPeriod: 2555, // 7 years\n        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n      },\n      {\n        id: 'marketing',\n        name: 'Marketing Communications',\n        description: 'Sending promotional emails and personalized offers',\n        dataCategories: ['CONTACT_INFO', 'BEHAVIORAL_DATA'],\n        purposes: ['Marketing', 'Personalization'],\n        legalBasis: 'Consent',\n        retentionPeriod: 1095, // 3 years\n        regions: ['US', 'EU', 'UK', 'CA'],\n      },\n      {\n        id: 'analytics',\n        name: 'Usage Analytics',\n        description: 'Analyzing user behavior to improve our services',\n        dataCategories: ['BEHAVIORAL_DATA', 'LOCATION_DATA'],\n        purposes: ['Analytics', 'Service Improvement'],\n        legalBasis: 'Legitimate Interest',\n        retentionPeriod: 730, // 2 years\n        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n      }\n    ];\n  }\n\n  // User Registration and Onboarding\n  async registerUser(formData: OnboardingFormData): Promise<{ userId: string; success: boolean }> {\n    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    \n    // Create user privacy profile\n    const userProfile: UserPrivacyProfile = {\n      userId,\n      preferredRegion: formData.preferredRegion,\n      dataResidencyPreference: formData.dataResidencyPreference,\n      consentPreferences: formData.consents.map(consent => ({\n        id: `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        userId,\n        dataCategory: consent.dataCategory,\n        purpose: consent.purpose,\n        granted: consent.granted,\n        timestamp: new Date(),\n        region: consent.region,\n        version: '1.0'\n      })),\n      communicationPreferences: formData.communicationPreferences,\n      rightsExercised: []\n    };\n\n    this.userProfiles.set(userId, userProfile);\n    this.consents.set(userId, userProfile.consentPreferences);\n\n    return { userId, success: true };\n  }\n\n  // Consent Management\n  async getConsents(userId: string): Promise<PrivacyConsent[]> {\n    return this.consents.get(userId) || [];\n  }\n\n  async updateConsent(userId: string, consentId: string, granted: boolean): Promise<boolean> {\n    const userConsents = this.consents.get(userId);\n    if (!userConsents) return false;\n\n    const consent = userConsents.find(c => c.id === consentId);\n    if (!consent) return false;\n\n    consent.granted = granted;\n    consent.timestamp = new Date();\n    \n    return true;\n  }\n\n  async grantConsent(userId: string, dataCategory: keyof typeof DATA_CATEGORIES, purpose: string, region: keyof typeof PRIVACY_REGIONS): Promise<string> {\n    const consentId = `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    const consent: PrivacyConsent = {\n      id: consentId,\n      userId,\n      dataCategory,\n      purpose,\n      granted: true,\n      timestamp: new Date(),\n      region,\n      version: '1.0'\n    };\n\n    const userConsents = this.consents.get(userId) || [];\n    userConsents.push(consent);\n    this.consents.set(userId, userConsents);\n\n    return consentId;\n  }\n\n  // User Rights Management\n  async exerciseUserRight(userId: string, right: keyof typeof USER_RIGHTS): Promise<string> {\n    const userProfile = this.userProfiles.get(userId);\n    if (!userProfile) throw new Error('User not found');\n\n    const requestId = `request_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    \n    userProfile.rightsExercised.push({\n      right,\n      requestedAt: new Date(),\n      status: 'PENDING'\n    });\n\n    this.userProfiles.set(userId, userProfile);\n    \n    return requestId;\n  }\n\n  // Data Export (Right to Portability)\n  async exportUserData(userId: string): Promise<any> {\n    const userProfile = this.userProfiles.get(userId);\n    const userConsents = this.consents.get(userId);\n    \n    if (!userProfile) throw new Error('User not found');\n\n    return {\n      profile: userProfile,\n      consents: userConsents,\n      exportedAt: new Date().toISOString(),\n      format: 'JSON'\n    };\n  }\n\n  // Data Deletion (Right to be Forgotten)\n  async deleteUserData(userId: string): Promise<boolean> {\n    const deleted = this.userProfiles.delete(userId) && this.consents.delete(userId);\n    return deleted;\n  }\n\n  // Region and Compliance Utilities\n  async getUserRegion(userId: string): Promise<keyof typeof PRIVACY_REGIONS | null> {\n    const userProfile = this.userProfiles.get(userId);\n    return userProfile?.preferredRegion || null;\n  }\n\n  async getApplicableProcessingActivities(region: keyof typeof PRIVACY_REGIONS): Promise<DataProcessingActivity[]> {\n    return this.processingActivities.filter(activity => \n      activity.regions.includes(region)\n    );\n  }\n\n  // Geolocation-based region detection (mock implementation)\n  async detectRegionFromIP(ipAddress: string): Promise<keyof typeof PRIVACY_REGIONS> {\n    // In production, use a geolocation service\n    // For demo purposes, return US as default\n    return 'US';\n  }\n\n  // Tenant Configuration\n  async getTenantConfig(tenantId: string = 'default'): Promise<TenantConfiguration | null> {\n    return this.tenantConfigs.get(tenantId) || null;\n  }\n\n  // Privacy Dashboard Data\n  async getPrivacyDashboardData(userId: string): Promise<{\n    profile: UserPrivacyProfile | null;\n    consents: PrivacyConsent[];\n    applicableLaws: string[];\n    dataRetentionInfo: any;\n  }> {\n    let profile = this.userProfiles.get(userId);\n    let consents = this.consents.get(userId) || [];\n\n    // If no profile exists and this looks like a demo user ID, create a demo profile\n    if (!profile && userId.startsWith('user_')) {\n      console.log('Creating demo profile for user:', userId);\n\n      // Create a demo profile\n      profile = {\n        userId: userId,\n        preferredRegion: 'US',\n        dataResidencyPreference: 'SAME_REGION',\n        consentPreferences: [\n          {\n            id: `consent_${Date.now()}_demo1`,\n            userId: userId,\n            dataCategory: 'PERSONAL_IDENTIFIERS',\n            purpose: 'Account Creation and Management',\n            granted: true,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          },\n          {\n            id: `consent_${Date.now()}_demo2`,\n            userId: userId,\n            dataCategory: 'CONTACT_INFO',\n            purpose: 'Service Communications',\n            granted: true,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          },\n          {\n            id: `consent_${Date.now()}_demo3`,\n            userId: userId,\n            dataCategory: 'BEHAVIORAL_DATA',\n            purpose: 'Service Improvement and Analytics',\n            granted: true,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          },\n          {\n            id: `consent_${Date.now()}_demo4`,\n            userId: userId,\n            dataCategory: 'CONTACT_INFO',\n            purpose: 'Marketing Communications',\n            granted: false,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          }\n        ],\n        communicationPreferences: {\n          marketing: false,\n          analytics: true,\n          personalization: false,\n          thirdPartySharing: false,\n        },\n        rightsExercised: []\n      };\n\n      // Store the demo profile\n      this.userProfiles.set(userId, profile);\n      this.consents.set(userId, profile.consentPreferences);\n      consents = profile.consentPreferences;\n    }\n\n    if (!profile) {\n      return {\n        profile: null,\n        consents: [],\n        applicableLaws: [],\n        dataRetentionInfo: {}\n      };\n    }\n\n    const applicableLaws = PRIVACY_REGIONS[profile.preferredRegion].laws;\n    const dataRetentionInfo = this.getDataRetentionInfo(profile.preferredRegion);\n\n    return {\n      profile,\n      consents,\n      applicableLaws,\n      dataRetentionInfo\n    };\n  }\n\n  private getDataRetentionInfo(region: keyof typeof PRIVACY_REGIONS): any {\n    const tenantConfig = this.tenantConfigs.get('default');\n    if (!tenantConfig) return {};\n\n    return Object.entries(tenantConfig.dataRetentionPolicies).map(([category, days]) => ({\n      category: DATA_CATEGORIES[category as keyof typeof DATA_CATEGORIES],\n      retentionPeriod: days,\n      retentionPeriodHuman: `${Math.floor((days || 0) / 365)} years, ${(days || 0) % 365} days`\n    }));\n  }\n}\n\n// Export singleton instance\nexport const privacyService = new PrivacyService();\n"], "names": [], "mappings": ";;;AAOA;;AAEA,yEAAyE;AACzE,MAAM;IACI,WAA0C,IAAI,MAAM;IACpD,eAAgD,IAAI,MAAM;IAC1D,gBAAkD,IAAI,MAAM;IAC5D,uBAAiD,EAAE,CAAC;IAE5D,aAAc;QACZ,IAAI,CAAC,qBAAqB;IAC5B;IAEQ,wBAAwB;QAC9B,0CAA0C;QAC1C,MAAM,gBAAqC;YACzC,UAAU;YACV,MAAM;YACN,SAAS;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YAC/D,eAAe;YACf,uBAAuB;gBACrB,sBAAsB;gBACtB,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB;gBAChB,eAAe;gBACf,iBAAiB;gBACjB,oBAAoB;gBACpB,aAAa;gBACb,iBAAiB;gBACjB,gBAAgB;YAClB;YACA,oBAAoB;gBAClB,wBAAwB;gBACxB,0BAA0B;gBAC1B,uBAAuB;gBACvB,cAAc;gBACd,uBAAuB;gBACvB,iBAAiB;YACnB;QACF;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;QAElC,2CAA2C;QAC3C,IAAI,CAAC,oBAAoB,GAAG;YAC1B;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAwB;iBAAe;gBACxD,UAAU;oBAAC;oBAAoB;oBAAkB;iBAAe;gBAChE,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;YACjE;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAgB;iBAAkB;gBACnD,UAAU;oBAAC;oBAAa;iBAAkB;gBAC1C,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YACnC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAmB;iBAAgB;gBACpD,UAAU;oBAAC;oBAAa;iBAAsB;gBAC9C,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;YACjE;SACD;IACH;IAEA,mCAAmC;IACnC,MAAM,aAAa,QAA4B,EAAiD;QAC9F,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAE9E,8BAA8B;QAC9B,MAAM,cAAkC;YACtC;YACA,iBAAiB,SAAS,eAAe;YACzC,yBAAyB,SAAS,uBAAuB;YACzD,oBAAoB,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBACpD,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACtE;oBACA,cAAc,QAAQ,YAAY;oBAClC,SAAS,QAAQ,OAAO;oBACxB,SAAS,QAAQ,OAAO;oBACxB,WAAW,IAAI;oBACf,QAAQ,QAAQ,MAAM;oBACtB,SAAS;gBACX,CAAC;YACD,0BAA0B,SAAS,wBAAwB;YAC3D,iBAAiB,EAAE;QACrB;QAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ;QAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,YAAY,kBAAkB;QAExD,OAAO;YAAE;YAAQ,SAAS;QAAK;IACjC;IAEA,qBAAqB;IACrB,MAAM,YAAY,MAAc,EAA6B;QAC3D,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;IACxC;IAEA,MAAM,cAAc,MAAc,EAAE,SAAiB,EAAE,OAAgB,EAAoB;QACzF,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,OAAO;QAE1B,MAAM,UAAU,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,CAAC,SAAS,OAAO;QAErB,QAAQ,OAAO,GAAG;QAClB,QAAQ,SAAS,GAAG,IAAI;QAExB,OAAO;IACT;IAEA,MAAM,aAAa,MAAc,EAAE,YAA0C,EAAE,OAAe,EAAE,MAAoC,EAAmB;QACrJ,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QACpF,MAAM,UAA0B;YAC9B,IAAI;YACJ;YACA;YACA;YACA,SAAS;YACT,WAAW,IAAI;YACf;YACA,SAAS;QACX;QAEA,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;QACpD,aAAa,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ;QAE1B,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,kBAAkB,MAAc,EAAE,KAA+B,EAAmB;QACxF,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1C,IAAI,CAAC,aAAa,MAAM,IAAI,MAAM;QAElC,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAEpF,YAAY,eAAe,CAAC,IAAI,CAAC;YAC/B;YACA,aAAa,IAAI;YACjB,QAAQ;QACV;QAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ;QAE9B,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,eAAe,MAAc,EAAgB;QACjD,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1C,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,aAAa,MAAM,IAAI,MAAM;QAElC,OAAO;YACL,SAAS;YACT,UAAU;YACV,YAAY,IAAI,OAAO,WAAW;YAClC,QAAQ;QACV;IACF;IAEA,wCAAwC;IACxC,MAAM,eAAe,MAAc,EAAoB;QACrD,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACzE,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,cAAc,MAAc,EAAgD;QAChF,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1C,OAAO,aAAa,mBAAmB;IACzC;IAEA,MAAM,kCAAkC,MAAoC,EAAqC;QAC/G,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA,WACtC,SAAS,OAAO,CAAC,QAAQ,CAAC;IAE9B;IAEA,2DAA2D;IAC3D,MAAM,mBAAmB,SAAiB,EAAyC;QACjF,2CAA2C;QAC3C,0CAA0C;QAC1C,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,WAAmB,SAAS,EAAuC;QACvF,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa;IAC7C;IAEA,yBAAyB;IACzB,MAAM,wBAAwB,MAAc,EAKzC;QACD,IAAI,UAAU,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACpC,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;QAE9C,iFAAiF;QACjF,IAAI,CAAC,WAAW,OAAO,UAAU,CAAC,UAAU;YAC1C,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,wBAAwB;YACxB,UAAU;gBACR,QAAQ;gBACR,iBAAiB;gBACjB,yBAAyB;gBACzB,oBAAoB;oBAClB;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;oBACA;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;oBACA;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;oBACA;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;iBACD;gBACD,0BAA0B;oBACxB,WAAW;oBACX,WAAW;oBACX,iBAAiB;oBACjB,mBAAmB;gBACrB;gBACA,iBAAiB,EAAE;YACrB;YAEA,yBAAyB;YACzB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,QAAQ,kBAAkB;YACpD,WAAW,QAAQ,kBAAkB;QACvC;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,SAAS;gBACT,UAAU,EAAE;gBACZ,gBAAgB,EAAE;gBAClB,mBAAmB,CAAC;YACtB;QACF;QAEA,MAAM,iBAAiB,mHAAA,CAAA,kBAAe,CAAC,QAAQ,eAAe,CAAC,CAAC,IAAI;QACpE,MAAM,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,eAAe;QAE3E,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEQ,qBAAqB,MAAoC,EAAO;QACtE,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAC5C,IAAI,CAAC,cAAc,OAAO,CAAC;QAE3B,OAAO,OAAO,OAAO,CAAC,aAAa,qBAAqB,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,KAAK,GAAK,CAAC;gBACnF,UAAU,mHAAA,CAAA,kBAAe,CAAC,SAAyC;gBACnE,iBAAiB;gBACjB,sBAAsB,GAAG,KAAK,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC;YAC3F,CAAC;IACH;AACF;AAGO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 3116, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/onboarding/onboarding-success.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { CheckCircle, Shield, Download, Settings, ArrowRight } from 'lucide-react';\nimport { privacyService } from '@/lib/privacy-service';\nimport type { UserPrivacyProfile, PrivacyConsent } from '@/lib/privacy-types';\n\ninterface OnboardingSuccessProps {\n  userId: string;\n}\n\nexport function OnboardingSuccess({ userId }: OnboardingSuccessProps) {\n  const [userProfile, setUserProfile] = useState<UserPrivacyProfile | null>(null);\n  const [consents, setConsents] = useState<PrivacyConsent[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const loadUserData = async () => {\n      try {\n        const dashboardData = await privacyService.getPrivacyDashboardData(userId);\n        setUserProfile(dashboardData.profile);\n        setConsents(dashboardData.consents);\n      } catch (error) {\n        console.error('Failed to load user data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadUserData();\n  }, [userId]);\n\n  const handleDownloadData = async () => {\n    try {\n      const userData = await privacyService.exportUserData(userId);\n      const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `privacy-data-${userId}.json`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Failed to download data:', error);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Card className=\"w-full max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading your privacy profile...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"w-full max-w-4xl mx-auto\">\n      <CardHeader className=\"text-center\">\n        <div className=\"flex justify-center mb-4\">\n          <div className=\"p-4 bg-green-100 rounded-full\">\n            <CheckCircle className=\"h-12 w-12 text-green-600\" />\n          </div>\n        </div>\n        <CardTitle className=\"text-3xl text-green-600\">Welcome!</CardTitle>\n        <CardDescription className=\"text-lg\">\n          Your privacy-first account has been created successfully\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"space-y-8\">\n        {/* Account Summary */}\n        {userProfile && (\n          <div className=\"bg-green-50 p-6 rounded-lg border border-green-200\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              <Shield className=\"h-5 w-5 text-green-600\" />\n              <h3 className=\"text-lg font-semibold text-green-900\">Your Privacy Profile</h3>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"font-medium text-green-800\">User ID:</span>\n                <span className=\"ml-2 text-green-700 font-mono\">{userId}</span>\n              </div>\n              <div>\n                <span className=\"font-medium text-green-800\">Data Region:</span>\n                <span className=\"ml-2 text-green-700\">{userProfile.preferredRegion}</span>\n              </div>\n              <div>\n                <span className=\"font-medium text-green-800\">Data Residency:</span>\n                <span className=\"ml-2 text-green-700\">\n                  {userProfile.dataResidencyPreference.replace('_', ' ').toLowerCase()}\n                </span>\n              </div>\n              <div>\n                <span className=\"font-medium text-green-800\">Active Consents:</span>\n                <span className=\"ml-2 text-green-700\">\n                  {consents.filter(c => c.granted).length} granted\n                </span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Consent Summary */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">Your Privacy Choices</h3>\n          <div className=\"grid gap-3\">\n            {consents.map((consent) => (\n              <div key={consent.id} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div className=\"flex-1\">\n                  <div className=\"font-medium\">{consent.purpose}</div>\n                  <div className=\"text-sm text-gray-600\">{consent.dataCategory.replace('_', ' ')}</div>\n                </div>\n                <Badge variant={consent.granted ? \"default\" : \"secondary\"}>\n                  {consent.granted ? 'Granted' : 'Declined'}\n                </Badge>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <Separator />\n\n        {/* Next Steps */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">What's Next?</h3>\n          <div className=\"grid gap-4\">\n            <div className=\"flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50\">\n              <Settings className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n              <div className=\"flex-1\">\n                <h4 className=\"font-medium\">Manage Your Privacy Settings</h4>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  Update your consent preferences, data retention settings, and privacy controls anytime.\n                </p>\n              </div>\n              <ArrowRight className=\"h-4 w-4 text-gray-400\" />\n            </div>\n\n            <div className=\"flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50\">\n              <Download className=\"h-5 w-5 text-purple-600 mt-0.5\" />\n              <div className=\"flex-1\">\n                <h4 className=\"font-medium\">Download Your Data</h4>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  Exercise your right to data portability by downloading all your personal data.\n                </p>\n              </div>\n              <ArrowRight className=\"h-4 w-4 text-gray-400\" />\n            </div>\n\n            <div className=\"flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50\">\n              <Shield className=\"h-5 w-5 text-green-600 mt-0.5\" />\n              <div className=\"flex-1\">\n                <h4 className=\"font-medium\">Privacy Dashboard</h4>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  Monitor how your data is being used and exercise your privacy rights.\n                </p>\n              </div>\n              <ArrowRight className=\"h-4 w-4 text-gray-400\" />\n            </div>\n          </div>\n        </div>\n\n        {/* Your Rights Reminder */}\n        <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\n          <h4 className=\"font-medium text-blue-900 mb-2\">Remember Your Rights</h4>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-blue-700\">\n            <div>✓ Access your personal data</div>\n            <div>✓ Correct inaccurate information</div>\n            <div>✓ Delete your account and data</div>\n            <div>✓ Export your data</div>\n            <div>✓ Withdraw consent anytime</div>\n            <div>✓ Object to data processing</div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <Button onClick={handleDownloadData} variant=\"outline\" className=\"flex-1\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Download My Data\n          </Button>\n          <Button \n            onClick={() => window.location.href = '/dashboard'} \n            className=\"flex-1\"\n          >\n            <Settings className=\"h-4 w-4 mr-2\" />\n            Go to Privacy Dashboard\n          </Button>\n        </div>\n\n        {/* Support Information */}\n        <div className=\"text-center text-sm text-gray-500 pt-4 border-t\">\n          <p>\n            Questions about your privacy? Contact our Data Protection Officer at{' '}\n            <a href=\"mailto:<EMAIL>\" className=\"text-blue-600 hover:underline\">\n              <EMAIL>\n            </a>\n          </p>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAeO,SAAS,kBAAkB,EAAE,MAAM,EAA0B;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,gBAAgB,MAAM,gIAAA,CAAA,iBAAc,CAAC,uBAAuB,CAAC;gBACnE,eAAe,cAAc,OAAO;gBACpC,YAAY,cAAc,QAAQ;YACpC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,gIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;YACrD,MAAM,OAAO,IAAI,KAAK;gBAAC,KAAK,SAAS,CAAC,UAAU,MAAM;aAAG,EAAE;gBAAE,MAAM;YAAmB;YACtF,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,aAAa,EAAE,OAAO,KAAK,CAAC;YAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAG3B,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAA0B;;;;;;kCAC/C,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCAAU;;;;;;;;;;;;0BAKvC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;kDAEnD,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;0DAAuB,YAAY,eAAe;;;;;;;;;;;;kDAEpE,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;0DACb,YAAY,uBAAuB,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;;kDAGtE,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;;oDACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAe,QAAQ,OAAO;;;;;;kEAC7C,8OAAC;wDAAI,WAAU;kEAAyB,QAAQ,YAAY,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAE5E,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,QAAQ,OAAO,GAAG,YAAY;0DAC3C,QAAQ,OAAO,GAAG,YAAY;;;;;;;uCANzB,QAAQ,EAAE;;;;;;;;;;;;;;;;kCAa1B,8OAAC,qIAAA,CAAA,YAAS;;;;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAI5C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAI5C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAGxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAI5C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;kDACL,8OAAC;kDAAI;;;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAoB,SAAQ;gCAAU,WAAU;;kDAC/D,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;gCACtC,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;gCAAE;gCACoE;8CACrE,8OAAC;oCAAE,MAAK;oCAA6B,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3F", "debugId": null}}, {"offset": {"line": 3806, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/onboarding/onboarding-flow.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Progress } from '@/components/ui/progress';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { CheckCircle } from 'lucide-react';\nimport { RegionSelector } from './region-selector';\nimport { ConsentManager } from './consent-manager';\nimport { AccountForm } from './account-form';\nimport { OnboardingSuccess } from './onboarding-success';\nimport { privacyService } from '@/lib/privacy-service';\nimport type { PrivacyRegion } from '@/lib/utils';\n\ntype OnboardingStep = 'region' | 'consent' | 'account' | 'success';\n\ninterface OnboardingState {\n  currentStep: OnboardingStep;\n  selectedRegion: PrivacyRegion | null;\n  consents: Record<string, boolean>;\n  isLoading: boolean;\n  userId: string | null;\n}\n\nexport function OnboardingFlow() {\n  const [state, setState] = useState<OnboardingState>({\n    currentStep: 'region',\n    selectedRegion: null,\n    consents: {\n      // Set required consents to true by default\n      'PERSONAL_IDENTIFIERS_account_creation_and_management': true,\n      'CONTACT_INFO_service_communications': true,\n    },\n    isLoading: false,\n    userId: null,\n  });\n\n  const steps = [\n    { key: 'region', label: 'Data Region', description: 'Choose your data location' },\n    { key: 'consent', label: 'Privacy Preferences', description: 'Manage your data consent' },\n    { key: 'account', label: 'Account Details', description: 'Complete your profile' },\n    { key: 'success', label: 'Complete', description: 'Account created successfully' },\n  ];\n\n  const currentStepIndex = steps.findIndex(step => step.key === state.currentStep);\n  const progress = ((currentStepIndex + 1) / steps.length) * 100;\n\n  const handleRegionSelect = (region: PrivacyRegion) => {\n    setState(prev => ({ ...prev, selectedRegion: region }));\n  };\n\n  const handleRegionNext = () => {\n    setState(prev => ({ ...prev, currentStep: 'consent' }));\n  };\n\n  const handleConsentChange = (consentKey: string, granted: boolean) => {\n    setState(prev => ({\n      ...prev,\n      consents: { ...prev.consents, [consentKey]: granted }\n    }));\n  };\n\n  const handleConsentNext = () => {\n    setState(prev => ({ ...prev, currentStep: 'account' }));\n  };\n\n  const handleConsentBack = () => {\n    setState(prev => ({ ...prev, currentStep: 'region' }));\n  };\n\n  const handleAccountSubmit = async (accountData: any) => {\n    if (!state.selectedRegion) return;\n\n    setState(prev => ({ ...prev, isLoading: true }));\n\n    try {\n      // Prepare consent data for registration\n      const consentData = Object.entries(state.consents)\n        .filter(([_, granted]) => granted)\n        .map(([key, _]) => {\n          // Parse consent key to extract data category and purpose\n          const [dataCategory, ...purposeParts] = key.split('_');\n          const purpose = purposeParts.join(' ').replace(/_/g, ' ');\n          \n          return {\n            dataCategory: dataCategory as any,\n            purpose: purpose,\n            granted: true,\n            region: state.selectedRegion!,\n          };\n        });\n\n      // Register user with privacy service\n      const registrationData = {\n        firstName: accountData.firstName,\n        lastName: accountData.lastName,\n        email: accountData.email,\n        preferredRegion: state.selectedRegion,\n        dataResidencyPreference: accountData.dataResidencyPreference,\n        consents: consentData,\n        communicationPreferences: {\n          marketing: accountData.marketingEmails,\n          analytics: accountData.analyticsOptIn,\n          personalization: false,\n          thirdPartySharing: false,\n        },\n        acceptTerms: accountData.acceptTerms,\n        acceptPrivacyPolicy: accountData.acceptPrivacyPolicy,\n        ageVerification: accountData.ageVerification,\n      };\n\n      const result = await privacyService.registerUser(registrationData);\n      \n      if (result.success) {\n        setState(prev => ({ \n          ...prev, \n          userId: result.userId,\n          currentStep: 'success',\n          isLoading: false \n        }));\n      } else {\n        throw new Error('Registration failed');\n      }\n    } catch (error) {\n      console.error('Registration error:', error);\n      setState(prev => ({ ...prev, isLoading: false }));\n      // In a real app, show error message to user\n    }\n  };\n\n  const handleAccountBack = () => {\n    setState(prev => ({ ...prev, currentStep: 'consent' }));\n  };\n\n  const renderCurrentStep = () => {\n    switch (state.currentStep) {\n      case 'region':\n        return (\n          <RegionSelector\n            selectedRegion={state.selectedRegion}\n            onRegionSelect={handleRegionSelect}\n            onNext={handleRegionNext}\n          />\n        );\n      \n      case 'consent':\n        return state.selectedRegion ? (\n          <ConsentManager\n            selectedRegion={state.selectedRegion}\n            consents={state.consents}\n            onConsentChange={handleConsentChange}\n            onNext={handleConsentNext}\n            onBack={handleConsentBack}\n          />\n        ) : null;\n      \n      case 'account':\n        return state.selectedRegion ? (\n          <AccountForm\n            selectedRegion={state.selectedRegion}\n            onSubmit={handleAccountSubmit}\n            onBack={handleAccountBack}\n            isLoading={state.isLoading}\n          />\n        ) : null;\n      \n      case 'success':\n        return state.userId ? (\n          <OnboardingSuccess userId={state.userId} />\n        ) : null;\n      \n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8 px-4\">\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Privacy-First Account Setup\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Create your account with complete control over your data\n          </p>\n        </div>\n\n        {/* Progress Indicator */}\n        <Card className=\"mb-8\">\n          <CardContent className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  Step {currentStepIndex + 1} of {steps.length}\n                </span>\n                <span className=\"text-sm text-gray-500\">\n                  {Math.round(progress)}% Complete\n                </span>\n              </div>\n              \n              <Progress value={progress} className=\"h-2\" />\n              \n              <div className=\"flex justify-between\">\n                {steps.map((step, index) => {\n                  const isCompleted = index < currentStepIndex;\n                  const isCurrent = index === currentStepIndex;\n                  \n                  return (\n                    <div key={step.key} className=\"flex flex-col items-center text-center\">\n                      <div className={`\n                        flex items-center justify-center w-8 h-8 rounded-full border-2 mb-2\n                        ${isCompleted \n                          ? 'bg-green-500 border-green-500 text-white' \n                          : isCurrent \n                            ? 'border-blue-500 text-blue-500' \n                            : 'border-gray-300 text-gray-300'\n                        }\n                      `}>\n                        {isCompleted ? (\n                          <CheckCircle className=\"h-4 w-4\" />\n                        ) : (\n                          <span className=\"text-sm font-medium\">{index + 1}</span>\n                        )}\n                      </div>\n                      <div className=\"space-y-1\">\n                        <div className={`text-sm font-medium ${\n                          isCurrent ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'\n                        }`}>\n                          {step.label}\n                        </div>\n                        <div className=\"text-xs text-gray-500 max-w-20\">\n                          {step.description}\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Current Step Content */}\n        <div className=\"mb-8\">\n          {renderCurrentStep()}\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center text-sm text-gray-500\">\n          <p>\n            Your privacy is our priority. All data is encrypted and processed according to \n            applicable privacy laws.\n          </p>\n          <div className=\"flex justify-center gap-4 mt-2\">\n            <a href=\"/privacy\" className=\"hover:text-gray-700\">Privacy Policy</a>\n            <a href=\"/terms\" className=\"hover:text-gray-700\">Terms of Service</a>\n            <a href=\"/security\" className=\"hover:text-gray-700\">Security</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAwBO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,aAAa;QACb,gBAAgB;QAChB,UAAU;YACR,2CAA2C;YAC3C,wDAAwD;YACxD,uCAAuC;QACzC;QACA,WAAW;QACX,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ;YAAE,KAAK;YAAU,OAAO;YAAe,aAAa;QAA4B;QAChF;YAAE,KAAK;YAAW,OAAO;YAAuB,aAAa;QAA2B;QACxF;YAAE,KAAK;YAAW,OAAO;YAAmB,aAAa;QAAwB;QACjF;YAAE,KAAK;YAAW,OAAO;YAAY,aAAa;QAA+B;KAClF;IAED,MAAM,mBAAmB,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK,MAAM,WAAW;IAC/E,MAAM,WAAW,AAAC,CAAC,mBAAmB,CAAC,IAAI,MAAM,MAAM,GAAI;IAE3D,MAAM,qBAAqB,CAAC;QAC1B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,gBAAgB;YAAO,CAAC;IACvD;IAEA,MAAM,mBAAmB;QACvB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAU,CAAC;IACvD;IAEA,MAAM,sBAAsB,CAAC,YAAoB;QAC/C,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,UAAU;oBAAE,GAAG,KAAK,QAAQ;oBAAE,CAAC,WAAW,EAAE;gBAAQ;YACtD,CAAC;IACH;IAEA,MAAM,oBAAoB;QACxB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAU,CAAC;IACvD;IAEA,MAAM,oBAAoB;QACxB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAS,CAAC;IACtD;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,MAAM,cAAc,EAAE;QAE3B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAK,CAAC;QAE9C,IAAI;YACF,wCAAwC;YACxC,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM,QAAQ,EAC9C,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAK,SACzB,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE;gBACZ,yDAAyD;gBACzD,MAAM,CAAC,cAAc,GAAG,aAAa,GAAG,IAAI,KAAK,CAAC;gBAClD,MAAM,UAAU,aAAa,IAAI,CAAC,KAAK,OAAO,CAAC,MAAM;gBAErD,OAAO;oBACL,cAAc;oBACd,SAAS;oBACT,SAAS;oBACT,QAAQ,MAAM,cAAc;gBAC9B;YACF;YAEF,qCAAqC;YACrC,MAAM,mBAAmB;gBACvB,WAAW,YAAY,SAAS;gBAChC,UAAU,YAAY,QAAQ;gBAC9B,OAAO,YAAY,KAAK;gBACxB,iBAAiB,MAAM,cAAc;gBACrC,yBAAyB,YAAY,uBAAuB;gBAC5D,UAAU;gBACV,0BAA0B;oBACxB,WAAW,YAAY,eAAe;oBACtC,WAAW,YAAY,cAAc;oBACrC,iBAAiB;oBACjB,mBAAmB;gBACrB;gBACA,aAAa,YAAY,WAAW;gBACpC,qBAAqB,YAAY,mBAAmB;gBACpD,iBAAiB,YAAY,eAAe;YAC9C;YAEA,MAAM,SAAS,MAAM,gIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YAEjD,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,QAAQ,OAAO,MAAM;wBACrB,aAAa;wBACb,WAAW;oBACb,CAAC;YACH,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QAC/C,4CAA4C;QAC9C;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAU,CAAC;IACvD;IAEA,MAAM,oBAAoB;QACxB,OAAQ,MAAM,WAAW;YACvB,KAAK;gBACH,qBACE,8OAAC,sJAAA,CAAA,iBAAc;oBACb,gBAAgB,MAAM,cAAc;oBACpC,gBAAgB;oBAChB,QAAQ;;;;;;YAId,KAAK;gBACH,OAAO,MAAM,cAAc,iBACzB,8OAAC,sJAAA,CAAA,iBAAc;oBACb,gBAAgB,MAAM,cAAc;oBACpC,UAAU,MAAM,QAAQ;oBACxB,iBAAiB;oBACjB,QAAQ;oBACR,QAAQ;;;;;2BAER;YAEN,KAAK;gBACH,OAAO,MAAM,cAAc,iBACzB,8OAAC,mJAAA,CAAA,cAAW;oBACV,gBAAgB,MAAM,cAAc;oBACpC,UAAU;oBACV,QAAQ;oBACR,WAAW,MAAM,SAAS;;;;;2BAE1B;YAEN,KAAK;gBACH,OAAO,MAAM,MAAM,iBACjB,8OAAC,yJAAA,CAAA,oBAAiB;oBAAC,QAAQ,MAAM,MAAM;;;;;2BACrC;YAEN;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAoC;gDAC5C,mBAAmB;gDAAE;gDAAK,MAAM,MAAM;;;;;;;sDAE9C,8OAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC;gDAAU;;;;;;;;;;;;;8CAI1B,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,OAAO;oCAAU,WAAU;;;;;;8CAErC,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,MAAM;wCAChB,MAAM,cAAc,QAAQ;wCAC5B,MAAM,YAAY,UAAU;wCAE5B,qBACE,8OAAC;4CAAmB,WAAU;;8DAC5B,8OAAC;oDAAI,WAAW,CAAC;;wBAEf,EAAE,cACE,6CACA,YACE,kCACA,gCACL;sBACH,CAAC;8DACE,4BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC;wDAAK,WAAU;kEAAuB,QAAQ;;;;;;;;;;;8DAGnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,oBAAoB,EACnC,YAAY,kBAAkB,cAAc,mBAAmB,iBAC/D;sEACC,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW;;;;;;;;;;;;;2CAvBb,KAAK,GAAG;;;;;oCA4BtB;;;;;;;;;;;;;;;;;;;;;;8BAOR,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCAIH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAsB;;;;;;8CACnD,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAsB;;;;;;8CACjD,8OAAC;oCAAE,MAAK;oCAAY,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE", "debugId": null}}, {"offset": {"line": 4338, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration with fallbacks\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-key';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured = !!(\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'https://placeholder.supabase.co' &&\n  supabaseAnonKey !== 'placeholder-key' &&\n  supabaseUrl !== 'YOUR_SUPABASE_PROJECT_URL' &&\n  supabaseAnonKey !== 'YOUR_SUPABASE_ANON_KEY'\n);\n\n// Client-side Supabase client (with RLS enabled) - only create if configured\nexport const supabase = isSupabaseConfigured ? createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  db: {\n    schema: 'public'\n  },\n  global: {\n    headers: {\n      'X-Client-Info': 'privacy-first-app'\n    }\n  }\n}) : null;\n\n// Server-side Supabase client (bypasses RLS for admin operations) - only create if configured\nexport const supabaseAdmin = isSupabaseConfigured ? createClient(supabaseUrl, supabaseServiceKey, {\n  auth: {\n    autoRefreshToken: false,\n    persistSession: false\n  },\n  db: {\n    schema: 'public'\n  }\n}) : null;\n\n// Regional Supabase clients for data residency compliance\nexport class SupabaseRegionalManager {\n  private static instance: SupabaseRegionalManager;\n  private regionalClients: Map<string, any> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): SupabaseRegionalManager {\n    if (!SupabaseRegionalManager.instance) {\n      SupabaseRegionalManager.instance = new SupabaseRegionalManager();\n    }\n    return SupabaseRegionalManager.instance;\n  }\n\n  // Get region-specific Supabase client\n  getRegionalClient(region: string) {\n    if (!this.regionalClients.has(region)) {\n      // In production, you would have different Supabase projects for each region\n      const regionalUrls: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_URL_US || supabaseUrl,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_URL_EU || supabaseUrl,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_URL_UK || supabaseUrl,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_URL_CA || supabaseUrl,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_URL_IN || supabaseUrl,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_URL_SG || supabaseUrl,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_URL_JP || supabaseUrl,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_URL_AU || supabaseUrl,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_URL_BR || supabaseUrl,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_URL_CN || supabaseUrl,\n      };\n\n      const regionalKeys: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_US || supabaseAnonKey,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_EU || supabaseAnonKey,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_UK || supabaseAnonKey,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CA || supabaseAnonKey,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_IN || supabaseAnonKey,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_SG || supabaseAnonKey,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_JP || supabaseAnonKey,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_AU || supabaseAnonKey,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_BR || supabaseAnonKey,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CN || supabaseAnonKey,\n      };\n\n      const client = createClient(\n        regionalUrls[region] || supabaseUrl,\n        regionalKeys[region] || supabaseAnonKey,\n        {\n          auth: {\n            autoRefreshToken: true,\n            persistSession: true\n          }\n        }\n      );\n\n      this.regionalClients.set(region, client);\n    }\n\n    return this.regionalClients.get(region);\n  }\n}\n\n// Utility functions for Supabase operations\nexport const supabaseUtils = {\n  // Check if Supabase is properly configured\n  isConfigured: () => {\n    return isSupabaseConfigured;\n  },\n\n  // Get current user\n  getCurrentUser: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n  },\n\n  // Sign in with email and password\n  signIn: async (email: string, password: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign up with email and password\n  signUp: async (email: string, password: string, metadata?: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: metadata\n      }\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign out\n  signOut: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n  },\n\n  // Create audit log entry\n  createAuditLog: async (logData: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('audit_logs')\n      .insert([logData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get user's privacy profile\n  getUserPrivacyProfile: async (userId: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('users')\n      .select(`\n        *,\n        consents(*),\n        privacy_requests(*),\n        tenant:tenants(*)\n      `)\n      .eq('id', userId)\n      .single();\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Update user consent\n  updateConsent: async (consentId: string, granted: boolean) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('consents')\n      .update({\n        granted,\n        granted_at: granted ? new Date().toISOString() : null,\n        withdrawn_at: !granted ? new Date().toISOString() : null,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', consentId);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Create privacy request\n  createPrivacyRequest: async (requestData: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('privacy_requests')\n      .insert([requestData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get compliance status\n  getComplianceStatus: async (tenantId: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('compliance_status')\n      .select('*')\n      .eq('tenant_id', tenantId);\n\n    if (error) throw error;\n    return data;\n  }\n};\n\n// Export regional manager instance\nexport const supabaseRegional = SupabaseRegionalManager.getInstance();\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,wCAAwC;AACxC,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAEpE,2CAA2C;AAC3C,MAAM,uBAAuB,CAAC,CAAC,CAC7B,eACA,mBACA,gBAAgB,qCAChB,oBAAoB,qBACpB,gBAAgB,+BAChB,oBAAoB,wBACtB;AAGO,MAAM,WAAW,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACxF,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,gBAAgB,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oBAAoB;IAChG,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;IACA,IAAI;QACF,QAAQ;IACV;AACF;AAGO,MAAM;IACX,OAAe,SAAkC;IACzC,kBAAoC,IAAI,MAAM;IAEtD,aAAsB,CAAC;IAEvB,OAAO,cAAuC;QAC5C,IAAI,CAAC,wBAAwB,QAAQ,EAAE;YACrC,wBAAwB,QAAQ,GAAG,IAAI;QACzC;QACA,OAAO,wBAAwB,QAAQ;IACzC;IAEA,sCAAsC;IACtC,kBAAkB,MAAc,EAAE;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS;YACrC,4EAA4E;YAC5E,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;YACnD;YAEA,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;YACxD;YAEA,MAAM,SAAS,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACxB,YAAY,CAAC,OAAO,IAAI,aACxB,YAAY,CAAC,OAAO,IAAI,iBACxB;gBACE,MAAM;oBACJ,kBAAkB;oBAClB,gBAAgB;gBAClB;YACF;YAGF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ;QACnC;QAEA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;AACF;AAGO,MAAM,gBAAgB;IAC3B,2CAA2C;IAC3C,cAAc;QACZ,OAAO;IACT;IAEA,mBAAmB;IACnB,gBAAgB;QACd,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe;QAC5B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe,UAAkB;QAC9C,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,WAAW;IACX,SAAS;QACP,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,yBAAyB;IACzB,gBAAgB,OAAO;QACrB,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC;YAAC;SAAQ;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,6BAA6B;IAC7B,uBAAuB,OAAO;QAC5B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;MAKT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,sBAAsB;IACtB,eAAe,OAAO,WAAmB;QACvC,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YACN;YACA,YAAY,UAAU,IAAI,OAAO,WAAW,KAAK;YACjD,cAAc,CAAC,UAAU,IAAI,OAAO,WAAW,KAAK;YACpD,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,yBAAyB;IACzB,sBAAsB,OAAO;QAC3B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAY;QAEvB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,qBAAqB,OAAO;QAC1B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB,wBAAwB,WAAW", "debugId": null}}, {"offset": {"line": 4524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/supabase-status.tsx"], "sourcesContent": ["'use client';\n\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Badge } from '@/components/ui/badge';\nimport { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';\nimport { supabaseUtils } from '@/lib/supabase';\n\nexport function SupabaseStatus() {\n  const isConfigured = supabaseUtils.isConfigured();\n\n  if (isConfigured) {\n    return (\n      <Alert className=\"mb-4 border-green-200 bg-green-50\">\n        <CheckCircle className=\"h-4 w-4 text-green-600\" />\n        <AlertDescription className=\"text-green-800\">\n          <div className=\"flex items-center gap-2\">\n            <Badge variant=\"outline\" className=\"bg-green-100 text-green-800 border-green-300\">\n              Supabase Connected\n            </Badge>\n            <span>Using real database for data storage</span>\n          </div>\n        </AlertDescription>\n      </Alert>\n    );\n  }\n\n  return (\n    <Alert className=\"mb-4 border-amber-200 bg-amber-50\">\n      <AlertTriangle className=\"h-4 w-4 text-amber-600\" />\n      <AlertDescription className=\"text-amber-800\">\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center gap-2\">\n            <Badge variant=\"outline\" className=\"bg-amber-100 text-amber-800 border-amber-300\">\n              Mock Data Mode\n            </Badge>\n            <span>Supabase not configured - using demo data</span>\n          </div>\n          <div className=\"text-sm\">\n            <p className=\"font-medium mb-1\">To connect your Supabase database:</p>\n            <ol className=\"list-decimal list-inside space-y-1 text-xs\">\n              <li>Set up your Supabase project</li>\n              <li>Update your <code className=\"bg-amber-100 px-1 rounded\">.env.local</code> file</li>\n              <li>Run the database migrations</li>\n              <li>Restart your development server</li>\n            </ol>\n            <p className=\"mt-2 text-xs\">\n              📖 See <code className=\"bg-amber-100 px-1 rounded\">SUPABASE_SETUP.md</code> for detailed instructions\n            </p>\n          </div>\n        </div>\n      </AlertDescription>\n    </Alert>\n  );\n}\n\nexport function SupabaseStatusBadge() {\n  const isConfigured = supabaseUtils.isConfigured();\n\n  return (\n    <Badge \n      variant=\"outline\" \n      className={\n        isConfigured \n          ? \"bg-green-100 text-green-800 border-green-300\" \n          : \"bg-amber-100 text-amber-800 border-amber-300\"\n      }\n    >\n      {isConfigured ? (\n        <>\n          <CheckCircle className=\"h-3 w-3 mr-1\" />\n          Supabase\n        </>\n      ) : (\n        <>\n          <XCircle className=\"h-3 w-3 mr-1\" />\n          Mock Data\n        </>\n      )}\n    </Badge>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,eAAe,sHAAA,CAAA,gBAAa,CAAC,YAAY;IAE/C,IAAI,cAAc;QAChB,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC,iIAAA,CAAA,mBAAgB;oBAAC,WAAU;8BAC1B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAA+C;;;;;;0CAGlF,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAU;;0BACf,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC,iIAAA,CAAA,mBAAgB;gBAAC,WAAU;0BAC1B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAA+C;;;;;;8CAGlF,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAmB;;;;;;8CAChC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;;gDAAG;8DAAY,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;gDAAiB;;;;;;;sDAC7E,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAEN,8OAAC;oCAAE,WAAU;;wCAAe;sDACnB,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;wCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzF;AAEO,SAAS;IACd,MAAM,eAAe,sHAAA,CAAA,gBAAa,CAAC,YAAY;IAE/C,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WACE,eACI,iDACA;kBAGL,6BACC;;8BACE,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;gBAAiB;;yCAI1C;;8BACE,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM9C", "debugId": null}}]}
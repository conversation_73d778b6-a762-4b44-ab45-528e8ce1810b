# 🚀 Complete Supabase Setup Guide

Follow these steps to migrate from mock data to real Supabase database persistence.

## 📋 Quick Overview

1. **Set up Supabase database** with migrations and seed data
2. **Update service selection logic** to use Supabase instead of mock data  
3. **Test real data persistence** to ensure everything works

---

## 🗄️ Step 1: Set up Supabase Database

### 1.1 Get Your Database Password
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `sktoyclpuaqpslvnssgg`
3. Go to **Settings** → **Database**
4. Copy or reset your database password

### 1.2 Update Environment Variables
```bash
# Edit .env.local file and replace [YOUR-PASSWORD] with your actual password:
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
DIRECT_URL="postgresql://postgres:<EMAIL>:5432/postgres"
```

### 1.3 Run Database Migrations

**Option A: Using Supabase SQL Editor (Recommended)**
1. Open **SQL Editor** in your Supabase dashboard
2. Copy and run `supabase/migrations/001_initial_schema.sql`
3. Copy and run `supabase/corrected_seed.sql`
4. Copy and run `supabase/rls_policies.sql`

**Option B: Using Prisma**
```bash
# After updating .env.local with correct password:
npx prisma db push
# Then still run corrected_seed.sql in Supabase SQL Editor
```

---

## 🔄 Step 2: Switch to Supabase

### 2.1 Test Database Connection
```bash
# Test if everything is working:
node src/scripts/test-supabase-connection.js
```

### 2.2 Switch Service Logic
```bash
# Switch from mock data to Supabase:
node src/scripts/switch-to-supabase.js
```

### 2.3 Restart Development Server
```bash
npm run dev
```

---

## 🧪 Step 3: Test Real Data Persistence

### 3.1 Test User Registration
1. Go to `http://localhost:3001`
2. Fill out the onboarding form
3. Submit and verify data is saved to Supabase

### 3.2 Test Privacy Dashboard
1. Go to `http://localhost:3001/dashboard`
2. Enter a user ID or use the demo account
3. Verify data loads from Supabase (not mock data)

### 3.3 Test Consent Management
1. In the dashboard, go to **Consent Management**
2. Toggle consent preferences
3. Verify changes are saved to database

### 3.4 Test Privacy Requests
1. Go to **Your Rights** tab
2. Submit a privacy request (e.g., data export)
3. Verify request is saved to database

---

## ✅ Verification Checklist

- [ ] Database password updated in `.env.local`
- [ ] Schema migration completed successfully
- [ ] Seed data inserted successfully
- [ ] RLS policies applied
- [ ] Connection test passes
- [ ] Service logic switched to Supabase
- [ ] Development server restarted
- [ ] User registration saves to database
- [ ] Dashboard loads real data
- [ ] Consent changes persist
- [ ] Privacy requests are saved
- [ ] Status shows "Supabase Connected" instead of "Mock Data Mode"

---

## 🚨 Troubleshooting

### Common Issues:

**"Connection refused" or "Authentication failed"**
- Double-check your database password
- Ensure your Supabase project is active
- Verify the connection string format

**"Table doesn't exist"**
- Make sure you ran the schema migration
- Check Supabase dashboard to see if tables were created
- Try running the migration again

**"Environment variable not found"**
- Ensure `.env.local` has both `DATABASE_URL` and `DIRECT_URL`
- Restart your development server after updating env vars

**Still showing "Mock Data Mode"**
- Make sure you ran the switch script: `node src/scripts/switch-to-supabase.js`
- Restart your development server
- Check browser console for any errors

### Getting Help:
- Check Supabase logs in the dashboard
- Run the test script: `node src/scripts/test-supabase-connection.js`
- Verify your connection string in Supabase Settings → Database

---

## 🎉 Success!

Once everything is working, you'll have:
- ✅ **Real database persistence** instead of mock data
- ✅ **Multi-tenant architecture** with proper data isolation
- ✅ **Privacy compliance features** with real data storage
- ✅ **Audit logging** for all privacy-related actions
- ✅ **Scalable infrastructure** ready for production

Your Privacy-First Application is now running with real Supabase database persistence! 🚀

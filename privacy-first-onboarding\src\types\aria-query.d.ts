// Type definitions for aria-query
// This file provides type definitions for the aria-query module to resolve TypeScript errors

declare module 'aria-query' {
  export interface ARIAAbstractRole {
    abstract: boolean;
    accessibleNameRequired: boolean;
    baseConcepts: Array<any>;
    childrenPresentational: boolean;
    nameFrom: Array<string>;
    prohibitedProps: Array<string>;
    props: Record<string, any>;
    relatedConcepts: Array<any>;
    requireContextRole: Array<string>;
    requiredContextRole: Array<string>;
    requiredOwnedElements: Array<Array<string>>;
    requiredProps: Record<string, any>;
    superClass: Array<Array<string>>;
  }

  export interface ARIARole extends ARIAAbstractRole {
    abstract: false;
  }

  export interface ARIAAbstractRoleDefinition extends ARIAAbstractRole {
    abstract: true;
  }

  export type ARIARoleDefinition = ARIARole | ARIAAbstractRoleDefinition;

  export const roles: Map<string, ARIARoleDefinition>;
  export const elementRoles: Map<string, Set<string>>;
  export const roleElements: Map<string, Set<string>>;
  export const dom: Map<string, any>;

  // Additional exports that might be used
  export function getRoles(): Map<string, ARIARoleDefinition>;
  export function getRole(role: string): ARIARoleDefinition | undefined;
  export function getElementRoles(element: string): Set<string> | undefined;
  export function getRoleElements(role: string): Set<string> | undefined;
}

// Re-export for compatibility
declare module 'aria-query/lib/etc/roles' {
  export * from 'aria-query';
}

declare module 'aria-query/lib/elementRoleMap' {
  export * from 'aria-query';
}

declare module 'aria-query/lib/roleElementMap' {
  export * from 'aria-query';
}

declare module 'aria-query/lib/domMap' {
  export * from 'aria-query';
}

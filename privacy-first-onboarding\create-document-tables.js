const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createTables() {
  console.log('🚀 Creating Document Management Tables...\n');

  // Create document_templates table using direct insert
  console.log('📄 Creating sample document template...');
  const tenantId = '550e8400-e29b-41d4-a716-446655440000';

  const { data: existingTemplate, error: checkError } = await supabase
    .from('document_templates')
    .select('id')
    .eq('tenant_id', tenantId)
    .eq('name', 'Standard Privacy Policy')
    .single();

  if (checkError && checkError.code !== 'PGRST116') {
    console.log('⚠️ document_templates table does not exist yet. This is expected for first run.');
  }

  if (!existingTemplate) {
    // Try to insert a sample template to test if table exists
    const { data: template, error: templatesError } = await supabase
      .from('document_templates')
      .insert([{
        tenant_id: tenantId,
        template_type: 'privacy_policy',
        name: 'Standard Privacy Policy',
        description: 'GDPR and CCPA compliant privacy policy template',
        template_content: {
          company_name: '{{company_name}}',
          contact_email: '{{contact_email}}'
        },
        markdown_template: `# Privacy Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Information We Collect
We collect personal identifiers, contact information, and usage data.

## 2. How We Use Your Information
We use your information to provide services and ensure compliance.

## 3. Your Rights
You have the right to access, correct, and delete your data.

## 4. Contact Information
For privacy questions, contact us at: {{contact_email}}`,
        compliance_frameworks: ['GDPR', 'CCPA']
      }])
      .select()
      .single();

    if (templatesError) {
      console.error('❌ Error creating document template:', templatesError.message);
      console.log('⚠️ This might mean the document_templates table doesn\'t exist yet.');
    } else {
      console.log('✅ Sample document template created:', template.id);
    }
  } else {
    console.log('✅ Document template already exists');
  }
  
  // Create document_versions table
  console.log('📄 Creating document_versions table...');
  const { error: versionsError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS document_versions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        tenant_id UUID NOT NULL,
        template_id UUID NOT NULL REFERENCES document_templates(id) ON DELETE CASCADE,
        version_number TEXT NOT NULL,
        version_type TEXT NOT NULL CHECK (version_type IN ('major', 'minor', 'patch')),
        markdown_content TEXT NOT NULL,
        generated_data JSONB NOT NULL DEFAULT '{}',
        pdf_url TEXT,
        status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
        changelog TEXT,
        published_at TIMESTAMP WITH TIME ZONE,
        created_by UUID,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        UNIQUE(tenant_id, template_id, version_number)
      );
    `
  });
  
  if (versionsError) {
    console.error('❌ Error creating document_versions:', versionsError.message);
  } else {
    console.log('✅ document_versions table created');
  }
  
  // Create end_user_consents table
  console.log('📄 Creating end_user_consents table...');
  const { error: consentsError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS end_user_consents (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        tenant_id UUID NOT NULL,
        document_version_id UUID NOT NULL REFERENCES document_versions(id) ON DELETE CASCADE,
        end_user_identifier TEXT NOT NULL,
        end_user_email TEXT,
        consent_type TEXT NOT NULL CHECK (consent_type IN ('privacy_policy', 'terms_of_service', 'cookie_policy')),
        consent_given BOOLEAN NOT NULL,
        consent_timestamp TIMESTAMP WITH TIME ZONE DEFAULT now(),
        ip_address INET,
        user_agent TEXT,
        consent_method TEXT NOT NULL CHECK (consent_method IN ('website_signup', 'email_confirmation', 'api_integration', 'embedded_widget')),
        notification_sent BOOLEAN DEFAULT false,
        notification_sent_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
    `
  });
  
  if (consentsError) {
    console.error('❌ Error creating end_user_consents:', consentsError.message);
  } else {
    console.log('✅ end_user_consents table created');
  }
  
  // Create notification_queue table
  console.log('📄 Creating notification_queue table...');
  const { error: queueError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS notification_queue (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        tenant_id UUID NOT NULL,
        document_version_id UUID NOT NULL REFERENCES document_versions(id) ON DELETE CASCADE,
        end_user_email TEXT NOT NULL,
        notification_type TEXT NOT NULL CHECK (notification_type IN ('version_update', 'consent_confirmation', 'policy_change')),
        priority INTEGER DEFAULT 5,
        scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT now(),
        processed_at TIMESTAMP WITH TIME ZONE,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
    `
  });
  
  if (queueError) {
    console.error('❌ Error creating notification_queue:', queueError.message);
  } else {
    console.log('✅ notification_queue table created');
  }
  
  // Create document_audit_log table
  console.log('📄 Creating document_audit_log table...');
  const { error: auditError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS document_audit_log (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        tenant_id UUID NOT NULL,
        action TEXT NOT NULL,
        resource_type TEXT NOT NULL,
        resource_id UUID NOT NULL,
        user_id UUID,
        old_values JSONB,
        new_values JSONB,
        ip_address INET,
        user_agent TEXT,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
    `
  });
  
  if (auditError) {
    console.error('❌ Error creating document_audit_log:', auditError.message);
  } else {
    console.log('✅ document_audit_log table created');
  }
  
  // Create helper functions
  console.log('📄 Creating helper functions...');
  const { error: funcError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE OR REPLACE FUNCTION calculate_next_version(
        current_version TEXT,
        version_type TEXT
      ) RETURNS TEXT AS $$
      DECLARE
        version_parts TEXT[];
        major_num INTEGER;
        minor_num INTEGER;
        patch_num INTEGER;
      BEGIN
        IF current_version IS NULL OR current_version = '' THEN
          RETURN '1.0.0';
        END IF;
        
        version_parts := string_to_array(current_version, '.');
        major_num := COALESCE(version_parts[1]::INTEGER, 0);
        minor_num := COALESCE(version_parts[2]::INTEGER, 0);
        patch_num := COALESCE(version_parts[3]::INTEGER, 0);
        
        CASE version_type
          WHEN 'major' THEN
            RETURN (major_num + 1) || '.0.0';
          WHEN 'minor' THEN
            RETURN major_num || '.' || (minor_num + 1) || '.0';
          WHEN 'patch' THEN
            RETURN major_num || '.' || minor_num || '.' || (patch_num + 1);
          ELSE
            RETURN major_num || '.' || (minor_num + 1) || '.0';
        END CASE;
      END;
      $$ LANGUAGE plpgsql;
    `
  });
  
  if (funcError) {
    console.error('❌ Error creating functions:', funcError.message);
  } else {
    console.log('✅ Helper functions created');
  }
  
  // Insert sample data
  console.log('📄 Inserting sample data...');
  const tenantId = '550e8400-e29b-41d4-a716-446655440000';
  
  const { error: sampleError } = await supabase.rpc('exec_sql', {
    sql: `
      INSERT INTO document_templates (
        tenant_id, template_type, name, description, template_content, markdown_template, compliance_frameworks
      ) VALUES 
      (
        '${tenantId}',
        'privacy_policy',
        'Standard Privacy Policy',
        'GDPR and CCPA compliant privacy policy template',
        '{"company_name": "{{company_name}}", "contact_email": "{{contact_email}}"}',
        '# Privacy Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Information We Collect
We collect personal identifiers, contact information, and usage data.

## 2. How We Use Your Information
We use your information to provide services and ensure compliance.

## 3. Your Rights
You have the right to access, correct, and delete your data.

## 4. Contact Information
For privacy questions, contact us at: {{contact_email}}',
        ARRAY['GDPR', 'CCPA']
      ) ON CONFLICT (tenant_id, name, template_type) DO NOTHING;
    `
  });
  
  if (sampleError) {
    console.error('❌ Error inserting sample data:', sampleError.message);
  } else {
    console.log('✅ Sample data inserted');
  }
  
  console.log('\n🎉 Document Management System setup complete!');
}

createTables().catch(console.error);

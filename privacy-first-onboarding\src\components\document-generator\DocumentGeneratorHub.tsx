// Document Generator Hub - Main landing page for all document generators
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  FileText, 
  Cookie, 
  Scale, 
  CheckCircle, 
  RotateCcw, 
  Truck, 
  AlertTriangle,
  Plus,
  Edit3,
  Clock
} from 'lucide-react';
import { DocumentGeneratorType, DocumentGeneratorSession } from '@/lib/document-generator-types';
import { DocumentGeneratorService } from '@/lib/document-generator-service';

interface DocumentGeneratorHubProps {
  tenantId: string;
  userId?: string;
  onStartGenerator: (type: DocumentGeneratorType, sessionId?: string) => void;
}

interface GeneratorConfig {
  type: DocumentGeneratorType;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
}

const generatorConfigs: GeneratorConfig[] = [
  {
    type: 'privacy_policy',
    title: 'Privacy Policy Generator',
    description: 'GDPR & CCPA compliant privacy policies',
    icon: Shield,
    color: 'text-green-600',
    bgColor: 'bg-green-50 hover:bg-green-100'
  },
  {
    type: 'terms_conditions',
    title: 'Terms and Conditions Generator',
    description: 'Service terms and user agreements',
    icon: FileText,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 hover:bg-blue-100'
  },
  {
    type: 'cookie_policy',
    title: 'Cookie Policy Generator',
    description: 'Cookie usage and consent policies',
    icon: Cookie,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 hover:bg-orange-100'
  },
  {
    type: 'eula',
    title: 'EULA Generator',
    description: 'End User License Agreements',
    icon: Scale,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 hover:bg-purple-100'
  },
  {
    type: 'acceptable_use',
    title: 'Acceptable Use Policy Generator',
    description: 'Platform usage guidelines',
    icon: CheckCircle,
    color: 'text-emerald-600',
    bgColor: 'bg-emerald-50 hover:bg-emerald-100'
  },
  {
    type: 'return_policy',
    title: 'Return Policy Generator',
    description: 'E-commerce return policies',
    icon: RotateCcw,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50 hover:bg-indigo-100'
  },
  {
    type: 'disclaimer',
    title: 'Disclaimer Generator',
    description: 'Legal disclaimers and limitations',
    icon: AlertTriangle,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100'
  },
  {
    type: 'shipping_policy',
    title: 'Shipping Policy Generator',
    description: 'Shipping terms and conditions',
    icon: Truck,
    color: 'text-cyan-600',
    bgColor: 'bg-cyan-50 hover:bg-cyan-100'
  }
];

export function DocumentGeneratorHub({ tenantId, userId, onStartGenerator }: DocumentGeneratorHubProps) {
  const [sessions, setSessions] = useState<DocumentGeneratorSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const documentService = new DocumentGeneratorService(tenantId);

  useEffect(() => {
    loadSessions();
  }, [tenantId, userId]);

  const loadSessions = async () => {
    try {
      setLoading(true);
      const response = await documentService.getSessions(userId);
      if (response.success && response.data) {
        setSessions(response.data);
      } else {
        setError(response.error || 'Failed to load sessions');
      }
    } catch (err) {
      setError('Failed to load sessions');
      console.error('Error loading sessions:', err);
    } finally {
      setLoading(false);
    }
  };

  const getSessionForType = (type: DocumentGeneratorType) => {
    return sessions.find(session => session.document_type === type);
  };

  const getCompletionPercentage = (session: DocumentGeneratorSession) => {
    const formData = session.form_data || {};
    const totalFields = 10; // Approximate number of fields per form
    const completedFields = Object.keys(formData).filter(key => 
      formData[key] !== null && formData[key] !== undefined && formData[key] !== ''
    ).length;
    return Math.min((completedFields / totalFields) * 100, 100);
  };

  const handleStartGenerator = async (type: DocumentGeneratorType) => {
    const existingSession = getSessionForType(type);
    if (existingSession) {
      onStartGenerator(type, existingSession.id);
    } else {
      // Create new session
      try {
        const response = await documentService.createSession({
          document_type: type,
          session_name: `${generatorConfigs.find(c => c.type === type)?.title} - ${new Date().toLocaleDateString()}`
        }, userId);

        if (response.success && response.data) {
          onStartGenerator(type, response.data.id);
          await loadSessions(); // Refresh sessions
        } else {
          setError(response.error || 'Failed to create session');
        }
      } catch (err) {
        setError('Failed to create session');
        console.error('Error creating session:', err);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900">Policy Generators</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Attorney-crafted documents and policies. Create professional legal documents 
          with our guided form builders.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Generator Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {generatorConfigs.map((config) => {
          const session = getSessionForType(config.type);
          const hasSession = !!session;
          const completionPercentage = session ? getCompletionPercentage(session) : 0;
          const Icon = config.icon;

          return (
            <Card 
              key={config.type} 
              className={`cursor-pointer transition-all duration-200 ${config.bgColor} border-2 hover:border-gray-300 hover:shadow-lg`}
              onClick={() => handleStartGenerator(config.type)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg bg-white shadow-sm`}>
                      <Icon className={`h-6 w-6 ${config.color}`} />
                    </div>
                    <div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        {config.title}
                      </CardTitle>
                      <CardDescription className="text-sm text-gray-600 mt-1">
                        {config.description}
                      </CardDescription>
                    </div>
                  </div>
                  
                  {hasSession && (
                    <div className="flex flex-col items-end space-y-2">
                      <Badge 
                        variant={session.completion_status === 'completed' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {session.completion_status === 'completed' ? 'Completed' : 'Draft'}
                      </Badge>
                      {session.completion_status !== 'completed' && (
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          <span>{Math.round(completionPercentage)}%</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                {hasSession ? (
                  <div className="space-y-3">
                    {session.completion_status !== 'completed' && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs text-gray-600">
                          <span>Progress</span>
                          <span>{Math.round(completionPercentage)}%</span>
                        </div>
                        <Progress value={completionPercentage} className="h-2" />
                      </div>
                    )}
                    
                    <div className="flex space-x-2">
                      <Button 
                        variant="default" 
                        size="sm" 
                        className="flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleStartGenerator(config.type);
                        }}
                      >
                        <Edit3 className="h-4 w-4 mr-2" />
                        {session.completion_status === 'completed' ? 'Edit' : 'Continue'}
                      </Button>
                    </div>
                    
                    <div className="text-xs text-gray-500">
                      Last updated: {new Date(session.updated_at).toLocaleDateString()}
                    </div>
                  </div>
                ) : (
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleStartGenerator(config.type);
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Start New
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Sessions */}
      {sessions.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-gray-900">Recent Sessions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sessions.slice(0, 6).map((session) => {
              const config = generatorConfigs.find(c => c.type === session.document_type);
              if (!config) return null;
              
              const Icon = config.icon;
              const completionPercentage = getCompletionPercentage(session);

              return (
                <Card 
                  key={session.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onStartGenerator(session.document_type, session.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <Icon className={`h-5 w-5 ${config.color}`} />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {session.session_name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(session.updated_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge 
                        variant={session.completion_status === 'completed' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {Math.round(completionPercentage)}%
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

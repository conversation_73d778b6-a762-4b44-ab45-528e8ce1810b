'use client';

import React, { useEffect, useState } from 'react';
import { ConsentWidget } from '@/components/embed/consent-widget';
import { EmbedWidgetConfig } from '@/lib/document-types';

interface EmbedPageProps {
  params: {
    versionId: string;
  };
}

export default function EmbedConsentPage({ params }: EmbedPageProps) {
  const [config, setConfig] = useState<Partial<EmbedWidgetConfig>>({});
  const [tenantId, setTenantId] = useState<string>('');

  useEffect(() => {
    // Get configuration from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    
    // Extract tenant ID
    const paramTenantId = urlParams.get('tenantId') || '550e8400-e29b-41d4-a716-446655440000';
    setTenantId(paramTenantId);

    // Extract widget configuration
    const widgetConfig: Partial<EmbedWidgetConfig> = {
      widget_type: (urlParams.get('type') as any) || 'inline',
      styling: {
        width: urlParams.get('width') || '100%',
        height: urlParams.get('height') || 'auto',
        background_color: urlParams.get('bg') || '#ffffff',
        text_color: urlParams.get('color') || '#374151',
        button_color: urlParams.get('buttonColor') || '#3b82f6',
        border_radius: urlParams.get('borderRadius') || '8px'
      },
      behavior: {
        auto_show: urlParams.get('autoShow') !== 'false',
        show_delay: parseInt(urlParams.get('delay') || '0'),
        require_scroll: urlParams.get('requireScroll') === 'true',
        remember_choice: urlParams.get('remember') !== 'false'
      },
      content: {
        title: urlParams.get('title') || undefined,
        description: urlParams.get('description') || undefined,
        accept_text: urlParams.get('acceptText') || 'Accept',
        decline_text: urlParams.get('declineText') || 'Decline'
      }
    };

    setConfig(widgetConfig);

    // Handle postMessage communication with parent window
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'init') {
        // Parent window is initializing the widget
        console.log('Widget initialized by parent:', event.data);
      }
    };

    window.addEventListener('message', handleMessage);
    
    // Notify parent that widget is ready
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'widget-ready',
        versionId: params.versionId
      }, '*');
    }

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [params.versionId]);

  const handleConsentChange = (consent: boolean) => {
    // Notify parent window about consent change
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'consent-changed',
        versionId: params.versionId,
        consent,
        timestamp: new Date().toISOString()
      }, '*');
    }
  };

  if (!tenantId) {
    return (
      <div className="p-4 text-center">
        <p className="text-red-500">Tenant ID is required</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-transparent">
      <div className="p-4">
        <ConsentWidget
          documentVersionId={params.versionId}
          tenantId={tenantId}
          config={config}
          onConsentChange={handleConsentChange}
        />
      </div>
    </div>
  );
}

'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { supabaseUtils } from '@/lib/supabase';

export function SupabaseStatus() {
  const isConfigured = supabaseUtils.isConfigured();

  // For now, always show mock data mode until database is set up
  const usingMockData = true;

  if (isConfigured) {
    return (
      <Alert className="mb-4 border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
              Supabase Connected
            </Badge>
            <span>Using real database for data storage</span>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Alert className="mb-4 border-amber-200 bg-amber-50">
      <AlertTriangle className="h-4 w-4 text-amber-600" />
      <AlertDescription className="text-amber-800">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300">
              Mock Data Mode
            </Badge>
            <span>{isConfigured ? 'Supabase configured but using demo data' : 'Supabase not configured - using demo data'}</span>
          </div>
          <div className="text-sm">
            <p className="font-medium mb-1">To connect your Supabase database:</p>
            <ol className="list-decimal list-inside space-y-1 text-xs">
              <li>Set up your Supabase project</li>
              <li>Update your <code className="bg-amber-100 px-1 rounded">.env.local</code> file</li>
              <li>Run the database migrations</li>
              <li>Restart your development server</li>
            </ol>
            <p className="mt-2 text-xs">
              📖 See <code className="bg-amber-100 px-1 rounded">SUPABASE_SETUP.md</code> for detailed instructions
            </p>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}

export function SupabaseStatusBadge() {
  const isConfigured = supabaseUtils.isConfigured();
  const usingMockData = false; // Now using real Supabase data

  return (
    <Badge
      variant="outline"
      className="bg-amber-100 text-amber-800 border-amber-300"
    >
      <XCircle className="h-3 w-3 mr-1" />
      Mock Data
    </Badge>
  );
}

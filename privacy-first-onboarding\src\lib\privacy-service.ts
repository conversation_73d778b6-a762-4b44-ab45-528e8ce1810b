import { 
  PrivacyConsent, 
  UserPrivacyProfile, 
  TenantConfiguration,
  OnboardingFormData,
  DataProcessingActivity 
} from './privacy-types';
import { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from './utils';

// Mock data storage - In production, this would connect to your database
class PrivacyService {
  private consents: Map<string, PrivacyConsent[]> = new Map();
  private userProfiles: Map<string, UserPrivacyProfile> = new Map();
  private tenantConfigs: Map<string, TenantConfiguration> = new Map();
  private processingActivities: DataProcessingActivity[] = [];

  constructor() {
    this.initializeDefaultData();
  }

  private initializeDefaultData() {
    // Initialize default tenant configuration
    const defaultTenant: TenantConfiguration = {
      tenantId: 'default',
      name: 'Privacy First Application',
      regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],
      defaultRegion: 'US',
      dataRetentionPolicies: {
        PERSONAL_IDENTIFIERS: 2555, // 7 years
        CONTACT_INFO: 1095, // 3 years
        FINANCIAL_INFO: 2555, // 7 years
        BIOMETRIC_DATA: 365, // 1 year
        LOCATION_DATA: 365, // 1 year
        BEHAVIORAL_DATA: 730, // 2 years
        SENSITIVE_PERSONAL: 365, // 1 year
        HEALTH_DATA: 2555, // 7 years
        EMPLOYMENT_DATA: 2555, // 7 years
        EDUCATION_DATA: 2555, // 7 years
      },
      complianceSettings: {
        requireExplicitConsent: true,
        enableRightToBeForgotten: true,
        enableDataPortability: true,
        enableOptOut: true,
        cookieConsentRequired: true,
        minorProtection: true,
      }
    };

    this.tenantConfigs.set('default', defaultTenant);

    // Initialize default processing activities
    this.processingActivities = [
      {
        id: 'user-account',
        name: 'User Account Management',
        description: 'Creating and managing user accounts',
        dataCategories: ['PERSONAL_IDENTIFIERS', 'CONTACT_INFO'],
        purposes: ['Account Creation', 'Authentication', 'User Support'],
        legalBasis: 'Contract Performance',
        retentionPeriod: 2555, // 7 years
        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],
      },
      {
        id: 'marketing',
        name: 'Marketing Communications',
        description: 'Sending promotional emails and personalized offers',
        dataCategories: ['CONTACT_INFO', 'BEHAVIORAL_DATA'],
        purposes: ['Marketing', 'Personalization'],
        legalBasis: 'Consent',
        retentionPeriod: 1095, // 3 years
        regions: ['US', 'EU', 'UK', 'CA'],
      },
      {
        id: 'analytics',
        name: 'Usage Analytics',
        description: 'Analyzing user behavior to improve our services',
        dataCategories: ['BEHAVIORAL_DATA', 'LOCATION_DATA'],
        purposes: ['Analytics', 'Service Improvement'],
        legalBasis: 'Legitimate Interest',
        retentionPeriod: 730, // 2 years
        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],
      }
    ];
  }

  // User Registration and Onboarding
  async registerUser(formData: OnboardingFormData): Promise<{ userId: string; success: boolean }> {
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create user privacy profile
    const userProfile: UserPrivacyProfile = {
      userId,
      preferredRegion: formData.preferredRegion,
      dataResidencyPreference: formData.dataResidencyPreference,
      consentPreferences: formData.consents.map(consent => ({
        id: `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        dataCategory: consent.dataCategory,
        purpose: consent.purpose,
        granted: consent.granted,
        timestamp: new Date(),
        region: consent.region,
        version: '1.0'
      })),
      communicationPreferences: formData.communicationPreferences,
      rightsExercised: []
    };

    this.userProfiles.set(userId, userProfile);
    this.consents.set(userId, userProfile.consentPreferences);

    return { userId, success: true };
  }

  // Consent Management
  async getConsents(userId: string): Promise<PrivacyConsent[]> {
    return this.consents.get(userId) || [];
  }

  async updateConsent(userId: string, consentId: string, granted: boolean): Promise<boolean> {
    const userConsents = this.consents.get(userId);
    if (!userConsents) return false;

    const consent = userConsents.find(c => c.id === consentId);
    if (!consent) return false;

    consent.granted = granted;
    consent.timestamp = new Date();
    
    return true;
  }

  async grantConsent(userId: string, dataCategory: keyof typeof DATA_CATEGORIES, purpose: string, region: keyof typeof PRIVACY_REGIONS): Promise<string> {
    const consentId = `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const consent: PrivacyConsent = {
      id: consentId,
      userId,
      dataCategory,
      purpose,
      granted: true,
      timestamp: new Date(),
      region,
      version: '1.0'
    };

    const userConsents = this.consents.get(userId) || [];
    userConsents.push(consent);
    this.consents.set(userId, userConsents);

    return consentId;
  }

  // User Rights Management
  async exerciseUserRight(userId: string, right: keyof typeof USER_RIGHTS): Promise<string> {
    const userProfile = this.userProfiles.get(userId);
    if (!userProfile) throw new Error('User not found');

    const requestId = `request_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    userProfile.rightsExercised.push({
      right,
      requestedAt: new Date(),
      status: 'PENDING'
    });

    this.userProfiles.set(userId, userProfile);
    
    return requestId;
  }

  // Data Export (Right to Portability)
  async exportUserData(userId: string): Promise<any> {
    const userProfile = this.userProfiles.get(userId);
    const userConsents = this.consents.get(userId);
    
    if (!userProfile) throw new Error('User not found');

    return {
      profile: userProfile,
      consents: userConsents,
      exportedAt: new Date().toISOString(),
      format: 'JSON'
    };
  }

  // Data Deletion (Right to be Forgotten)
  async deleteUserData(userId: string): Promise<boolean> {
    const deleted = this.userProfiles.delete(userId) && this.consents.delete(userId);
    return deleted;
  }

  // Region and Compliance Utilities
  async getUserRegion(userId: string): Promise<keyof typeof PRIVACY_REGIONS | null> {
    const userProfile = this.userProfiles.get(userId);
    return userProfile?.preferredRegion || null;
  }

  async getApplicableProcessingActivities(region: keyof typeof PRIVACY_REGIONS): Promise<DataProcessingActivity[]> {
    return this.processingActivities.filter(activity => 
      activity.regions.includes(region)
    );
  }

  // Geolocation-based region detection (mock implementation)
  async detectRegionFromIP(ipAddress: string): Promise<keyof typeof PRIVACY_REGIONS> {
    // In production, use a geolocation service
    // For demo purposes, return US as default
    return 'US';
  }

  // Tenant Configuration
  async getTenantConfig(tenantId: string = 'default'): Promise<TenantConfiguration | null> {
    return this.tenantConfigs.get(tenantId) || null;
  }

  // Privacy Dashboard Data
  async getPrivacyDashboardData(userId: string): Promise<{
    profile: UserPrivacyProfile | null;
    consents: PrivacyConsent[];
    applicableLaws: string[];
    dataRetentionInfo: any;
  }> {
    const profile = this.userProfiles.get(userId);
    const consents = this.consents.get(userId) || [];
    
    if (!profile) {
      return {
        profile: null,
        consents: [],
        applicableLaws: [],
        dataRetentionInfo: {}
      };
    }

    const applicableLaws = PRIVACY_REGIONS[profile.preferredRegion].laws;
    const dataRetentionInfo = this.getDataRetentionInfo(profile.preferredRegion);

    return {
      profile,
      consents,
      applicableLaws,
      dataRetentionInfo
    };
  }

  private getDataRetentionInfo(region: keyof typeof PRIVACY_REGIONS): any {
    const tenantConfig = this.tenantConfigs.get('default');
    if (!tenantConfig) return {};

    return Object.entries(tenantConfig.dataRetentionPolicies).map(([category, days]) => ({
      category: DATA_CATEGORIES[category as keyof typeof DATA_CATEGORIES],
      retentionPeriod: days,
      retentionPeriodHuman: `${Math.floor((days || 0) / 365)} years, ${(days || 0) % 365} days`
    }));
  }
}

// Export singleton instance
export const privacyService = new PrivacyService();

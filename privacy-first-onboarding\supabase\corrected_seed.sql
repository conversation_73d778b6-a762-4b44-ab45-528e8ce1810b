-- Corrected Seed Data for Privacy-First Application
-- Copy and paste this into your Supabase SQL Editor

-- Insert default tenant
INSERT INTO tenants (id, name, slug, regions, default_region) VALUES 
('550e8400-e29b-41d4-a716-************', 'Privacy First Application', 'default', 
 ARRAY['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'], 'US')
ON CONFLICT (slug) DO NOTHING;

-- Insert tenant privacy configuration
INSERT INTO tenant_privacy_configs (tenant_id, require_explicit_consent, enable_right_to_be_forgotten, 
                                   enable_data_portability, enable_opt_out, cookie_consent_required, minor_protection) VALUES 
('550e8400-e29b-41d4-a716-************', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE)
ON CONFLICT (tenant_id) DO NOTHING;

-- Insert default roles
INSERT INTO roles (name, description, permissions, is_system) VALUES 
('super_admin', 'Super administrator with all permissions', ARRAY['*'], TRUE),
('admin', 'Administrator with most permissions', 
 ARRAY['users:read', 'users:create', 'users:update', 'privacy:read', 'privacy:manage', 
       'consents:read', 'consents:manage', 'audit:read', 'api:read', 'api:write'], TRUE),
('privacy_officer', 'Privacy officer with privacy management permissions',
 ARRAY['users:read', 'privacy:read', 'privacy:manage', 'privacy:export', 'privacy:delete',
       'consents:read', 'consents:manage', 'audit:read'], TRUE),
('user', 'Standard user with basic permissions',
 ARRAY['privacy:read', 'consents:read'], TRUE)
ON CONFLICT (name) DO NOTHING;

-- Insert default data processing activities
INSERT INTO data_processing_activities (tenant_id, name, description, data_categories, purposes, 
                                       legal_basis, retention_period, regions, third_parties, is_active) VALUES 
('550e8400-e29b-41d4-a716-************', 'User Account Management', 'Creating and managing user accounts',
 ARRAY['PERSONAL_IDENTIFIERS', 'CONTACT_INFO'], ARRAY['Account Creation', 'Authentication', 'User Support'],
 'Contract Performance', 2555, ARRAY['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'], ARRAY[]::TEXT[], TRUE),

('550e8400-e29b-41d4-a716-************', 'Marketing Communications', 'Sending promotional emails and personalized offers',
 ARRAY['CONTACT_INFO', 'BEHAVIORAL_DATA'], ARRAY['Marketing', 'Personalization'],
 'Consent', 1095, ARRAY['US', 'EU', 'UK', 'CA'], ARRAY[]::TEXT[], TRUE),

('550e8400-e29b-41d4-a716-************', 'Usage Analytics', 'Analyzing user behavior to improve our services',
 ARRAY['BEHAVIORAL_DATA', 'LOCATION_DATA'], ARRAY['Analytics', 'Service Improvement'],
 'Legitimate Interest', 730, ARRAY['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'], ARRAY[]::TEXT[], TRUE);

-- Insert compliance status records
INSERT INTO compliance_status (tenant_id, region, law, is_compliant, last_assessment, next_assessment, compliance_score, findings) VALUES 
('550e8400-e29b-41d4-a716-************', 'US', 'CCPA', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb),

('550e8400-e29b-41d4-a716-************', 'US', 'CPRA', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb),

('550e8400-e29b-41d4-a716-************', 'EU', 'GDPR', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb),

('550e8400-e29b-41d4-a716-************', 'UK', 'UK GDPR', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb),

('550e8400-e29b-41d4-a716-************', 'UK', 'DPA 2018', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb),

('550e8400-e29b-41d4-a716-************', 'CA', 'PIPEDA', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb),

('550e8400-e29b-41d4-a716-************', 'CA', 'CPPA', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb),

('550e8400-e29b-41d4-a716-************', 'IN', 'DPDP', TRUE, NOW(), NOW() + INTERVAL '1 year', 95,
 '{"strengths": ["Strong consent management", "Comprehensive audit logging"], "improvements": ["Enhanced data minimization", "Automated compliance monitoring"]}'::jsonb);

-- Create a demo user for testing (using proper UUID)
INSERT INTO users (id, tenant_id, email, first_name, last_name, preferred_region, data_residency_preference,
                  marketing_consent, analytics_consent, personalization_consent, third_party_sharing, is_active, email_verified) VALUES 
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Demo', 'User', 'US', 'SAME_REGION',
 FALSE, TRUE, FALSE, FALSE, TRUE, TRUE)
ON CONFLICT (tenant_id, email) DO NOTHING;

-- Assign user role to demo user
INSERT INTO user_roles (user_id, role_id, tenant_id) 
SELECT '550e8400-e29b-41d4-a716-************', r.id, '550e8400-e29b-41d4-a716-************'
FROM roles r WHERE r.name = 'user'
ON CONFLICT (user_id, role_id, tenant_id) DO NOTHING;

-- Create demo consents
INSERT INTO consents (user_id, data_category, purpose, legal_basis, granted, granted_at, region, consent_method) VALUES 
('550e8400-e29b-41d4-a716-************', 'PERSONAL_IDENTIFIERS', 'Account Creation and Management', 'CONTRACT', TRUE, NOW(), 'US', 'WEB_FORM'),
('550e8400-e29b-41d4-a716-************', 'CONTACT_INFO', 'Service Communications', 'CONTRACT', TRUE, NOW(), 'US', 'WEB_FORM'),
('550e8400-e29b-41d4-a716-************', 'BEHAVIORAL_DATA', 'Service Improvement and Analytics', 'LEGITIMATE_INTEREST', TRUE, NOW(), 'US', 'WEB_FORM'),
('550e8400-e29b-41d4-a716-************', 'CONTACT_INFO', 'Marketing Communications', 'CONSENT', FALSE, NULL, 'US', 'WEB_FORM');

-- Create audit log entry for demo user creation
INSERT INTO audit_logs (user_id, event_type, action, resource, resource_id, region, metadata) VALUES 
('550e8400-e29b-41d4-a716-************', 'USER_REGISTRATION', 'CREATE', 'user', '550e8400-e29b-41d4-a716-************', 'US', 
 '{"demo": true, "created_by": "seed_script"}'::jsonb);

-- Create a sample privacy request
INSERT INTO privacy_requests (user_id, request_type, status, description, region, legal_deadline) VALUES 
('550e8400-e29b-41d4-a716-************', 'ACCESS', 'COMPLETED', 'Demo data access request', 'US', NOW() + INTERVAL '45 days');

-- Commit the transaction
COMMIT;

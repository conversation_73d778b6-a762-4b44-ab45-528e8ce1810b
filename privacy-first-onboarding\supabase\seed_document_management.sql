-- Seed data for Document Management System
-- Sample templates and initial configuration

-- Insert default document templates
INSERT INTO document_templates (
    tenant_id, 
    template_type, 
    name, 
    description, 
    template_content, 
    markdown_template,
    compliance_frameworks
) VALUES 
(
    '550e8400-e29b-41d4-a716-************', -- Default tenant ID
    'privacy_policy',
    'Standard Privacy Policy',
    'GDPR and CCPA compliant privacy policy template',
    '{
        "company_name": "{{company_name}}",
        "contact_email": "{{contact_email}}",
        "data_categories": ["personal_identifiers", "contact_info", "behavioral_data"],
        "retention_period": "{{retention_period}}",
        "legal_basis": "{{legal_basis}}"
    }',
    '# Privacy Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Information We Collect

We collect the following types of personal information:
- Personal identifiers (name, email address)
- Contact information
- Behavioral data and usage analytics

## 2. How We Use Your Information

We use your personal information for:
- Providing and improving our services
- Communication and customer support
- Legal compliance and fraud prevention

## 3. Data Retention

We retain your personal information for {{retention_period}} or as required by law.

## 4. Your Rights

Under GDPR and CCPA, you have the right to:
- Access your personal data
- Correct inaccurate data
- Delete your data
- Data portability
- Opt-out of data processing

## 5. Contact Information

For privacy-related questions, contact us at: {{contact_email}}

## 6. Legal Basis

Our legal basis for processing: {{legal_basis}}',
    ARRAY['GDPR', 'CCPA']
),
(
    '550e8400-e29b-41d4-a716-************',
    'terms_of_service',
    'Standard Terms of Service',
    'Standard terms of service template',
    '{
        "company_name": "{{company_name}}",
        "service_name": "{{service_name}}",
        "governing_law": "{{governing_law}}",
        "contact_email": "{{contact_email}}"
    }',
    '# Terms of Service for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Acceptance of Terms

By using {{service_name}}, you agree to these Terms of Service.

## 2. Description of Service

{{service_name}} provides privacy-first user onboarding and document management services.

## 3. User Responsibilities

You are responsible for:
- Providing accurate information
- Maintaining account security
- Complying with applicable laws

## 4. Prohibited Uses

You may not use our service for:
- Illegal activities
- Spam or harassment
- Unauthorized access attempts

## 5. Termination

We may terminate your account for violation of these terms.

## 6. Governing Law

These terms are governed by {{governing_law}}.

## 7. Contact Information

Questions about these terms? Contact: {{contact_email}}',
    ARRAY['General']
),
(
    '550e8400-e29b-41d4-a716-************',
    'cookie_policy',
    'Cookie Policy',
    'GDPR compliant cookie policy template',
    '{
        "company_name": "{{company_name}}",
        "website_url": "{{website_url}}",
        "contact_email": "{{contact_email}}"
    }',
    '# Cookie Policy for {{company_name}}

**Last Updated:** {{current_date}}

## What Are Cookies

Cookies are small text files stored on your device when you visit {{website_url}}.

## Types of Cookies We Use

### Essential Cookies
Required for basic website functionality.

### Analytics Cookies
Help us understand how visitors use our website.

### Marketing Cookies
Used to deliver relevant advertisements.

## Managing Cookies

You can control cookies through your browser settings.

## Contact Us

Questions about our cookie policy? Contact: {{contact_email}}',
    ARRAY['GDPR', 'ePrivacy']
);

-- Insert default notification preferences
INSERT INTO notification_preferences (
    tenant_id,
    notification_type,
    is_enabled,
    configuration
) VALUES 
(
    '550e8400-e29b-41d4-a716-************',
    'email',
    true,
    '{
        "smtp_host": "smtp.example.com",
        "smtp_port": 587,
        "from_email": "<EMAIL>",
        "from_name": "Privacy First App",
        "subject_template": "{{document_type}} Updated - {{company_name}}",
        "email_template": "Your {{document_type}} has been updated. Please review the changes at: {{document_url}}",
        "include_pdf": true
    }'
),
(
    '550e8400-e29b-41d4-a716-************',
    'webhook',
    false,
    '{
        "webhook_url": "https://api.example.com/webhooks/document-updates",
        "secret_key": "webhook_secret_key",
        "retry_attempts": 3,
        "timeout_seconds": 30
    }'
);

-- Create initial document versions for the templates
INSERT INTO document_versions (
    tenant_id,
    template_id,
    version_number,
    version_type,
    markdown_content,
    generated_data,
    status,
    changelog
)
SELECT 
    dt.tenant_id,
    dt.id,
    '1.0.0',
    'major',
    replace(replace(replace(replace(
        dt.markdown_template,
        '{{company_name}}', 'Privacy First Application'
    ), '{{contact_email}}', '<EMAIL>'
    ), '{{retention_period}}', '2 years'
    ), '{{legal_basis}}', 'Legitimate interest and consent'),
    '{
        "company_name": "Privacy First Application",
        "contact_email": "<EMAIL>",
        "retention_period": "2 years",
        "legal_basis": "Legitimate interest and consent"
    }',
    'published',
    'Initial version of ' || dt.name
FROM document_templates dt
WHERE dt.tenant_id = '550e8400-e29b-41d4-a716-************';

-- Update published_at for the initial versions
UPDATE document_versions 
SET published_at = now() 
WHERE status = 'published' 
AND tenant_id = '550e8400-e29b-41d4-a716-************';

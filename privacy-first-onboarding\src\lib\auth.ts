import { NextAuthOptions } from 'next-auth';
import { PrismaAdapter } from '@auth/prisma-adapter';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import { prisma } from './database';
import { createAuditLog } from './database';

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        tenantSlug: { label: 'Organization', type: 'text' },
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Find tenant
          const tenant = await prisma.tenant.findUnique({
            where: { slug: credentials.tenantSlug || 'default' },
          });

          if (!tenant) {
            return null;
          }

          // Find user
          const user = await prisma.user.findUnique({
            where: {
              tenantId_email: {
                tenantId: tenant.id,
                email: credentials.email,
              },
            },
            include: {
              userRoles: {
                include: {
                  role: true,
                },
              },
            },
          });

          if (!user || !user.hashedPassword) {
            return null;
          }

          // Verify password
          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.hashedPassword
          );

          if (!isPasswordValid) {
            return null;
          }

          // Update last login
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLoginAt: new Date() },
          });

          // Create audit log
          await createAuditLog(
            'USER_LOGIN',
            'READ',
            'user',
            user.id,
            user.id,
            null,
            { loginMethod: 'credentials' },
            { 
              userAgent: req.headers?.['user-agent'],
              ip: req.headers?.['x-forwarded-for'] || req.headers?.['x-real-ip']
            },
            user.preferredRegion,
            req.headers?.['x-forwarded-for'] as string || req.headers?.['x-real-ip'] as string,
            req.headers?.['user-agent'] as string
          );

          return {
            id: user.id,
            email: user.email,
            name: `${user.firstName} ${user.lastName}`,
            image: null,
            tenantId: user.tenantId,
            preferredRegion: user.preferredRegion,
            roles: user.userRoles.map(ur => ur.role.name),
          };
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.tenantId = user.tenantId;
        token.preferredRegion = user.preferredRegion;
        token.roles = user.roles;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.tenantId = token.tenantId as string;
        session.user.preferredRegion = token.preferredRegion as string;
        session.user.roles = token.roles as string[];
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },
  events: {
    async signOut({ token }) {
      if (token?.sub) {
        await createAuditLog(
          'USER_LOGOUT',
          'READ',
          'user',
          token.sub,
          token.sub,
          null,
          { logoutMethod: 'manual' },
          null,
          token.preferredRegion as string
        );
      }
    },
  },
};

// Role-based access control utilities
export async function hasPermission(
  userId: string,
  permission: string,
  tenantId?: string
): Promise<boolean> {
  try {
    const userRoles = await prisma.userRole.findMany({
      where: {
        userId,
        ...(tenantId && { tenantId }),
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      },
      include: {
        role: true,
      },
    });

    return userRoles.some(userRole =>
      userRole.role.permissions.includes(permission) ||
      userRole.role.permissions.includes('*') // Super admin
    );
  } catch (error) {
    console.error('Permission check failed:', error);
    return false;
  }
}

export async function getUserRoles(userId: string, tenantId?: string): Promise<string[]> {
  try {
    const userRoles = await prisma.userRole.findMany({
      where: {
        userId,
        ...(tenantId && { tenantId }),
        OR: [
          { expiresAt: null },
          { expiresAt: { gt: new Date() } },
        ],
      },
      include: {
        role: true,
      },
    });

    return userRoles.map(ur => ur.role.name);
  } catch (error) {
    console.error('Failed to get user roles:', error);
    return [];
  }
}

export async function assignRole(
  userId: string,
  roleName: string,
  tenantId: string,
  grantedBy?: string,
  expiresAt?: Date
): Promise<boolean> {
  try {
    const role = await prisma.role.findUnique({
      where: { name: roleName },
    });

    if (!role) {
      return false;
    }

    await prisma.userRole.create({
      data: {
        userId,
        roleId: role.id,
        tenantId,
        grantedBy,
        expiresAt,
      },
    });

    // Create audit log
    await createAuditLog(
      'ROLE_ASSIGNMENT',
      'CREATE',
      'user_role',
      undefined,
      grantedBy || userId,
      null,
      { userId, roleName, tenantId },
      { grantedBy, expiresAt }
    );

    return true;
  } catch (error) {
    console.error('Failed to assign role:', error);
    return false;
  }
}

export async function revokeRole(
  userId: string,
  roleName: string,
  tenantId: string,
  revokedBy?: string
): Promise<boolean> {
  try {
    const role = await prisma.role.findUnique({
      where: { name: roleName },
    });

    if (!role) {
      return false;
    }

    const deletedRole = await prisma.userRole.deleteMany({
      where: {
        userId,
        roleId: role.id,
        tenantId,
      },
    });

    if (deletedRole.count > 0) {
      // Create audit log
      await createAuditLog(
        'ROLE_REVOCATION',
        'DELETE',
        'user_role',
        undefined,
        revokedBy || userId,
        { userId, roleName, tenantId },
        null,
        { revokedBy }
      );
    }

    return deletedRole.count > 0;
  } catch (error) {
    console.error('Failed to revoke role:', error);
    return false;
  }
}

// Password utilities
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// Default permissions
export const PERMISSIONS = {
  // User management
  'users:read': 'View users',
  'users:create': 'Create users',
  'users:update': 'Update users',
  'users:delete': 'Delete users',
  
  // Privacy management
  'privacy:read': 'View privacy data',
  'privacy:manage': 'Manage privacy settings',
  'privacy:export': 'Export user data',
  'privacy:delete': 'Delete user data',
  
  // Consent management
  'consents:read': 'View consents',
  'consents:manage': 'Manage consents',
  
  // Audit logs
  'audit:read': 'View audit logs',
  
  // System administration
  'system:admin': 'System administration',
  'tenants:manage': 'Manage tenants',
  'roles:manage': 'Manage roles',
  
  // API access
  'api:read': 'API read access',
  'api:write': 'API write access',
  
  // Super admin (all permissions)
  '*': 'All permissions',
} as const;

export type Permission = keyof typeof PERMISSIONS;

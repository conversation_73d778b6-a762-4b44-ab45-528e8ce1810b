'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  Download, 
  Share,
  Clock,
  Users,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { DocumentTemplate, DocumentVersion } from '@/lib/document-types';
import { DocumentManagementService } from '@/lib/document-service';
import { TemplateEditor } from './template-editor';
import { VersionHistory } from './version-history';
import { ConsentAnalytics } from './consent-analytics';

interface DocumentManagementProps {
  tenantId: string;
}

export function DocumentManagement({ tenantId }: DocumentManagementProps) {
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('templates');

  const documentService = new DocumentManagementService(tenantId);

  useEffect(() => {
    loadTemplates();
  }, [tenantId]);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const response = await documentService.getTemplates();
      if (response.success && response.data) {
        setTemplates(response.data);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setShowTemplateEditor(true);
  };

  const handleEditTemplate = (template: DocumentTemplate) => {
    setSelectedTemplate(template);
    setShowTemplateEditor(true);
  };

  const handleViewVersions = (template: DocumentTemplate) => {
    setSelectedTemplate(template);
    setShowVersionHistory(true);
  };

  const getTemplateTypeIcon = (type: string) => {
    switch (type) {
      case 'privacy_policy':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'terms_of_service':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'cookie_policy':
        return <FileText className="h-5 w-5 text-orange-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTemplateTypeName = (type: string) => {
    switch (type) {
      case 'privacy_policy':
        return 'Privacy Policy';
      case 'terms_of_service':
        return 'Terms of Service';
      case 'cookie_policy':
        return 'Cookie Policy';
      case 'data_processing_agreement':
        return 'Data Processing Agreement';
      default:
        return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  if (showTemplateEditor) {
    return (
      <TemplateEditor
        template={selectedTemplate}
        tenantId={tenantId}
        onSave={() => {
          setShowTemplateEditor(false);
          loadTemplates();
        }}
        onCancel={() => setShowTemplateEditor(false)}
      />
    );
  }

  if (showVersionHistory && selectedTemplate) {
    return (
      <VersionHistory
        template={selectedTemplate}
        tenantId={tenantId}
        onBack={() => setShowVersionHistory(false)}
      />
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Document Management</h1>
          <p className="text-muted-foreground">
            Create and manage privacy policies, terms of service, and other legal documents
          </p>
        </div>
        <Button onClick={handleCreateTemplate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Template
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-6">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : templates.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No templates yet</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Create your first document template to get started with automated policy generation.
                </p>
                <Button onClick={handleCreateTemplate}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Template
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {templates.map((template) => (
                <Card key={template.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getTemplateTypeIcon(template.template_type)}
                        <div>
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                          <CardDescription>
                            {getTemplateTypeName(template.template_type)}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge variant={template.is_active ? 'default' : 'secondary'}>
                        {template.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {template.description || 'No description provided'}
                      </p>
                      
                      <div className="flex flex-wrap gap-1">
                        {template.compliance_frameworks.map((framework) => (
                          <Badge key={framework} variant="outline" className="text-xs">
                            {framework}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(template.updated_at).toLocaleDateString()}
                        </div>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewVersions(template)}
                          className="flex-1"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Versions
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditTemplate(template)}
                          className="flex-1"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics">
          <ConsentAnalytics tenantId={tenantId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

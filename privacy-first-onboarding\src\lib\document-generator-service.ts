// Document Generator Service
// Service for managing document generator sessions and generating documents

import { supabaseAdmin } from './supabase';
import { 
  DocumentGeneratorSession, 
  GeneratedDocument, 
  CreateSessionRequest, 
  UpdateSessionRequest, 
  GenerateDocumentRequest,
  ApiResponse,
  DocumentGeneratorType
} from './document-generator-types';

export class DocumentGeneratorService {
  private tenantId: string;

  constructor(tenantId: string) {
    if (!tenantId || tenantId.trim() === '') {
      throw new Error('Tenant ID is required for DocumentGeneratorService');
    }
    this.tenantId = tenantId.trim();
  }

  // Session Management
  async createSession(request: CreateSessionRequest, userId?: string): Promise<ApiResponse<DocumentGeneratorSession>> {
    try {
      // Try to create session in database
      const { data, error } = await supabaseAdmin
        .from('document_generator_sessions')
        .insert([{
          tenant_id: this.tenantId,
          user_id: userId,
          document_type: request.document_type,
          session_name: request.session_name,
          form_data: request.initial_data || {},
          completion_status: 'draft'
        }])
        .select()
        .single();

      if (error) {
        // If table doesn't exist, create mock session
        if (error.code === '42P01') {
          console.log('Using mock session data (table not found)');
          const mockSession: DocumentGeneratorSession = {
            id: `mock-session-${Date.now()}`,
            tenant_id: this.tenantId,
            user_id: userId,
            document_type: request.document_type,
            form_data: request.form_data || {},
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          return { success: true, data: mockSession };
        }
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error creating session:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async updateSession(sessionId: string, request: UpdateSessionRequest): Promise<ApiResponse<DocumentGeneratorSession>> {
    try {
      const { data, error } = await supabaseAdmin
        .from('document_generator_sessions')
        .update({
          form_data: request.form_data,
          completion_status: request.completion_status,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)
        .eq('tenant_id', this.tenantId)
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error updating session:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getSession(sessionId: string): Promise<ApiResponse<DocumentGeneratorSession>> {
    try {
      const { data, error } = await supabaseAdmin
        .from('document_generator_sessions')
        .select('*')
        .eq('id', sessionId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) {
        // If table doesn't exist or session not found, return null
        if (error.code === '42P01' || error.code === 'PGRST116') {
          console.log('Session not found or table missing, will use mock data');
          return { success: false, error: 'Session not found' };
        }
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error getting session:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async getSessions(userId?: string): Promise<ApiResponse<DocumentGeneratorSession[]>> {
    try {
      let query = supabaseAdmin
        .from('document_generator_sessions')
        .select('*')
        .eq('tenant_id', this.tenantId)
        .order('updated_at', { ascending: false });

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) {
        // If table doesn't exist, return empty array
        if (error.code === '42P01') {
          console.log('Using mock sessions data (table not found)');
          return { success: true, data: [] };
        }
        throw error;
      }

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('Error getting sessions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Document Generation
  async generateDocument(request: GenerateDocumentRequest): Promise<ApiResponse<GeneratedDocument>> {
    try {
      // Get session data (this will handle mock data if needed)
      const sessionResponse = await this.getSession(request.session_id);
      if (!sessionResponse.success || !sessionResponse.data) {
        // Create mock session if not found
        const mockSession: DocumentGeneratorSession = {
          id: request.session_id,
          tenant_id: this.tenantId,
          document_type: 'privacy_policy',
          form_data: {
            company_name: 'Example Company',
            website_url: 'https://example.com',
            contact_email: '<EMAIL>'
          },
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        console.log('Using mock session for document generation');
      }

      const session = sessionResponse.data || {
        id: request.session_id,
        tenant_id: this.tenantId,
        document_type: 'privacy_policy',
        form_data: {
          company_name: 'Example Company',
          website_url: 'https://example.com',
          contact_email: '<EMAIL>'
        },
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Generate document content based on type and form data
      const generatedContent = await this.generateContentFromTemplate(
        session.document_type,
        session.form_data
      );

      // Try to save generated document
      try {
        const { data, error } = await supabaseAdmin
          .from('generated_documents')
          .insert([{
            session_id: request.session_id,
            tenant_id: this.tenantId,
            document_type: session.document_type,
            title: request.title,
            generated_content: generatedContent.html,
            markdown_content: generatedContent.markdown,
            status: 'draft'
          }])
          .select()
          .single();

        if (error && error.code === '42P01') {
          // Table doesn't exist, create mock document
          console.log('Using mock document generation (table not found)');
          const mockDocument: GeneratedDocument = {
            id: `mock-doc-${Date.now()}`,
            session_id: request.session_id,
            tenant_id: this.tenantId,
            document_type: session.document_type,
            title: request.title,
            generated_content: generatedContent.html,
            markdown_content: generatedContent.markdown,
            status: 'draft',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          return { success: true, data: mockDocument };
        }

        if (error) throw error;
        return { success: true, data };
      } catch (dbError) {
        // Fallback to mock document
        console.log('Database error, using mock document:', dbError);
        const mockDocument: GeneratedDocument = {
          id: `mock-doc-${Date.now()}`,
          session_id: request.session_id,
          tenant_id: this.tenantId,
          document_type: session.document_type,
          title: request.title,
          generated_content: generatedContent.html,
          markdown_content: generatedContent.markdown,
          status: 'draft',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        return { success: true, data: mockDocument };
      }
    } catch (error) {
      console.error('Error generating document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async generateContentFromTemplate(
    documentType: DocumentGeneratorType, 
    formData: Record<string, any>
  ): Promise<{ html: string; markdown: string }> {
    // Get template for document type
    const template = this.getTemplateForType(documentType);
    
    // Replace template variables with form data
    let content = template;
    Object.entries(formData).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      const replacement = Array.isArray(value) ? value.join(', ') : String(value || '');
      content = content.replace(new RegExp(placeholder, 'g'), replacement);
    });

    // Add current date
    content = content.replace(/{{current_date}}/g, new Date().toLocaleDateString());
    content = content.replace(/{{current_year}}/g, new Date().getFullYear().toString());

    return {
      markdown: content,
      html: this.markdownToHtml(content)
    };
  }

  private getTemplateForType(documentType: DocumentGeneratorType): string {
    const templates = {
      privacy_policy: `# Privacy Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Information We Collect

We collect the following types of personal information:
- Personal identifiers: {{personal_info_types}}
- Usage data and analytics
- Cookies and tracking technologies

## 2. How We Use Your Information

We use your personal information for:
{{data_usage_purposes}}

## 3. Information Sharing
We may share your information with: {{third_party_services}}

## 4. Your Rights

Under GDPR, you have the right to access, correct, delete, and port your data.
Under CCPA, you have the right to know, delete, and opt-out of the sale of your personal information.

## 5. Data Retention
We retain your data for: {{data_retention_period}}

## 6. Contact Information
For privacy questions, contact us at: {{privacy_contact_email}}
Data Protection Officer: {{dpo_contact}}`,

      terms_conditions: `# Terms and Conditions for {{service_name}}

**Last Updated:** {{current_date}}

## 1. Acceptance of Terms
By using {{service_name}}, you agree to these terms and conditions.

## 2. Service Description
{{service_name}} is a {{service_type}} service provided by {{company_name}}.

## 3. User Accounts
- Minimum age requirement: {{minimum_age}} years
- Account termination: {{account_termination_policy}}

## 4. Prohibited Activities
The following activities are prohibited:
{{prohibited_activities}}

## 5. Content Guidelines
{{content_guidelines}}

## 6. Intellectual Property
{{intellectual_property_policy}}

## 7. Limitation of Liability
{{liability_limitations}}

## 8. Disclaimers
{{warranty_disclaimers}}

## 9. Governing Law
These terms are governed by the laws of {{governing_law}}.

## 10. Dispute Resolution
{{dispute_resolution}}

## 11. Contact Information
For questions about these terms, contact: {{contact_email}}`,

      cookie_policy: `# Cookie Policy for {{website_name}}

**Last Updated:** {{current_date}}

## What Are Cookies
Cookies are small text files stored on your device when you visit our website.

## Types of Cookies We Use

### Essential Cookies
These cookies are necessary for the website to function properly.

### Analytics Cookies
We use analytics cookies to understand how visitors interact with our website.

### Marketing Cookies
These cookies are used to deliver relevant advertisements.

### Functional Cookies
These cookies enhance your experience on our website.

## Third-Party Services
- Google Analytics
- Facebook Pixel
- {{other_tracking_services}}

## Managing Cookies
{{cookie_management_instructions}}

## Contact Us
For questions about our cookie policy, contact: {{contact_email}}`,

      eula: `# End User License Agreement for {{software_name}}

**Last Updated:** {{current_date}}

## 1. License Grant
{{company_name}} grants you a limited, non-exclusive license to use {{software_name}}.

## 2. Restrictions
You may not:
{{license_restrictions}}

## 3. Intellectual Property
All rights, title, and interest in {{software_name}} remain with {{company_name}}.

## 4. Termination
This license terminates automatically if you breach these terms.

## 5. Disclaimer
{{software_name}} is provided "as is" without warranties.

## 6. Contact
For questions, contact: {{contact_email}}`,

      acceptable_use: `# Acceptable Use Policy for {{service_name}}

**Last Updated:** {{current_date}}

## 1. Purpose
This policy outlines acceptable use of {{service_name}}.

## 2. Prohibited Activities
{{prohibited_activities}}

## 3. Content Guidelines
{{content_guidelines}}

## 4. Enforcement
Violations may result in account suspension or termination.

## 5. Contact
Report violations to: {{contact_email}}`,

      return_policy: `# Return Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Return Window
Items may be returned within {{return_window}} days.

## 2. Return Conditions
{{return_conditions}}

## 3. Return Process
{{return_process}}

## 4. Refunds
{{refund_policy}}

## 5. Contact
For returns, contact: {{contact_email}}`,

      disclaimer: `# Disclaimer for {{website_name}}

**Last Updated:** {{current_date}}

## 1. General Disclaimer
{{general_disclaimer}}

## 2. Limitation of Liability
{{liability_limitations}}

## 3. No Warranties
{{warranty_disclaimers}}

## 4. Contact
For questions, contact: {{contact_email}}`,

      shipping_policy: `# Shipping Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Shipping Methods
{{shipping_methods}}

## 2. Processing Time
{{processing_time}}

## 3. Shipping Costs
{{shipping_costs}}

## 4. International Shipping
{{international_shipping}}

## 5. Contact
For shipping questions, contact: {{contact_email}}`
    };

    return templates[documentType] || templates.privacy_policy;
  }

  // Get a specific generated document
  async getDocument(documentId: string): Promise<ApiResponse<GeneratedDocument>> {
    try {
      const { data, error } = await supabaseAdmin
        .from('generated_documents')
        .select('*')
        .eq('id', documentId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error getting document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Document not found'
      };
    }
  }

  // Delete a generated document
  async deleteDocument(documentId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabaseAdmin
        .from('generated_documents')
        .delete()
        .eq('id', documentId)
        .eq('tenant_id', this.tenantId);

      if (error) throw error;

      return { success: true };
    } catch (error) {
      console.error('Error deleting document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete document'
      };
    }
  }

  private markdownToHtml(markdown: string): string {
    // Simple markdown to HTML conversion
    return markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      .replace(/\n/gim, '<br>');
  }
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Privacy Framework Utilities\nexport const PRIVACY_REGIONS = {\n  US: { code: 'US', name: 'United States', laws: ['CCPA', 'CPRA'] },\n  EU: { code: 'EU', name: 'European Union', laws: ['GDPR'] },\n  UK: { code: 'UK', name: 'United Kingdom', laws: ['UK GDPR', 'DPA 2018'] },\n  CA: { code: 'CA', name: 'Canada', laws: ['PIPEDA', 'CPPA'] },\n  IN: { code: 'IN', name: 'India', laws: ['DPDP'] },\n  SG: { code: 'SG', name: 'Singapore', laws: ['PDPA'] },\n  JP: { code: 'JP', name: 'Japan', laws: ['APPI'] },\n  AU: { code: 'AU', name: 'Australia', laws: ['Privacy Act'] },\n  BR: { code: 'BR', name: 'Brazil', laws: ['LGPD'] },\n  CN: { code: 'CN', name: 'China', laws: ['PIPL'] },\n} as const;\n\nexport type PrivacyRegion = keyof typeof PRIVACY_REGIONS;\n\nexport const DATA_CATEGORIES = {\n  PERSONAL_IDENTIFIERS: 'Personal Identifiers',\n  CONTACT_INFO: 'Contact Information',\n  FINANCIAL_INFO: 'Financial Information',\n  BIOMETRIC_DATA: 'Biometric Data',\n  LOCATION_DATA: 'Location Data',\n  BEHAVIORAL_DATA: 'Behavioral Data',\n  SENSITIVE_PERSONAL: 'Sensitive Personal Data',\n  HEALTH_DATA: 'Health Data',\n  EMPLOYMENT_DATA: 'Employment Data',\n  EDUCATION_DATA: 'Education Data',\n} as const;\n\nexport type DataCategory = keyof typeof DATA_CATEGORIES;\n\nexport const USER_RIGHTS = {\n  ACCESS: 'Right to Access',\n  CORRECTION: 'Right to Correction',\n  DELETION: 'Right to Deletion',\n  PORTABILITY: 'Right to Data Portability',\n  OPT_OUT: 'Right to Opt-Out',\n  RESTRICT_PROCESSING: 'Right to Restrict Processing',\n  OBJECT: 'Right to Object',\n  NON_DISCRIMINATION: 'Right to Non-Discrimination',\n} as const;\n\nexport type UserRight = keyof typeof USER_RIGHTS;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QAAE,MAAM;QAAM,MAAM;QAAiB,MAAM;YAAC;YAAQ;SAAO;IAAC;IAChE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;SAAO;IAAC;IACzD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;YAAW;SAAW;IAAC;IACxE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;YAAU;SAAO;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAO;IAAC;IACpD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAc;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;SAAO;IAAC;IACjD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;AAClD;AAIO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;AAClB;AAIO,MAAM,cAAc;IACzB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,aAAa;IACb,SAAS;IACT,qBAAqB;IACrB,QAAQ;IACR,oBAAoB;AACtB", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/privacy-service.ts"], "sourcesContent": ["import { \n  PrivacyConsent, \n  UserPrivacyProfile, \n  TenantConfiguration,\n  OnboardingFormData,\n  DataProcessingActivity \n} from './privacy-types';\nimport { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from './utils';\n\n// Mock data storage - In production, this would connect to your database\nclass PrivacyService {\n  private consents: Map<string, PrivacyConsent[]> = new Map();\n  private userProfiles: Map<string, UserPrivacyProfile> = new Map();\n  private tenantConfigs: Map<string, TenantConfiguration> = new Map();\n  private processingActivities: DataProcessingActivity[] = [];\n\n  constructor() {\n    this.initializeDefaultData();\n  }\n\n  private initializeDefaultData() {\n    // Initialize default tenant configuration\n    const defaultTenant: TenantConfiguration = {\n      tenantId: 'default',\n      name: 'Privacy First Application',\n      regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n      defaultRegion: 'US',\n      dataRetentionPolicies: {\n        PERSONAL_IDENTIFIERS: 2555, // 7 years\n        CONTACT_INFO: 1095, // 3 years\n        FINANCIAL_INFO: 2555, // 7 years\n        BIOMETRIC_DATA: 365, // 1 year\n        LOCATION_DATA: 365, // 1 year\n        BEHAVIORAL_DATA: 730, // 2 years\n        SENSITIVE_PERSONAL: 365, // 1 year\n        HEALTH_DATA: 2555, // 7 years\n        EMPLOYMENT_DATA: 2555, // 7 years\n        EDUCATION_DATA: 2555, // 7 years\n      },\n      complianceSettings: {\n        requireExplicitConsent: true,\n        enableRightToBeForgotten: true,\n        enableDataPortability: true,\n        enableOptOut: true,\n        cookieConsentRequired: true,\n        minorProtection: true,\n      }\n    };\n\n    this.tenantConfigs.set('default', defaultTenant);\n\n    // Initialize default processing activities\n    this.processingActivities = [\n      {\n        id: 'user-account',\n        name: 'User Account Management',\n        description: 'Creating and managing user accounts',\n        dataCategories: ['PERSONAL_IDENTIFIERS', 'CONTACT_INFO'],\n        purposes: ['Account Creation', 'Authentication', 'User Support'],\n        legalBasis: 'Contract Performance',\n        retentionPeriod: 2555, // 7 years\n        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n      },\n      {\n        id: 'marketing',\n        name: 'Marketing Communications',\n        description: 'Sending promotional emails and personalized offers',\n        dataCategories: ['CONTACT_INFO', 'BEHAVIORAL_DATA'],\n        purposes: ['Marketing', 'Personalization'],\n        legalBasis: 'Consent',\n        retentionPeriod: 1095, // 3 years\n        regions: ['US', 'EU', 'UK', 'CA'],\n      },\n      {\n        id: 'analytics',\n        name: 'Usage Analytics',\n        description: 'Analyzing user behavior to improve our services',\n        dataCategories: ['BEHAVIORAL_DATA', 'LOCATION_DATA'],\n        purposes: ['Analytics', 'Service Improvement'],\n        legalBasis: 'Legitimate Interest',\n        retentionPeriod: 730, // 2 years\n        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n      }\n    ];\n  }\n\n  // User Registration and Onboarding\n  async registerUser(formData: OnboardingFormData): Promise<{ userId: string; success: boolean }> {\n    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    \n    // Create user privacy profile\n    const userProfile: UserPrivacyProfile = {\n      userId,\n      preferredRegion: formData.preferredRegion,\n      dataResidencyPreference: formData.dataResidencyPreference,\n      consentPreferences: formData.consents.map(consent => ({\n        id: `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        userId,\n        dataCategory: consent.dataCategory,\n        purpose: consent.purpose,\n        granted: consent.granted,\n        timestamp: new Date(),\n        region: consent.region,\n        version: '1.0'\n      })),\n      communicationPreferences: formData.communicationPreferences,\n      rightsExercised: []\n    };\n\n    this.userProfiles.set(userId, userProfile);\n    this.consents.set(userId, userProfile.consentPreferences);\n\n    return { userId, success: true };\n  }\n\n  // Consent Management\n  async getConsents(userId: string): Promise<PrivacyConsent[]> {\n    return this.consents.get(userId) || [];\n  }\n\n  async updateConsent(userId: string, consentId: string, granted: boolean): Promise<boolean> {\n    const userConsents = this.consents.get(userId);\n    if (!userConsents) return false;\n\n    const consent = userConsents.find(c => c.id === consentId);\n    if (!consent) return false;\n\n    consent.granted = granted;\n    consent.timestamp = new Date();\n    \n    return true;\n  }\n\n  async grantConsent(userId: string, dataCategory: keyof typeof DATA_CATEGORIES, purpose: string, region: keyof typeof PRIVACY_REGIONS): Promise<string> {\n    const consentId = `consent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    const consent: PrivacyConsent = {\n      id: consentId,\n      userId,\n      dataCategory,\n      purpose,\n      granted: true,\n      timestamp: new Date(),\n      region,\n      version: '1.0'\n    };\n\n    const userConsents = this.consents.get(userId) || [];\n    userConsents.push(consent);\n    this.consents.set(userId, userConsents);\n\n    return consentId;\n  }\n\n  // User Rights Management\n  async exerciseUserRight(userId: string, right: keyof typeof USER_RIGHTS): Promise<string> {\n    const userProfile = this.userProfiles.get(userId);\n    if (!userProfile) throw new Error('User not found');\n\n    const requestId = `request_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    \n    userProfile.rightsExercised.push({\n      right,\n      requestedAt: new Date(),\n      status: 'PENDING'\n    });\n\n    this.userProfiles.set(userId, userProfile);\n    \n    return requestId;\n  }\n\n  // Data Export (Right to Portability)\n  async exportUserData(userId: string): Promise<any> {\n    const userProfile = this.userProfiles.get(userId);\n    const userConsents = this.consents.get(userId);\n    \n    if (!userProfile) throw new Error('User not found');\n\n    return {\n      profile: userProfile,\n      consents: userConsents,\n      exportedAt: new Date().toISOString(),\n      format: 'JSON'\n    };\n  }\n\n  // Data Deletion (Right to be Forgotten)\n  async deleteUserData(userId: string): Promise<boolean> {\n    const deleted = this.userProfiles.delete(userId) && this.consents.delete(userId);\n    return deleted;\n  }\n\n  // Region and Compliance Utilities\n  async getUserRegion(userId: string): Promise<keyof typeof PRIVACY_REGIONS | null> {\n    const userProfile = this.userProfiles.get(userId);\n    return userProfile?.preferredRegion || null;\n  }\n\n  async getApplicableProcessingActivities(region: keyof typeof PRIVACY_REGIONS): Promise<DataProcessingActivity[]> {\n    return this.processingActivities.filter(activity => \n      activity.regions.includes(region)\n    );\n  }\n\n  // Geolocation-based region detection (mock implementation)\n  async detectRegionFromIP(ipAddress: string): Promise<keyof typeof PRIVACY_REGIONS> {\n    // In production, use a geolocation service\n    // For demo purposes, return US as default\n    return 'US';\n  }\n\n  // Tenant Configuration\n  async getTenantConfig(tenantId: string = 'default'): Promise<TenantConfiguration | null> {\n    return this.tenantConfigs.get(tenantId) || null;\n  }\n\n  // Privacy Dashboard Data\n  async getPrivacyDashboardData(userId: string): Promise<{\n    profile: UserPrivacyProfile | null;\n    consents: PrivacyConsent[];\n    applicableLaws: string[];\n    dataRetentionInfo: any;\n  }> {\n    let profile = this.userProfiles.get(userId);\n    let consents = this.consents.get(userId) || [];\n\n    // If no profile exists and this looks like a demo user ID, create a demo profile\n    if (!profile && (userId.startsWith('user_') || userId === '550e8400-e29b-41d4-a716-************')) {\n      console.log('Creating demo profile for user:', userId);\n\n      // Create a demo profile\n      profile = {\n        userId: userId,\n        preferredRegion: 'US',\n        dataResidencyPreference: 'SAME_REGION',\n        consentPreferences: [\n          {\n            id: `consent_${Date.now()}_demo1`,\n            userId: userId,\n            dataCategory: 'PERSONAL_IDENTIFIERS',\n            purpose: 'Account Creation and Management',\n            granted: true,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          },\n          {\n            id: `consent_${Date.now()}_demo2`,\n            userId: userId,\n            dataCategory: 'CONTACT_INFO',\n            purpose: 'Service Communications',\n            granted: true,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          },\n          {\n            id: `consent_${Date.now()}_demo3`,\n            userId: userId,\n            dataCategory: 'BEHAVIORAL_DATA',\n            purpose: 'Service Improvement and Analytics',\n            granted: true,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          },\n          {\n            id: `consent_${Date.now()}_demo4`,\n            userId: userId,\n            dataCategory: 'CONTACT_INFO',\n            purpose: 'Marketing Communications',\n            granted: false,\n            timestamp: new Date(),\n            region: 'US',\n            version: '1.0'\n          }\n        ],\n        communicationPreferences: {\n          marketing: false,\n          analytics: true,\n          personalization: false,\n          thirdPartySharing: false,\n        },\n        rightsExercised: []\n      };\n\n      // Store the demo profile\n      this.userProfiles.set(userId, profile);\n      this.consents.set(userId, profile.consentPreferences);\n      consents = profile.consentPreferences;\n    }\n\n    if (!profile) {\n      return {\n        profile: null,\n        consents: [],\n        applicableLaws: [],\n        dataRetentionInfo: {}\n      };\n    }\n\n    const applicableLaws = PRIVACY_REGIONS[profile.preferredRegion].laws;\n    const dataRetentionInfo = this.getDataRetentionInfo(profile.preferredRegion);\n\n    return {\n      profile,\n      consents,\n      applicableLaws,\n      dataRetentionInfo\n    };\n  }\n\n  private getDataRetentionInfo(region: keyof typeof PRIVACY_REGIONS): any {\n    const tenantConfig = this.tenantConfigs.get('default');\n    if (!tenantConfig) return {};\n\n    return Object.entries(tenantConfig.dataRetentionPolicies).map(([category, days]) => ({\n      category: DATA_CATEGORIES[category as keyof typeof DATA_CATEGORIES],\n      retentionPeriod: days,\n      retentionPeriodHuman: `${Math.floor((days || 0) / 365)} years, ${(days || 0) % 365} days`\n    }));\n  }\n}\n\n// Export singleton instance\nexport const privacyService = new PrivacyService();\n"], "names": [], "mappings": ";;;AAOA;;AAEA,yEAAyE;AACzE,MAAM;IACI,WAA0C,IAAI,MAAM;IACpD,eAAgD,IAAI,MAAM;IAC1D,gBAAkD,IAAI,MAAM;IAC5D,uBAAiD,EAAE,CAAC;IAE5D,aAAc;QACZ,IAAI,CAAC,qBAAqB;IAC5B;IAEQ,wBAAwB;QAC9B,0CAA0C;QAC1C,MAAM,gBAAqC;YACzC,UAAU;YACV,MAAM;YACN,SAAS;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YAC/D,eAAe;YACf,uBAAuB;gBACrB,sBAAsB;gBACtB,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB;gBAChB,eAAe;gBACf,iBAAiB;gBACjB,oBAAoB;gBACpB,aAAa;gBACb,iBAAiB;gBACjB,gBAAgB;YAClB;YACA,oBAAoB;gBAClB,wBAAwB;gBACxB,0BAA0B;gBAC1B,uBAAuB;gBACvB,cAAc;gBACd,uBAAuB;gBACvB,iBAAiB;YACnB;QACF;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;QAElC,2CAA2C;QAC3C,IAAI,CAAC,oBAAoB,GAAG;YAC1B;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAwB;iBAAe;gBACxD,UAAU;oBAAC;oBAAoB;oBAAkB;iBAAe;gBAChE,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;YACjE;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAgB;iBAAkB;gBACnD,UAAU;oBAAC;oBAAa;iBAAkB;gBAC1C,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;YACnC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAmB;iBAAgB;gBACpD,UAAU;oBAAC;oBAAa;iBAAsB;gBAC9C,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;YACjE;SACD;IACH;IAEA,mCAAmC;IACnC,MAAM,aAAa,QAA4B,EAAiD;QAC9F,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAE9E,8BAA8B;QAC9B,MAAM,cAAkC;YACtC;YACA,iBAAiB,SAAS,eAAe;YACzC,yBAAyB,SAAS,uBAAuB;YACzD,oBAAoB,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;oBACpD,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;oBACtE;oBACA,cAAc,QAAQ,YAAY;oBAClC,SAAS,QAAQ,OAAO;oBACxB,SAAS,QAAQ,OAAO;oBACxB,WAAW,IAAI;oBACf,QAAQ,QAAQ,MAAM;oBACtB,SAAS;gBACX,CAAC;YACD,0BAA0B,SAAS,wBAAwB;YAC3D,iBAAiB,EAAE;QACrB;QAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ;QAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,YAAY,kBAAkB;QAExD,OAAO;YAAE;YAAQ,SAAS;QAAK;IACjC;IAEA,qBAAqB;IACrB,MAAM,YAAY,MAAc,EAA6B;QAC3D,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;IACxC;IAEA,MAAM,cAAc,MAAc,EAAE,SAAiB,EAAE,OAAgB,EAAoB;QACzF,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,OAAO;QAE1B,MAAM,UAAU,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,CAAC,SAAS,OAAO;QAErB,QAAQ,OAAO,GAAG;QAClB,QAAQ,SAAS,GAAG,IAAI;QAExB,OAAO;IACT;IAEA,MAAM,aAAa,MAAc,EAAE,YAA0C,EAAE,OAAe,EAAE,MAAoC,EAAmB;QACrJ,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QACpF,MAAM,UAA0B;YAC9B,IAAI;YACJ;YACA;YACA;YACA,SAAS;YACT,WAAW,IAAI;YACf;YACA,SAAS;QACX;QAEA,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;QACpD,aAAa,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ;QAE1B,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,kBAAkB,MAAc,EAAE,KAA+B,EAAmB;QACxF,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1C,IAAI,CAAC,aAAa,MAAM,IAAI,MAAM;QAElC,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAEpF,YAAY,eAAe,CAAC,IAAI,CAAC;YAC/B;YACA,aAAa,IAAI;YACjB,QAAQ;QACV;QAEA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ;QAE9B,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,eAAe,MAAc,EAAgB;QACjD,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1C,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,aAAa,MAAM,IAAI,MAAM;QAElC,OAAO;YACL,SAAS;YACT,UAAU;YACV,YAAY,IAAI,OAAO,WAAW;YAClC,QAAQ;QACV;IACF;IAEA,wCAAwC;IACxC,MAAM,eAAe,MAAc,EAAoB;QACrD,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACzE,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,cAAc,MAAc,EAAgD;QAChF,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1C,OAAO,aAAa,mBAAmB;IACzC;IAEA,MAAM,kCAAkC,MAAoC,EAAqC;QAC/G,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA,WACtC,SAAS,OAAO,CAAC,QAAQ,CAAC;IAE9B;IAEA,2DAA2D;IAC3D,MAAM,mBAAmB,SAAiB,EAAyC;QACjF,2CAA2C;QAC3C,0CAA0C;QAC1C,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,WAAmB,SAAS,EAAuC;QACvF,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa;IAC7C;IAEA,yBAAyB;IACzB,MAAM,wBAAwB,MAAc,EAKzC;QACD,IAAI,UAAU,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACpC,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE;QAE9C,iFAAiF;QACjF,IAAI,CAAC,WAAW,CAAC,OAAO,UAAU,CAAC,YAAY,WAAW,sCAAsC,GAAG;YACjG,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,wBAAwB;YACxB,UAAU;gBACR,QAAQ;gBACR,iBAAiB;gBACjB,yBAAyB;gBACzB,oBAAoB;oBAClB;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;oBACA;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;oBACA;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;oBACA;wBACE,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,MAAM,CAAC;wBACjC,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;iBACD;gBACD,0BAA0B;oBACxB,WAAW;oBACX,WAAW;oBACX,iBAAiB;oBACjB,mBAAmB;gBACrB;gBACA,iBAAiB,EAAE;YACrB;YAEA,yBAAyB;YACzB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,QAAQ,kBAAkB;YACpD,WAAW,QAAQ,kBAAkB;QACvC;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,SAAS;gBACT,UAAU,EAAE;gBACZ,gBAAgB,EAAE;gBAClB,mBAAmB,CAAC;YACtB;QACF;QAEA,MAAM,iBAAiB,mHAAA,CAAA,kBAAe,CAAC,QAAQ,eAAe,CAAC,CAAC,IAAI;QACpE,MAAM,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,eAAe;QAE3E,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEQ,qBAAqB,MAAoC,EAAO;QACtE,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAC5C,IAAI,CAAC,cAAc,OAAO,CAAC;QAE3B,OAAO,OAAO,OAAO,CAAC,aAAa,qBAAqB,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,KAAK,GAAK,CAAC;gBACnF,UAAU,mHAAA,CAAA,kBAAe,CAAC,SAAyC;gBACnE,iBAAiB;gBACjB,sBAAsB,GAAG,KAAK,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC;YAC3F,CAAC;IACH;AACF;AAGO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/dashboard/privacy-dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Switch } from '@/components/ui/switch';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Progress } from '@/components/ui/progress';\nimport { Separator } from '@/components/ui/separator';\nimport { \n  Shield, \n  Download, \n  Trash2, \n  Edit, \n  Eye, \n  Settings, \n  Globe, \n  Clock,\n  CheckCircle,\n  XCircle,\n  AlertTriangle,\n  Info\n} from 'lucide-react';\nimport { privacyService } from '@/lib/privacy-service';\nimport { supabasePrivacyService } from '@/lib/supabase-privacy-service';\nimport { supabaseUtils } from '@/lib/supabase';\nimport { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from '@/lib/utils';\nimport type { UserPrivacyProfile, PrivacyConsent } from '@/lib/privacy-types';\n\ninterface PrivacyDashboardProps {\n  userId: string;\n}\n\nexport function PrivacyDashboard({ userId }: PrivacyDashboardProps) {\n  const [userProfile, setUserProfile] = useState<UserPrivacyProfile | null>(null);\n  const [consents, setConsents] = useState<PrivacyConsent[]>([]);\n  const [applicableLaws, setApplicableLaws] = useState<string[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('overview');\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [userId]);\n\n  const loadDashboardData = async () => {\n    try {\n      console.log('Loading dashboard data for user:', userId);\n\n      // Always use mock service for now until Supabase database is properly set up\n      console.log('Loading dashboard data using mock service for userId:', userId);\n      let data = await privacyService.getPrivacyDashboardData(userId);\n      console.log('Mock data loaded:', data);\n\n      // If no profile was found, the service should have created a demo profile\n      if (!data.profile) {\n        console.log('No profile found, this should not happen with the updated mock service');\n      }\n\n      if (!data.profile) {\n        // Create a demo user profile for testing\n        const demoFormData = {\n          firstName: 'Demo',\n          lastName: 'User',\n          email: '<EMAIL>',\n          preferredRegion: 'US' as const,\n          dataResidencyPreference: 'SAME_REGION' as const,\n          consents: [\n            {\n              dataCategory: 'PERSONAL_IDENTIFIERS' as const,\n              purpose: 'Account Creation and Management',\n              granted: true,\n              region: 'US' as const,\n            },\n            {\n              dataCategory: 'CONTACT_INFO' as const,\n              purpose: 'Service Communications',\n              granted: true,\n              region: 'US' as const,\n            },\n            {\n              dataCategory: 'BEHAVIORAL_DATA' as const,\n              purpose: 'Service Improvement and Analytics',\n              granted: true,\n              region: 'US' as const,\n            },\n            {\n              dataCategory: 'CONTACT_INFO' as const,\n              purpose: 'Marketing Communications',\n              granted: false,\n              region: 'US' as const,\n            },\n          ],\n          communicationPreferences: {\n            marketing: false,\n            analytics: true,\n            personalization: false,\n            thirdPartySharing: false,\n          },\n          acceptTerms: true,\n          acceptPrivacyPolicy: true,\n          ageVerification: true,\n        };\n\n        // Register the demo user\n        await privacyService.registerUser(demoFormData);\n\n        // Load the data again\n        data = await privacyService.getPrivacyDashboardData(userId);\n      }\n\n      setUserProfile(data.profile);\n      setConsents(data.consents);\n      setApplicableLaws(data.applicableLaws);\n    } catch (error) {\n      console.error('Failed to load dashboard data:', error);\n\n      // Create a fallback demo profile if all else fails\n      const fallbackProfile = {\n        userId: userId,\n        preferredRegion: 'US' as const,\n        dataResidencyPreference: 'SAME_REGION' as const,\n        consentPreferences: [\n          {\n            id: 'demo-consent-1',\n            userId: userId,\n            dataCategory: 'PERSONAL_IDENTIFIERS' as const,\n            purpose: 'Account Creation and Management',\n            granted: true,\n            timestamp: new Date(),\n            region: 'US' as const,\n            version: '1.0',\n          },\n          {\n            id: 'demo-consent-2',\n            userId: userId,\n            dataCategory: 'CONTACT_INFO' as const,\n            purpose: 'Marketing Communications',\n            granted: false,\n            timestamp: new Date(),\n            region: 'US' as const,\n            version: '1.0',\n          },\n        ],\n        communicationPreferences: {\n          marketing: false,\n          analytics: true,\n          personalization: false,\n          thirdPartySharing: false,\n        },\n        rightsExercised: [],\n      };\n\n      setUserProfile(fallbackProfile);\n      setConsents(fallbackProfile.consentPreferences);\n      setApplicableLaws(['CCPA', 'CPRA']);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleConsentToggle = async (consentId: string, granted: boolean) => {\n    try {\n      await privacyService.updateConsent(userId, consentId, granted);\n      await loadDashboardData(); // Refresh data\n    } catch (error) {\n      console.error('Failed to update consent:', error);\n    }\n  };\n\n  const handleExerciseRight = async (right: keyof typeof USER_RIGHTS) => {\n    try {\n      const requestId = await privacyService.exerciseUserRight(userId, right);\n      alert(`Your ${USER_RIGHTS[right]} request has been submitted. Request ID: ${requestId}`);\n      await loadDashboardData(); // Refresh data\n    } catch (error) {\n      console.error('Failed to exercise right:', error);\n    }\n  };\n\n  const handleDownloadData = async () => {\n    try {\n      const userData = await privacyService.exportUserData(userId);\n      const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `privacy-data-${userId}.json`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Failed to download data:', error);\n    }\n  };\n\n  const handleDeleteAccount = async () => {\n    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {\n      try {\n        await privacyService.deleteUserData(userId);\n        alert('Your account and all associated data have been deleted.');\n        window.location.href = '/';\n      } catch (error) {\n        console.error('Failed to delete account:', error);\n      }\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 p-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading your privacy dashboard...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!userProfile) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 p-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          <Alert>\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription>\n              Unable to load your privacy profile. This might be because:\n              <ul className=\"mt-2 ml-4 list-disc text-sm\">\n                <li>The User ID doesn't exist in our system</li>\n                <li>You need to complete the onboarding flow first</li>\n                <li>There's a temporary system issue</li>\n              </ul>\n            </AlertDescription>\n          </Alert>\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\n            <h4 className=\"font-medium text-blue-900 mb-2\">Quick Solutions</h4>\n            <div className=\"text-sm text-blue-700 space-y-2\">\n              <p>1. <strong>Go back to onboarding:</strong> <a href=\"/\" className=\"underline\">Complete the onboarding flow</a> to create a new account</p>\n              <p>2. <strong>Try a demo account:</strong> Go back and click \"Try Demo Account\" button</p>\n              <p>3. <strong>Use a valid User ID:</strong> Make sure your User ID starts with \"user_\"</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const grantedConsents = consents.filter(c => c.granted).length;\n  const totalConsents = consents.length;\n  const consentPercentage = totalConsents > 0 ? (grantedConsents / totalConsents) * 100 : 0;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-8\">\n      <div className=\"max-w-6xl mx-auto space-y-8\">\n        {/* Header */}\n        <div className=\"text-center\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Privacy Dashboard</h1>\n          <p className=\"text-lg text-gray-600\">\n            Manage your data, privacy preferences, and exercise your rights\n          </p>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Globe className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-600\">Data Region</p>\n                  <p className=\"font-semibold\">{PRIVACY_REGIONS[userProfile.preferredRegion].name}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <Shield className=\"h-5 w-5 text-green-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-600\">Privacy Laws</p>\n                  <p className=\"font-semibold\">{applicableLaws.length} applicable</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <CheckCircle className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-600\">Active Consents</p>\n                  <p className=\"font-semibold\">{grantedConsents} of {totalConsents}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 bg-orange-100 rounded-lg\">\n                  <Clock className=\"h-5 w-5 text-orange-600\" />\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-600\">Rights Exercised</p>\n                  <p className=\"font-semibold\">{userProfile.rightsExercised.length}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Main Dashboard */}\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"grid w-full grid-cols-4\">\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"consents\">Consent Management</TabsTrigger>\n            <TabsTrigger value=\"rights\">Your Rights</TabsTrigger>\n            <TabsTrigger value=\"settings\">Settings</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"overview\" className=\"space-y-6\">\n            {/* Privacy Score */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Shield className=\"h-5 w-5\" />\n                  Privacy Health Score\n                </CardTitle>\n                <CardDescription>\n                  Your current privacy configuration and data protection status\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span>Consent Management</span>\n                    <span>{Math.round(consentPercentage)}%</span>\n                  </div>\n                  <Progress value={consentPercentage} className=\"h-2\" />\n                </div>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                  <div className=\"space-y-2\">\n                    <h4 className=\"font-medium\">Data Protection</h4>\n                    <div className=\"space-y-1 text-sm\">\n                      <div className=\"flex items-center gap-2\">\n                        <CheckCircle className=\"h-3 w-3 text-green-600\" />\n                        <span>Data encrypted at rest and in transit</span>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        <CheckCircle className=\"h-3 w-3 text-green-600\" />\n                        <span>Regional data residency enforced</span>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        <CheckCircle className=\"h-3 w-3 text-green-600\" />\n                        <span>Regular security audits performed</span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <h4 className=\"font-medium\">Compliance Status</h4>\n                    <div className=\"space-y-1\">\n                      {applicableLaws.map((law) => (\n                        <Badge key={law} variant=\"outline\" className=\"mr-1\">\n                          {law} Compliant\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Recent Activity */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Recent Privacy Activity</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  {userProfile.rightsExercised.slice(0, 5).map((activity, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"p-1 bg-blue-100 rounded\">\n                          <Settings className=\"h-3 w-3 text-blue-600\" />\n                        </div>\n                        <div>\n                          <p className=\"font-medium\">{USER_RIGHTS[activity.right]}</p>\n                          <p className=\"text-sm text-gray-600\">\n                            {activity.requestedAt.toLocaleDateString()}\n                          </p>\n                        </div>\n                      </div>\n                      <Badge variant={\n                        activity.status === 'COMPLETED' ? 'default' :\n                        activity.status === 'IN_PROGRESS' ? 'secondary' :\n                        activity.status === 'REJECTED' ? 'destructive' : 'outline'\n                      }>\n                        {activity.status}\n                      </Badge>\n                    </div>\n                  ))}\n                  \n                  {userProfile.rightsExercised.length === 0 && (\n                    <p className=\"text-center text-gray-500 py-4\">\n                      No privacy rights have been exercised yet.\n                    </p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"consents\" className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Manage Your Consent Preferences</CardTitle>\n                <CardDescription>\n                  Control how your data is used by toggling consent for different purposes\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                {consents.map((consent) => (\n                  <div key={consent.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex-1\">\n                      <h4 className=\"font-medium\">{consent.purpose}</h4>\n                      <p className=\"text-sm text-gray-600\">\n                        {DATA_CATEGORIES[consent.dataCategory]} • \n                        Last updated: {consent.timestamp.toLocaleDateString()}\n                      </p>\n                    </div>\n                    <div className=\"flex items-center gap-3\">\n                      <Switch\n                        checked={consent.granted}\n                        onCheckedChange={(checked) => handleConsentToggle(consent.id, checked)}\n                      />\n                      {consent.granted ? (\n                        <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                      ) : (\n                        <XCircle className=\"h-4 w-4 text-gray-400\" />\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"rights\" className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Exercise Your Privacy Rights</CardTitle>\n                <CardDescription>\n                  These rights are guaranteed under {applicableLaws.join(', ')} and other applicable privacy laws\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid gap-4\">\n                  <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex items-center gap-3\">\n                      <Eye className=\"h-5 w-5 text-blue-600\" />\n                      <div>\n                        <h4 className=\"font-medium\">Access Your Data</h4>\n                        <p className=\"text-sm text-gray-600\">Download a copy of all your personal data</p>\n                      </div>\n                    </div>\n                    <Button onClick={handleDownloadData} variant=\"outline\">\n                      <Download className=\"h-4 w-4 mr-2\" />\n                      Download\n                    </Button>\n                  </div>\n\n                  <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex items-center gap-3\">\n                      <Edit className=\"h-5 w-5 text-green-600\" />\n                      <div>\n                        <h4 className=\"font-medium\">Correct Your Data</h4>\n                        <p className=\"text-sm text-gray-600\">Request correction of inaccurate information</p>\n                      </div>\n                    </div>\n                    <Button \n                      onClick={() => handleExerciseRight('CORRECTION')} \n                      variant=\"outline\"\n                    >\n                      Request Correction\n                    </Button>\n                  </div>\n\n                  <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex items-center gap-3\">\n                      <Trash2 className=\"h-5 w-5 text-red-600\" />\n                      <div>\n                        <h4 className=\"font-medium\">Delete Your Account</h4>\n                        <p className=\"text-sm text-gray-600\">Permanently delete your account and all data</p>\n                      </div>\n                    </div>\n                    <Button onClick={handleDeleteAccount} variant=\"destructive\">\n                      Delete Account\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"settings\" className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Privacy Settings</CardTitle>\n                <CardDescription>\n                  Configure your privacy preferences and data handling options\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-6\">\n                <div className=\"space-y-4\">\n                  <div>\n                    <h4 className=\"font-medium mb-2\">Data Residency</h4>\n                    <p className=\"text-sm text-gray-600 mb-3\">\n                      Your data is currently stored in: <strong>{PRIVACY_REGIONS[userProfile.preferredRegion].name}</strong>\n                    </p>\n                    <p className=\"text-sm text-gray-600\">\n                      Data residency preference: <strong>{userProfile.dataResidencyPreference.replace('_', ' ')}</strong>\n                    </p>\n                  </div>\n\n                  <Separator />\n\n                  <div>\n                    <h4 className=\"font-medium mb-2\">Communication Preferences</h4>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm\">Marketing Communications</span>\n                        <Switch checked={userProfile.communicationPreferences.marketing} />\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm\">Analytics Data Collection</span>\n                        <Switch checked={userProfile.communicationPreferences.analytics} />\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-sm\">Personalization</span>\n                        <Switch checked={userProfile.communicationPreferences.personalization} />\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAGA;AA5BA;;;;;;;;;;;;;;AAmCO,SAAS,iBAAiB,EAAE,MAAM,EAAyB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,6EAA6E;YAC7E,QAAQ,GAAG,CAAC,yDAAyD;YACrE,IAAI,OAAO,MAAM,gIAAA,CAAA,iBAAc,CAAC,uBAAuB,CAAC;YACxD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,0EAA0E;YAC1E,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,yCAAyC;gBACzC,MAAM,eAAe;oBACnB,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,iBAAiB;oBACjB,yBAAyB;oBACzB,UAAU;wBACR;4BACE,cAAc;4BACd,SAAS;4BACT,SAAS;4BACT,QAAQ;wBACV;wBACA;4BACE,cAAc;4BACd,SAAS;4BACT,SAAS;4BACT,QAAQ;wBACV;wBACA;4BACE,cAAc;4BACd,SAAS;4BACT,SAAS;4BACT,QAAQ;wBACV;wBACA;4BACE,cAAc;4BACd,SAAS;4BACT,SAAS;4BACT,QAAQ;wBACV;qBACD;oBACD,0BAA0B;wBACxB,WAAW;wBACX,WAAW;wBACX,iBAAiB;wBACjB,mBAAmB;oBACrB;oBACA,aAAa;oBACb,qBAAqB;oBACrB,iBAAiB;gBACnB;gBAEA,yBAAyB;gBACzB,MAAM,gIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;gBAElC,sBAAsB;gBACtB,OAAO,MAAM,gIAAA,CAAA,iBAAc,CAAC,uBAAuB,CAAC;YACtD;YAEA,eAAe,KAAK,OAAO;YAC3B,YAAY,KAAK,QAAQ;YACzB,kBAAkB,KAAK,cAAc;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAEhD,mDAAmD;YACnD,MAAM,kBAAkB;gBACtB,QAAQ;gBACR,iBAAiB;gBACjB,yBAAyB;gBACzB,oBAAoB;oBAClB;wBACE,IAAI;wBACJ,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,QAAQ;wBACR,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,WAAW,IAAI;wBACf,QAAQ;wBACR,SAAS;oBACX;iBACD;gBACD,0BAA0B;oBACxB,WAAW;oBACX,WAAW;oBACX,iBAAiB;oBACjB,mBAAmB;gBACrB;gBACA,iBAAiB,EAAE;YACrB;YAEA,eAAe;YACf,YAAY,gBAAgB,kBAAkB;YAC9C,kBAAkB;gBAAC;gBAAQ;aAAO;QACpC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,OAAO,WAAmB;QACpD,IAAI;YACF,MAAM,gIAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,QAAQ,WAAW;YACtD,MAAM,qBAAqB,eAAe;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,YAAY,MAAM,gIAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,QAAQ;YACjE,MAAM,CAAC,KAAK,EAAE,mHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,yCAAyC,EAAE,WAAW;YACvF,MAAM,qBAAqB,eAAe;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,gIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;YACrD,MAAM,OAAO,IAAI,KAAK;gBAAC,KAAK,SAAS,CAAC,UAAU,MAAM;aAAG,EAAE;gBAAE,MAAM;YAAmB;YACtF,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,aAAa,EAAE,OAAO,KAAK,CAAC;YAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,QAAQ,gFAAgF;YAC1F,IAAI;gBACF,MAAM,gIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;gBACpC,MAAM;gBACN,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,iIAAA,CAAA,mBAAgB;;oCAAC;kDAEhB,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAKV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAE;0DAAG,8OAAC;0DAAO;;;;;;4CAA+B;0DAAC,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAY;;;;;;4CAAgC;;;;;;;kDAChH,8OAAC;;4CAAE;0DAAG,8OAAC;0DAAO;;;;;;4CAA4B;;;;;;;kDAC1C,8OAAC;;4CAAE;0DAAG,8OAAC;0DAAO;;;;;;4CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMvD;IAEA,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;IAC9D,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,oBAAoB,gBAAgB,IAAI,AAAC,kBAAkB,gBAAiB,MAAM;IAExF,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAiB,mHAAA,CAAA,kBAAe,CAAC,YAAY,eAAe,CAAC,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMvF,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDAAiB,eAAe,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM5D,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDAAiB;wDAAgB;wDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM3D,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAiB,YAAY,eAAe,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1E,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;;sCACrC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;;;;;;;sCAGhC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CAEtC,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGhC,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;;wEAAM,KAAK,KAAK,CAAC;wEAAmB;;;;;;;;;;;;;sEAEvC,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,OAAO;4DAAmB,WAAU;;;;;;;;;;;;8DAGhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAc;;;;;;8EAC5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,2NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;8FACvB,8OAAC;8FAAK;;;;;;;;;;;;sFAER,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,2NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;8FACvB,8OAAC;8FAAK;;;;;;;;;;;;sFAER,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,2NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;8FACvB,8OAAC;8FAAK;;;;;;;;;;;;;;;;;;;;;;;;sEAKZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAc;;;;;;8EAC5B,8OAAC;oEAAI,WAAU;8EACZ,eAAe,GAAG,CAAC,CAAC,oBACnB,8OAAC,iIAAA,CAAA,QAAK;4EAAW,SAAQ;4EAAU,WAAU;;gFAC1C;gFAAI;;2EADK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWxB,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,YAAY,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACtD,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;;;;;;sFAEtB,8OAAC;;8FACC,8OAAC;oFAAE,WAAU;8FAAe,mHAAA,CAAA,cAAW,CAAC,SAAS,KAAK,CAAC;;;;;;8FACvD,8OAAC;oFAAE,WAAU;8FACV,SAAS,WAAW,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;8EAI9C,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SACL,SAAS,MAAM,KAAK,cAAc,YAClC,SAAS,MAAM,KAAK,gBAAgB,cACpC,SAAS,MAAM,KAAK,aAAa,gBAAgB;8EAEhD,SAAS,MAAM;;;;;;;2DAjBV;;;;;oDAsBX,YAAY,eAAe,CAAC,MAAM,KAAK,mBACtC,8OAAC;wDAAE,WAAU;kEAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASxD,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAe,QAAQ,OAAO;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;;oEACV,mHAAA,CAAA,kBAAe,CAAC,QAAQ,YAAY,CAAC;oEAAC;oEACxB,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;kEAGvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,QAAQ,OAAO;gEACxB,iBAAiB,CAAC,UAAY,oBAAoB,QAAQ,EAAE,EAAE;;;;;;4DAE/D,QAAQ,OAAO,iBACd,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;qFAEvB,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;+CAhBf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;sCAyB5B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;;oDAAC;oDACoB,eAAe,IAAI,CAAC;oDAAM;;;;;;;;;;;;;kDAGjE,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAc;;;;;;sFAC5B,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAGzC,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS;4DAAoB,SAAQ;;8EAC3C,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAKzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAc;;;;;;sFAC5B,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAGzC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,oBAAoB;4DACnC,SAAQ;sEACT;;;;;;;;;;;;8DAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAc;;;;;;sFAC5B,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAGzC,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS;4DAAqB,SAAQ;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAStE,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,8OAAC;4DAAE,WAAU;;gEAA6B;8EACN,8OAAC;8EAAQ,mHAAA,CAAA,kBAAe,CAAC,YAAY,eAAe,CAAC,CAAC,IAAI;;;;;;;;;;;;sEAE9F,8OAAC;4DAAE,WAAU;;gEAAwB;8EACR,8OAAC;8EAAQ,YAAY,uBAAuB,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;8DAIzF,8OAAC,qIAAA,CAAA,YAAS;;;;;8DAEV,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAS,YAAY,wBAAwB,CAAC,SAAS;;;;;;;;;;;;8EAEjE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAS,YAAY,wBAAwB,CAAC,SAAS;;;;;;;;;;;;8EAEjE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAU;;;;;;sFAC1B,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAS,YAAY,wBAAwB,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY7F", "debugId": null}}, {"offset": {"line": 2667, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration with fallbacks\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-key';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured = !!(\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'https://placeholder.supabase.co' &&\n  supabaseAnonKey !== 'placeholder-key' &&\n  supabaseUrl !== 'YOUR_SUPABASE_PROJECT_URL' &&\n  supabaseAnonKey !== 'YOUR_SUPABASE_ANON_KEY'\n);\n\n// Client-side Supabase client (with RLS enabled) - only create if configured\nexport const supabase = isSupabaseConfigured ? createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  db: {\n    schema: 'public'\n  },\n  global: {\n    headers: {\n      'X-Client-Info': 'privacy-first-app'\n    }\n  }\n}) : null;\n\n// Server-side Supabase client (bypasses RLS for admin operations) - only create if configured\nexport const supabaseAdmin = isSupabaseConfigured ? createClient(supabaseUrl, supabaseServiceKey, {\n  auth: {\n    autoRefreshToken: false,\n    persistSession: false\n  },\n  db: {\n    schema: 'public'\n  }\n}) : null;\n\n// Regional Supabase clients for data residency compliance\nexport class SupabaseRegionalManager {\n  private static instance: SupabaseRegionalManager;\n  private regionalClients: Map<string, any> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): SupabaseRegionalManager {\n    if (!SupabaseRegionalManager.instance) {\n      SupabaseRegionalManager.instance = new SupabaseRegionalManager();\n    }\n    return SupabaseRegionalManager.instance;\n  }\n\n  // Get region-specific Supabase client\n  getRegionalClient(region: string) {\n    if (!this.regionalClients.has(region)) {\n      // In production, you would have different Supabase projects for each region\n      const regionalUrls: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_URL_US || supabaseUrl,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_URL_EU || supabaseUrl,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_URL_UK || supabaseUrl,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_URL_CA || supabaseUrl,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_URL_IN || supabaseUrl,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_URL_SG || supabaseUrl,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_URL_JP || supabaseUrl,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_URL_AU || supabaseUrl,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_URL_BR || supabaseUrl,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_URL_CN || supabaseUrl,\n      };\n\n      const regionalKeys: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_US || supabaseAnonKey,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_EU || supabaseAnonKey,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_UK || supabaseAnonKey,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CA || supabaseAnonKey,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_IN || supabaseAnonKey,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_SG || supabaseAnonKey,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_JP || supabaseAnonKey,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_AU || supabaseAnonKey,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_BR || supabaseAnonKey,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CN || supabaseAnonKey,\n      };\n\n      const client = createClient(\n        regionalUrls[region] || supabaseUrl,\n        regionalKeys[region] || supabaseAnonKey,\n        {\n          auth: {\n            autoRefreshToken: true,\n            persistSession: true\n          }\n        }\n      );\n\n      this.regionalClients.set(region, client);\n    }\n\n    return this.regionalClients.get(region);\n  }\n}\n\n// Utility functions for Supabase operations\nexport const supabaseUtils = {\n  // Check if Supabase is properly configured\n  isConfigured: () => {\n    return isSupabaseConfigured;\n  },\n\n  // Get current user\n  getCurrentUser: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n  },\n\n  // Sign in with email and password\n  signIn: async (email: string, password: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign up with email and password\n  signUp: async (email: string, password: string, metadata?: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: metadata\n      }\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign out\n  signOut: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n  },\n\n  // Create audit log entry\n  createAuditLog: async (logData: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('audit_logs')\n      .insert([logData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get user's privacy profile\n  getUserPrivacyProfile: async (userId: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('users')\n      .select(`\n        *,\n        consents(*),\n        privacy_requests(*),\n        tenant:tenants(*)\n      `)\n      .eq('id', userId)\n      .single();\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Update user consent\n  updateConsent: async (consentId: string, granted: boolean) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('consents')\n      .update({\n        granted,\n        granted_at: granted ? new Date().toISOString() : null,\n        withdrawn_at: !granted ? new Date().toISOString() : null,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', consentId);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Create privacy request\n  createPrivacyRequest: async (requestData: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('privacy_requests')\n      .insert([requestData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get compliance status\n  getComplianceStatus: async (tenantId: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase\n      .from('compliance_status')\n      .select('*')\n      .eq('tenant_id', tenantId);\n\n    if (error) throw error;\n    return data;\n  }\n};\n\n// Export regional manager instance\nexport const supabaseRegional = SupabaseRegionalManager.getInstance();\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,wCAAwC;AACxC,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAEpE,2CAA2C;AAC3C,MAAM,uBAAuB,CAAC,CAAC,CAC7B,eACA,mBACA,gBAAgB,qCAChB,oBAAoB,qBACpB,gBAAgB,+BAChB,oBAAoB,wBACtB;AAGO,MAAM,WAAW,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACxF,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,gBAAgB,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oBAAoB;IAChG,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;IACA,IAAI;QACF,QAAQ;IACV;AACF;AAGO,MAAM;IACX,OAAe,SAAkC;IACzC,kBAAoC,IAAI,MAAM;IAEtD,aAAsB,CAAC;IAEvB,OAAO,cAAuC;QAC5C,IAAI,CAAC,wBAAwB,QAAQ,EAAE;YACrC,wBAAwB,QAAQ,GAAG,IAAI;QACzC;QACA,OAAO,wBAAwB,QAAQ;IACzC;IAEA,sCAAsC;IACtC,kBAAkB,MAAc,EAAE;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS;YACrC,4EAA4E;YAC5E,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;YACnD;YAEA,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;YACxD;YAEA,MAAM,SAAS,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACxB,YAAY,CAAC,OAAO,IAAI,aACxB,YAAY,CAAC,OAAO,IAAI,iBACxB;gBACE,MAAM;oBACJ,kBAAkB;oBAClB,gBAAgB;gBAClB;YACF;YAGF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ;QACnC;QAEA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;AACF;AAGO,MAAM,gBAAgB;IAC3B,2CAA2C;IAC3C,cAAc;QACZ,OAAO;IACT;IAEA,mBAAmB;IACnB,gBAAgB;QACd,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe;QAC5B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe,UAAkB;QAC9C,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,WAAW;IACX,SAAS;QACP,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,yBAAyB;IACzB,gBAAgB,OAAO;QACrB,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,cACL,MAAM,CAAC;YAAC;SAAQ;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,6BAA6B;IAC7B,uBAAuB,OAAO;QAC5B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;MAKT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,sBAAsB;IACtB,eAAe,OAAO,WAAmB;QACvC,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;YACN;YACA,YAAY,UAAU,IAAI,OAAO,WAAW,KAAK;YACjD,cAAc,CAAC,UAAU,IAAI,OAAO,WAAW,KAAK;YACpD,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,yBAAyB;IACzB,sBAAsB,OAAO;QAC3B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAY;QAEvB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,qBAAqB,OAAO;QAC1B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB,wBAAwB,WAAW", "debugId": null}}, {"offset": {"line": 2853, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/supabase-status.tsx"], "sourcesContent": ["'use client';\n\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Badge } from '@/components/ui/badge';\nimport { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';\nimport { supabaseUtils } from '@/lib/supabase';\n\nexport function SupabaseStatus() {\n  const isConfigured = supabaseUtils.isConfigured();\n\n  // For now, always show mock data mode until database is set up\n  const usingMockData = true;\n\n  if (isConfigured && !usingMockData) {\n    return (\n      <Alert className=\"mb-4 border-green-200 bg-green-50\">\n        <CheckCircle className=\"h-4 w-4 text-green-600\" />\n        <AlertDescription className=\"text-green-800\">\n          <div className=\"flex items-center gap-2\">\n            <Badge variant=\"outline\" className=\"bg-green-100 text-green-800 border-green-300\">\n              Supabase Connected\n            </Badge>\n            <span>Using real database for data storage</span>\n          </div>\n        </AlertDescription>\n      </Alert>\n    );\n  }\n\n  return (\n    <Alert className=\"mb-4 border-amber-200 bg-amber-50\">\n      <AlertTriangle className=\"h-4 w-4 text-amber-600\" />\n      <AlertDescription className=\"text-amber-800\">\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center gap-2\">\n            <Badge variant=\"outline\" className=\"bg-amber-100 text-amber-800 border-amber-300\">\n              Mock Data Mode\n            </Badge>\n            <span>{isConfigured ? 'Supabase configured but using demo data' : 'Supabase not configured - using demo data'}</span>\n          </div>\n          <div className=\"text-sm\">\n            <p className=\"font-medium mb-1\">To connect your Supabase database:</p>\n            <ol className=\"list-decimal list-inside space-y-1 text-xs\">\n              <li>Set up your Supabase project</li>\n              <li>Update your <code className=\"bg-amber-100 px-1 rounded\">.env.local</code> file</li>\n              <li>Run the database migrations</li>\n              <li>Restart your development server</li>\n            </ol>\n            <p className=\"mt-2 text-xs\">\n              📖 See <code className=\"bg-amber-100 px-1 rounded\">SUPABASE_SETUP.md</code> for detailed instructions\n            </p>\n          </div>\n        </div>\n      </AlertDescription>\n    </Alert>\n  );\n}\n\nexport function SupabaseStatusBadge() {\n  const isConfigured = supabaseUtils.isConfigured();\n  const usingMockData = true; // For now, always using mock data\n\n  return (\n    <Badge\n      variant=\"outline\"\n      className=\"bg-amber-100 text-amber-800 border-amber-300\"\n    >\n      <XCircle className=\"h-3 w-3 mr-1\" />\n      Mock Data\n    </Badge>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,eAAe,sHAAA,CAAA,gBAAa,CAAC,YAAY;IAE/C,+DAA+D;IAC/D,MAAM,gBAAgB;IAEtB,uCAAoC;;IAcpC;IAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAU;;0BACf,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC,iIAAA,CAAA,mBAAgB;gBAAC,WAAU;0BAC1B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAA+C;;;;;;8CAGlF,8OAAC;8CAAM,eAAe,4CAA4C;;;;;;;;;;;;sCAEpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAmB;;;;;;8CAChC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;;gDAAG;8DAAY,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;gDAAiB;;;;;;;sDAC7E,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAEN,8OAAC;oCAAE,WAAU;;wCAAe;sDACnB,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;wCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzF;AAEO,SAAS;IACd,MAAM,eAAe,sHAAA,CAAA,gBAAa,CAAC,YAAY;IAC/C,MAAM,gBAAgB,MAAM,kCAAkC;IAE9D,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;;0BAEV,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAiB;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3046, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3072, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3100, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { PrivacyDashboard } from '@/components/dashboard/privacy-dashboard';\nimport { SupabaseStatus } from '@/components/supabase-status';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Shield, AlertTriangle } from 'lucide-react';\n\nexport default function DashboardPage() {\n  const [userId, setUserId] = useState<string>('');\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  // In a real application, you would get the userId from authentication context\n  // For demo purposes, we'll allow manual entry\n  useEffect(() => {\n    // Check if userId is in URL params or localStorage\n    const urlParams = new URLSearchParams(window.location.search);\n    const userIdFromUrl = urlParams.get('userId');\n    const userIdFromStorage = localStorage.getItem('demoUserId');\n    \n    if (userIdFromUrl) {\n      setUserId(userIdFromUrl);\n      setIsAuthenticated(true);\n      localStorage.setItem('demoUserId', userIdFromUrl);\n    } else if (userIdFromStorage) {\n      setUserId(userIdFromStorage);\n      setIsAuthenticated(true);\n    }\n  }, []);\n\n  const handleLogin = () => {\n    const currentUserId = userId.trim();\n    if (!currentUserId) {\n      setError('Please enter a valid User ID');\n      return;\n    }\n\n    // Basic validation - in real app, this would be proper authentication\n    if (currentUserId.startsWith('user_') || currentUserId === '550e8400-e29b-41d4-a716-************') {\n      setIsAuthenticated(true);\n      setError('');\n      localStorage.setItem('demoUserId', currentUserId);\n    } else {\n      setError('Invalid User ID format. User ID should start with \"user_\" or use the demo UUID');\n    }\n  };\n\n  const handleLogout = () => {\n    setIsAuthenticated(false);\n    setUserId('');\n    localStorage.removeItem('demoUserId');\n    window.location.href = '/';\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center p-8\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"p-8\">\n            <div className=\"text-center mb-6\">\n              <div className=\"p-3 bg-blue-100 rounded-full w-fit mx-auto mb-4\">\n                <Shield className=\"h-8 w-8 text-blue-600\" />\n              </div>\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n                Privacy Dashboard Access\n              </h1>\n              <p className=\"text-gray-600\">\n                Enter your User ID to access your privacy dashboard\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <Label htmlFor=\"userId\">User ID</Label>\n                <Input\n                  id=\"userId\"\n                  type=\"text\"\n                  placeholder=\"user_1234567890_abcdef123\"\n                  value={userId}\n                  onChange={(e) => setUserId(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && handleLogin()}\n                />\n              </div>\n\n              {error && (\n                <Alert>\n                  <AlertTriangle className=\"h-4 w-4\" />\n                  <AlertDescription>{error}</AlertDescription>\n                </Alert>\n              )}\n\n              <Button onClick={handleLogin} className=\"w-full\">\n                Access Dashboard\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setUserId('550e8400-e29b-41d4-a716-************');\n                  handleLogin();\n                }}\n                className=\"w-full\"\n              >\n                Try Demo Account\n              </Button>\n\n              <div className=\"text-center\">\n                <p className=\"text-sm text-gray-500 mb-2\">\n                  Don't have an account?\n                </p>\n                <Button \n                  variant=\"outline\" \n                  onClick={() => window.location.href = '/'}\n                  className=\"w-full\"\n                >\n                  Create Privacy-First Account\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\n              <h4 className=\"font-medium text-blue-900 mb-2\">Demo Instructions</h4>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p>1. Complete the onboarding flow to get a User ID</p>\n                <p>2. Use that User ID to access this dashboard</p>\n                <p>3. Or use any ID starting with \"user_\" for demo purposes</p>\n                <p>4. Try: <code className=\"bg-blue-100 px-1 rounded\">550e8400-e29b-41d4-a716-************</code></p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Header with logout */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-6xl mx-auto px-8 py-4 flex justify-between items-center\">\n          <div className=\"flex items-center gap-2\">\n            <Shield className=\"h-6 w-6 text-blue-600\" />\n            <span className=\"font-semibold text-gray-900\">Privacy Dashboard</span>\n          </div>\n          <div className=\"flex items-center gap-4\">\n            <span className=\"text-sm text-gray-600\">\n              User: <code className=\"bg-gray-100 px-2 py-1 rounded text-xs\">{userId}</code>\n            </span>\n            <Button variant=\"outline\" size=\"sm\" onClick={handleLogout}>\n              Logout\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Supabase Status */}\n      <SupabaseStatus />\n\n      {/* Dashboard */}\n      <PrivacyDashboard userId={userId} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAVA;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,8EAA8E;IAC9E,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mDAAmD;QACnD,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC5D,MAAM,gBAAgB,UAAU,GAAG,CAAC;QACpC,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAE/C,IAAI,eAAe;YACjB,UAAU;YACV,mBAAmB;YACnB,aAAa,OAAO,CAAC,cAAc;QACrC,OAAO,IAAI,mBAAmB;YAC5B,UAAU;YACV,mBAAmB;QACrB;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,gBAAgB,OAAO,IAAI;QACjC,IAAI,CAAC,eAAe;YAClB,SAAS;YACT;QACF;QAEA,sEAAsE;QACtE,IAAI,cAAc,UAAU,CAAC,YAAY,kBAAkB,wCAAwC;YACjG,mBAAmB;YACnB,SAAS;YACT,aAAa,OAAO,CAAC,cAAc;QACrC,OAAO;YACL,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,mBAAmB;QACnB,UAAU;QACV,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAK/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;gCAI3C,uBACC,8OAAC,iIAAA,CAAA,QAAK;;sDACJ,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC,iIAAA,CAAA,mBAAgB;sDAAE;;;;;;;;;;;;8CAIvB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAa,WAAU;8CAAS;;;;;;8CAIjD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,UAAU;wCACV;oCACF;oCACA,WAAU;8CACX;;;;;;8CAID,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACtC,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;;gDAAE;8DAAQ,8OAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOpE;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAA8B;;;;;;;;;;;;sCAEhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAwB;sDAChC,8OAAC;4CAAK,WAAU;sDAAyC;;;;;;;;;;;;8CAEjE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;8CAAc;;;;;;;;;;;;;;;;;;;;;;;0BAQjE,8OAAC,wIAAA,CAAA,iBAAc;;;;;0BAGf,8OAAC,uJAAA,CAAA,mBAAgB;gBAAC,QAAQ;;;;;;;;;;;;AAGhC", "debugId": null}}]}
# Local Development Environment
# DATABASE_URL="postgresql://postgres:password@localhost:5432/privacy_first_dev"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://sktoyclpuaqpslvnssgg.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNrdG95Y2xwdWFxcHNsdm5zc2dnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NzgxOTksImV4cCI6MjA2NjM1NDE5OX0.vs4SFSfAOqA3qlEh6E2SxjLtkQPLFn1jvjIH3DyCDMg"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNrdG95Y2xwdWFxcHNsdm5zc2dnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDc3ODE5OSwiZXhwIjoyMDY2MzU0MTk5fQ.9BYjJOWtAsr1LgZsaTHTKrlPUja4pD0ZPRfjyId61_4"

# Supabase Database URL (for Prisma)
# Note: You need to replace [YOUR-PASSWORD] with your actual Supabase database password
DATABASE_URL="postgresql://postgres:[Bappa_Moraya21]@db.sktoyclpuaqpslvnssgg.supabase.co:5432/postgres"
DIRECT_URL="postgresql://postgres:[Bappa_Moraya21]@db.sktoyclpuaqpslvnssgg.supabase.co:5432/postgres"

# For demo purposes, all regions use the same local database
DATABASE_URL_US="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_EU="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_UK="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_CA="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_IN="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_SG="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_JP="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_AU="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_BR="postgresql://postgres:password@localhost:5432/privacy_first_dev"

# Development Configuration
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
ENABLE_DATABASE_LOGGING="true"
LOG_LEVEL="debug"

# Local Development Environment
# DATABASE_URL="postgresql://postgres:password@localhost:5432/privacy_first_dev"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="YOUR_SUPABASE_PROJECT_URL"
NEXT_PUBLIC_SUPABASE_ANON_KEY="YOUR_SUPABASE_ANON_KEY"
SUPABASE_SERVICE_ROLE_KEY="YOUR_SUPABASE_SERVICE_ROLE_KEY"

# Supabase Database URL (for Prisma)
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"

# For demo purposes, all regions use the same local database
DATABASE_URL_US="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_EU="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_UK="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_CA="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_IN="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_SG="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_JP="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_AU="postgresql://postgres:password@localhost:5432/privacy_first_dev"
DATABASE_URL_BR="postgresql://postgres:password@localhost:5432/privacy_first_dev"

# Development Configuration
NODE_ENV="development"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
ENABLE_DATABASE_LOGGING="true"
LOG_LEVEL="debug"

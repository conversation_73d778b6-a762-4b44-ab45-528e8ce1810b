// Document Generator Page - Main page for the document generator system
'use client';

import React, { useState } from 'react';
import { DocumentGeneratorHub } from '@/components/document-generator/DocumentGeneratorHub';
import { DocumentGeneratorWizard } from '@/components/document-generator/DocumentGeneratorWizard';
import { DocumentViewer } from '@/components/document-generator/DocumentViewer';
import { DocumentGeneratorType } from '@/lib/document-generator-types';
import { useTenant } from '@/hooks/useTenant';
import { useAuth } from '@/hooks/useAuth';

type ViewMode = 'hub' | 'wizard' | 'viewer';

interface DocumentGeneratorPageState {
  mode: ViewMode;
  documentType?: DocumentGeneratorType;
  sessionId?: string;
  documentId?: string;
}

export default function DocumentGeneratorPage() {
  const { tenant } = useTenant();
  const { user } = useAuth();
  
  const [state, setState] = useState<DocumentGeneratorPageState>({
    mode: 'hub'
  });

  const handleStartGenerator = (documentType: DocumentGeneratorType, sessionId?: string) => {
    setState({
      mode: 'wizard',
      documentType,
      sessionId
    });
  };

  const handleBackToHub = () => {
    setState({
      mode: 'hub'
    });
  };

  const handleDocumentComplete = (documentId: string) => {
    setState({
      mode: 'viewer',
      documentId
    });
  };

  const handleBackToWizard = () => {
    setState({
      mode: 'wizard',
      documentType: state.documentType,
      sessionId: state.sessionId
    });
  };

  if (!tenant) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-4">
          <h2 className="text-xl font-semibold text-gray-900">No Tenant Selected</h2>
          <p className="text-gray-600">Please select a tenant to access the document generator.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {state.mode === 'hub' && (
        <DocumentGeneratorHub
          tenantId={tenant.id}
          userId={user?.id}
          onStartGenerator={handleStartGenerator}
        />
      )}

      {state.mode === 'wizard' && state.documentType && (
        <DocumentGeneratorWizard
          tenantId={tenant.id}
          userId={user?.id}
          documentType={state.documentType}
          sessionId={state.sessionId}
          onBack={handleBackToHub}
          onComplete={handleDocumentComplete}
        />
      )}

      {state.mode === 'viewer' && state.documentId && (
        <DocumentViewer
          tenantId={tenant.id}
          documentId={state.documentId}
          onBack={handleBackToHub}
          onEdit={handleBackToWizard}
        />
      )}
    </div>
  );
}

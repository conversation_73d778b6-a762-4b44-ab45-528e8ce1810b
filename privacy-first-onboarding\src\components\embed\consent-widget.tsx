'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Download,
  ExternalLink,
  Info
} from 'lucide-react';
import { DocumentVersion, ConsentTrackingRequest, EmbedWidgetConfig } from '@/lib/document-types';

interface ConsentWidgetProps {
  documentVersionId: string;
  tenantId: string;
  config?: Partial<EmbedWidgetConfig>;
  onConsentChange?: (consent: boolean) => void;
}

interface ConsentWidgetState {
  document: DocumentVersion | null;
  loading: boolean;
  consented: boolean | null;
  showDocument: boolean;
  error: string | null;
}

export function ConsentWidget({ 
  documentVersionId, 
  tenantId, 
  config = {},
  onConsentChange 
}: ConsentWidgetProps) {
  const [state, setState] = useState<ConsentWidgetState>({
    document: null,
    loading: true,
    consented: null,
    showDocument: false,
    error: null
  });

  const widgetConfig: EmbedWidgetConfig = {
    document_version_id: documentVersionId,
    widget_type: config.widget_type || 'inline',
    styling: {
      width: '100%',
      height: 'auto',
      background_color: '#ffffff',
      text_color: '#374151',
      button_color: '#3b82f6',
      border_radius: '8px',
      ...config.styling
    },
    behavior: {
      auto_show: true,
      show_delay: 0,
      require_scroll: false,
      remember_choice: true,
      ...config.behavior
    },
    content: {
      title: 'Privacy Policy Consent',
      description: 'Please review and accept our privacy policy to continue.',
      accept_text: 'Accept',
      decline_text: 'Decline',
      ...config.content
    }
  };

  useEffect(() => {
    loadDocument();
    checkExistingConsent();
  }, [documentVersionId, tenantId]);

  const loadDocument = async () => {
    try {
      const response = await fetch(`/api/documents/versions/${documentVersionId}?tenantId=${tenantId}`);
      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({ ...prev, document: data.data, loading: false }));
      } else {
        setState(prev => ({ ...prev, error: data.error, loading: false }));
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to load document', 
        loading: false 
      }));
    }
  };

  const checkExistingConsent = () => {
    if (widgetConfig.behavior.remember_choice) {
      const stored = localStorage.getItem(`consent-${documentVersionId}`);
      if (stored) {
        const consent = JSON.parse(stored);
        setState(prev => ({ ...prev, consented: consent.granted }));
      }
    }
  };

  const handleConsent = async (granted: boolean) => {
    try {
      const userIdentifier = getUserIdentifier();
      
      const consentRequest: ConsentTrackingRequest = {
        document_version_id: documentVersionId,
        end_user_identifier: userIdentifier,
        end_user_email: getUserEmail(),
        consent_given: granted,
        consent_method: 'embedded_widget'
      };

      const response = await fetch(`/api/consent/track?tenantId=${tenantId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(consentRequest)
      });

      const data = await response.json();
      
      if (data.success) {
        setState(prev => ({ ...prev, consented: granted }));
        
        // Store consent locally if remember_choice is enabled
        if (widgetConfig.behavior.remember_choice) {
          localStorage.setItem(`consent-${documentVersionId}`, JSON.stringify({
            granted,
            timestamp: new Date().toISOString()
          }));
        }
        
        // Notify parent component
        onConsentChange?.(granted);
      } else {
        setState(prev => ({ ...prev, error: data.error }));
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to record consent' 
      }));
    }
  };

  const getUserIdentifier = (): string => {
    // Try to get user identifier from various sources
    const urlParams = new URLSearchParams(window.location.search);
    const userId = urlParams.get('userId') || 
                   urlParams.get('user_id') || 
                   localStorage.getItem('user_id') ||
                   `anonymous-${Date.now()}`;
    return userId;
  };

  const getUserEmail = (): string | undefined => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('email') || 
           urlParams.get('user_email') || 
           localStorage.getItem('user_email') || 
           undefined;
  };

  const getDocumentTypeName = (type: string) => {
    switch (type) {
      case 'privacy_policy':
        return 'Privacy Policy';
      case 'terms_of_service':
        return 'Terms of Service';
      case 'cookie_policy':
        return 'Cookie Policy';
      default:
        return 'Document';
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'privacy_policy':
        return <Shield className="h-5 w-5 text-blue-500" />;
      case 'terms_of_service':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'cookie_policy':
        return <FileText className="h-5 w-5 text-orange-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  if (state.loading) {
    return (
      <Card style={{ backgroundColor: widgetConfig.styling.background_color }}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (state.error) {
    return (
      <Alert variant="destructive">
        <XCircle className="h-4 w-4" />
        <AlertDescription>{state.error}</AlertDescription>
      </Alert>
    );
  }

  if (!state.document) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>Document not found</AlertDescription>
      </Alert>
    );
  }

  // If user has already consented, show confirmation
  if (state.consented !== null) {
    return (
      <Card style={{ backgroundColor: widgetConfig.styling.background_color }}>
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            {state.consented ? (
              <CheckCircle className="h-6 w-6 text-green-500" />
            ) : (
              <XCircle className="h-6 w-6 text-red-500" />
            )}
            <div className="flex-1">
              <p className="font-medium" style={{ color: widgetConfig.styling.text_color }}>
                {state.consented ? 'Consent Granted' : 'Consent Declined'}
              </p>
              <p className="text-sm text-muted-foreground">
                You have {state.consented ? 'accepted' : 'declined'} our{' '}
                {getDocumentTypeName(state.document.template?.template_type || 'document').toLowerCase()}
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setState(prev => ({ ...prev, consented: null }))}
            >
              Change
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      style={{ 
        backgroundColor: widgetConfig.styling.background_color,
        borderRadius: widgetConfig.styling.border_radius 
      }}
    >
      <CardHeader>
        <CardTitle 
          className="flex items-center gap-2"
          style={{ color: widgetConfig.styling.text_color }}
        >
          {getDocumentTypeIcon(state.document.template?.template_type || 'document')}
          {widgetConfig.content.title}
        </CardTitle>
        <CardDescription style={{ color: widgetConfig.styling.text_color }}>
          {widgetConfig.content.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            v{state.document.version_number}
          </Badge>
          <span className="text-sm text-muted-foreground">
            {getDocumentTypeName(state.document.template?.template_type || 'document')}
          </span>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setState(prev => ({ ...prev, showDocument: !prev.showDocument }))}
          >
            <Eye className="h-3 w-3 mr-1" />
            {state.showDocument ? 'Hide' : 'View'} Document
          </Button>
          
          {state.document.pdf_url && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(state.document.pdf_url, '_blank')}
            >
              <Download className="h-3 w-3 mr-1" />
              PDF
            </Button>
          )}
        </div>

        {state.showDocument && (
          <div className="border rounded p-4 max-h-64 overflow-y-auto">
            <pre className="whitespace-pre-wrap text-xs">
              {state.document.markdown_content}
            </pre>
          </div>
        )}

        <div className="flex gap-3 pt-4">
          <Button
            onClick={() => handleConsent(true)}
            style={{ backgroundColor: widgetConfig.styling.button_color }}
            className="flex-1"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            {widgetConfig.content.accept_text}
          </Button>
          <Button
            variant="outline"
            onClick={() => handleConsent(false)}
            className="flex-1"
          >
            <XCircle className="h-4 w-4 mr-2" />
            {widgetConfig.content.decline_text}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

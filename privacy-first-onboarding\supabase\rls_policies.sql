-- Row Level Security (RLS) Policies for Privacy-First Application
-- These policies ensure users can only access their own data and admins have appropriate access

-- Enable RLS on all tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_privacy_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE consents ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_processing_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_breaches ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's tenant_id
CREATE OR REPLACE FUNCTION get_user_tenant_id()
<PERSON><PERSON>URNS UUID AS $$
BEGIN
  RETURN (
    SELECT tenant_id 
    FROM users 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has specific role
CREATE OR REPLACE FUNCTION user_has_role(role_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() 
    AND r.name = role_name
    AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has permission
CREATE OR REPLACE FUNCTION user_has_permission(permission TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = auth.uid() 
    AND (permission = ANY(r.permissions) OR '*' = ANY(r.permissions))
    AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tenants policies
CREATE POLICY "Users can view their tenant" ON tenants
  FOR SELECT USING (id = get_user_tenant_id());

CREATE POLICY "Admins can manage tenants" ON tenants
  FOR ALL USING (user_has_permission('tenants:manage'));

-- Tenant privacy configs policies
CREATE POLICY "Users can view their tenant config" ON tenant_privacy_configs
  FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage tenant configs" ON tenant_privacy_configs
  FOR ALL USING (user_has_permission('tenants:manage'));

-- Users policies
CREATE POLICY "Users can view their own profile" ON users
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can view users in their tenant" ON users
  FOR SELECT USING (
    user_has_permission('users:read') AND 
    tenant_id = get_user_tenant_id()
  );

CREATE POLICY "Admins can manage users in their tenant" ON users
  FOR ALL USING (
    user_has_permission('users:create') AND 
    tenant_id = get_user_tenant_id()
  );

-- Roles policies
CREATE POLICY "All authenticated users can view roles" ON roles
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage roles" ON roles
  FOR ALL USING (user_has_permission('roles:manage'));

-- User roles policies
CREATE POLICY "Users can view their own roles" ON user_roles
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view user roles in their tenant" ON user_roles
  FOR SELECT USING (
    user_has_permission('users:read') AND 
    tenant_id = get_user_tenant_id()
  );

CREATE POLICY "Admins can manage user roles in their tenant" ON user_roles
  FOR ALL USING (
    user_has_permission('roles:manage') AND 
    tenant_id = get_user_tenant_id()
  );

-- Consents policies
CREATE POLICY "Users can view their own consents" ON consents
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own consents" ON consents
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own consents" ON consents
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Privacy officers can view consents in their tenant" ON consents
  FOR SELECT USING (
    user_has_permission('consents:read') AND 
    user_id IN (
      SELECT id FROM users WHERE tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Privacy officers can manage consents in their tenant" ON consents
  FOR ALL USING (
    user_has_permission('consents:manage') AND 
    user_id IN (
      SELECT id FROM users WHERE tenant_id = get_user_tenant_id()
    )
  );

-- Data processing activities policies
CREATE POLICY "Users can view activities for their tenant" ON data_processing_activities
  FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage activities for their tenant" ON data_processing_activities
  FOR ALL USING (
    user_has_permission('privacy:manage') AND 
    tenant_id = get_user_tenant_id()
  );

-- Privacy requests policies
CREATE POLICY "Users can view their own privacy requests" ON privacy_requests
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create their own privacy requests" ON privacy_requests
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Privacy officers can view requests in their tenant" ON privacy_requests
  FOR SELECT USING (
    user_has_permission('privacy:read') AND 
    user_id IN (
      SELECT id FROM users WHERE tenant_id = get_user_tenant_id()
    )
  );

CREATE POLICY "Privacy officers can manage requests in their tenant" ON privacy_requests
  FOR ALL USING (
    user_has_permission('privacy:manage') AND 
    user_id IN (
      SELECT id FROM users WHERE tenant_id = get_user_tenant_id()
    )
  );

-- Audit logs policies
CREATE POLICY "Users can view their own audit logs" ON audit_logs
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Auditors can view audit logs in their tenant" ON audit_logs
  FOR SELECT USING (
    user_has_permission('audit:read') AND 
    (user_id IN (
      SELECT id FROM users WHERE tenant_id = get_user_tenant_id()
    ) OR user_id IS NULL)
  );

CREATE POLICY "System can insert audit logs" ON audit_logs
  FOR INSERT WITH CHECK (true);

-- Data breaches policies
CREATE POLICY "Admins can view data breaches" ON data_breaches
  FOR SELECT USING (user_has_permission('privacy:read'));

CREATE POLICY "Admins can manage data breaches" ON data_breaches
  FOR ALL USING (user_has_permission('privacy:manage'));

-- Compliance status policies
CREATE POLICY "Users can view compliance status for their tenant" ON compliance_status
  FOR SELECT USING (tenant_id = get_user_tenant_id());

CREATE POLICY "Admins can manage compliance status for their tenant" ON compliance_status
  FOR ALL USING (
    user_has_permission('privacy:manage') AND 
    tenant_id = get_user_tenant_id()
  );

-- API keys policies
CREATE POLICY "Users can view their own API keys" ON api_keys
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own API keys" ON api_keys
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admins can view API keys in their tenant" ON api_keys
  FOR SELECT USING (
    user_has_permission('api:read') AND 
    user_id IN (
      SELECT id FROM users WHERE tenant_id = get_user_tenant_id()
    )
  );

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant permissions to service role (for admin operations)
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;

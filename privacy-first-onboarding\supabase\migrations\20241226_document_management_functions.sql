-- Document Management System Functions
-- Core business logic for document versioning, generation, and publishing

-- Function to calculate next version number
CREATE OR REPLACE FUNCTION calculate_next_version(
    current_version TEXT,
    version_type TEXT
) RETURNS TEXT AS $$
DECLARE
    version_parts TEXT[];
    major_num INTEGER;
    minor_num INTEGER;
    patch_num INTEGER;
BEGIN
    -- Handle null or empty current version
    IF current_version IS NULL OR current_version = '' THEN
        RETURN '1.0.0';
    END IF;
    
    -- Split version into parts
    version_parts := string_to_array(current_version, '.');
    
    -- Extract version numbers
    major_num := COALESCE(version_parts[1]::INTEGER, 0);
    minor_num := COALESCE(version_parts[2]::INTEGER, 0);
    patch_num := COALESCE(version_parts[3]::INTEGER, 0);
    
    -- Calculate new version based on type
    CASE version_type
        WHEN 'major' THEN
            RETURN (major_num + 1) || '.0.0';
        WHEN 'minor' THEN
            RETURN major_num || '.' || (minor_num + 1) || '.0';
        WHEN 'patch' THEN
            RETURN major_num || '.' || minor_num || '.' || (patch_num + 1);
        ELSE
            RETURN major_num || '.' || (minor_num + 1) || '.0';
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Function to process markdown template with data
CREATE OR REPLACE FUNCTION process_markdown_template(
    template_content TEXT,
    template_data JSONB
) RETURNS TEXT AS $$
DECLARE
    processed_content TEXT;
    key TEXT;
    value TEXT;
BEGIN
    processed_content := template_content;
    
    -- Replace template variables with actual data
    FOR key, value IN SELECT * FROM jsonb_each_text(template_data)
    LOOP
        processed_content := replace(processed_content, '{{' || key || '}}', value);
    END LOOP;
    
    -- Replace common template variables
    processed_content := replace(processed_content, '{{current_date}}', CURRENT_DATE::TEXT);
    processed_content := replace(processed_content, '{{current_year}}', EXTRACT(YEAR FROM CURRENT_DATE)::TEXT);
    
    RETURN processed_content;
END;
$$ LANGUAGE plpgsql;

-- Function to create new document version
CREATE OR REPLACE FUNCTION create_document_version(
    p_tenant_id UUID,
    p_template_id UUID,
    p_changes JSONB,
    p_version_type TEXT DEFAULT 'minor',
    p_changelog TEXT DEFAULT NULL,
    p_created_by UUID DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_new_version_id UUID;
    v_current_version TEXT;
    v_new_version TEXT;
    v_template_content TEXT;
    v_processed_content TEXT;
BEGIN
    -- Set tenant context
    PERFORM set_config('app.tenant_id', p_tenant_id::TEXT, true);
    
    -- Get current latest version and template
    SELECT 
        COALESCE(MAX(dv.version_number), '0.0.0'),
        dt.markdown_template
    INTO v_current_version, v_template_content
    FROM document_templates dt
    LEFT JOIN document_versions dv ON dt.id = dv.template_id
    WHERE dt.id = p_template_id
    GROUP BY dt.markdown_template;
    
    -- Calculate new version number
    v_new_version := calculate_next_version(v_current_version, p_version_type);
    
    -- Process template with changes
    v_processed_content := process_markdown_template(v_template_content, p_changes);
    
    -- Create new version
    INSERT INTO document_versions (
        tenant_id, template_id, version_number, version_type,
        markdown_content, generated_data, changelog, status, created_by
    )
    VALUES (
        p_tenant_id, p_template_id, v_new_version, p_version_type,
        v_processed_content, p_changes, p_changelog, 'draft', p_created_by
    )
    RETURNING id INTO v_new_version_id;
    
    -- Log the action
    INSERT INTO document_audit_log (
        tenant_id, action, resource_type, resource_id, user_id, new_values
    ) VALUES (
        p_tenant_id, 'CREATE_VERSION', 'document_version', v_new_version_id, 
        p_created_by, jsonb_build_object('version', v_new_version, 'type', p_version_type)
    );
    
    RETURN v_new_version_id;
END;
$$ LANGUAGE plpgsql;

-- Function to publish document version and notify users
CREATE OR REPLACE FUNCTION publish_document_version(
    p_tenant_id UUID,
    p_version_id UUID,
    p_notify_existing_users BOOLEAN DEFAULT true,
    p_published_by UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    v_affected_users INTEGER := 0;
    v_template_id UUID;
    v_version_number TEXT;
BEGIN
    -- Set tenant context
    PERFORM set_config('app.tenant_id', p_tenant_id::TEXT, true);
    
    -- Get version details
    SELECT template_id, version_number 
    INTO v_template_id, v_version_number
    FROM document_versions 
    WHERE id = p_version_id;
    
    -- Publish the version
    UPDATE document_versions 
    SET status = 'published', published_at = now()
    WHERE id = p_version_id;
    
    -- Archive previous published versions
    UPDATE document_versions 
    SET status = 'archived'
    WHERE template_id = v_template_id
    AND id != p_version_id 
    AND status = 'published';
    
    -- Queue notifications for existing users if requested
    IF p_notify_existing_users THEN
        INSERT INTO notification_queue (tenant_id, document_version_id, end_user_email, notification_type)
        SELECT DISTINCT 
            p_tenant_id, 
            p_version_id, 
            end_user_email,
            'version_update'
        FROM end_user_consents 
        WHERE tenant_id = p_tenant_id 
        AND consent_given = true
        AND document_version_id IN (
            SELECT id FROM document_versions 
            WHERE template_id = v_template_id
        );
        
        GET DIAGNOSTICS v_affected_users = ROW_COUNT;
    END IF;
    
    -- Log the action
    INSERT INTO document_audit_log (
        tenant_id, action, resource_type, resource_id, user_id, new_values
    ) VALUES (
        p_tenant_id, 'PUBLISH_VERSION', 'document_version', p_version_id, 
        p_published_by, jsonb_build_object('version', v_version_number, 'notified_users', v_affected_users)
    );
    
    RETURN jsonb_build_object(
        'success', true,
        'version_id', p_version_id,
        'version_number', v_version_number,
        'notified_users', v_affected_users
    );
END;
$$ LANGUAGE plpgsql;

-- GDPR-specific functions for data subject rights
CREATE OR REPLACE FUNCTION export_user_consent_data(
    p_tenant_id UUID,
    p_user_email TEXT
) RETURNS JSONB AS $$
DECLARE
    v_consent_data JSONB;
BEGIN
    -- Set tenant context
    PERFORM set_config('app.tenant_id', p_tenant_id::TEXT, true);

    SELECT jsonb_agg(
        jsonb_build_object(
            'consent_id', euc.id,
            'document_type', euc.consent_type,
            'consent_given', euc.consent_given,
            'timestamp', euc.consent_timestamp,
            'ip_address', euc.ip_address,
            'user_agent', euc.user_agent,
            'consent_method', euc.consent_method,
            'document_version', dv.version_number,
            'document_name', dt.name
        )
    ) INTO v_consent_data
    FROM end_user_consents euc
    JOIN document_versions dv ON euc.document_version_id = dv.id
    JOIN document_templates dt ON dv.template_id = dt.id
    WHERE euc.end_user_email = p_user_email
    AND euc.tenant_id = p_tenant_id;

    RETURN COALESCE(v_consent_data, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to delete user consent data (Right to be forgotten)
CREATE OR REPLACE FUNCTION delete_user_consent_data(
    p_tenant_id UUID,
    p_user_email TEXT,
    p_deleted_by UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- Set tenant context
    PERFORM set_config('app.tenant_id', p_tenant_id::TEXT, true);

    -- Log the deletion request
    INSERT INTO document_audit_log (
        tenant_id, action, resource_type, resource_id, user_id, old_values
    ) VALUES (
        p_tenant_id, 'DELETE_USER_DATA', 'end_user_consents', gen_random_uuid(),
        p_deleted_by, jsonb_build_object('user_email', p_user_email)
    );

    -- Delete consent records
    DELETE FROM end_user_consents
    WHERE tenant_id = p_tenant_id
    AND end_user_email = p_user_email;

    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    -- Delete notification history
    DELETE FROM notification_history
    WHERE tenant_id = p_tenant_id
    AND end_user_identifier = p_user_email;

    RETURN jsonb_build_object(
        'success', true,
        'deleted_consents', v_deleted_count,
        'user_email', p_user_email
    );
END;
$$ LANGUAGE plpgsql;

module.exports = {

"[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RegionalDatabaseManager": (()=>RegionalDatabaseManager),
    "checkDatabaseConnection": (()=>checkDatabaseConnection),
    "cleanupExpiredData": (()=>cleanupExpiredData),
    "createAuditLog": (()=>createAuditLog),
    "disconnectDatabase": (()=>disconnectDatabase),
    "prisma": (()=>prisma),
    "regionalDb": (()=>regionalDb),
    "seedDatabase": (()=>seedDatabase)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const prisma = globalThis.prisma || new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: ("TURBOPACK compile-time truthy", 1) ? [
        'query',
        'error',
        'warn'
    ] : ("TURBOPACK unreachable", undefined),
    datasources: {
        db: {
            url: process.env.DATABASE_URL
        }
    }
});
if ("TURBOPACK compile-time truthy", 1) {
    globalThis.prisma = prisma;
}
async function checkDatabaseConnection() {
    try {
        await prisma.$queryRaw`SELECT 1`;
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
}
async function disconnectDatabase() {
    await prisma.$disconnect();
}
class RegionalDatabaseManager {
    static instance;
    regionalClients = new Map();
    constructor(){}
    static getInstance() {
        if (!RegionalDatabaseManager.instance) {
            RegionalDatabaseManager.instance = new RegionalDatabaseManager();
        }
        return RegionalDatabaseManager.instance;
    }
    // Get database client for specific region
    getRegionalClient(region) {
        if (!this.regionalClients.has(region)) {
            // In production, this would connect to region-specific database URLs
            const regionalUrl = this.getRegionalDatabaseUrl(region);
            const client = new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
                datasources: {
                    db: {
                        url: regionalUrl
                    }
                },
                log: ("TURBOPACK compile-time truthy", 1) ? [
                    'error'
                ] : ("TURBOPACK unreachable", undefined)
            });
            this.regionalClients.set(region, client);
        }
        return this.regionalClients.get(region);
    }
    // Get region-specific database URL
    getRegionalDatabaseUrl(region) {
        // In production, you would have different database URLs for each region
        const regionalUrls = {
            'US': process.env.DATABASE_URL_US || process.env.DATABASE_URL || '',
            'EU': process.env.DATABASE_URL_EU || process.env.DATABASE_URL || '',
            'UK': process.env.DATABASE_URL_UK || process.env.DATABASE_URL || '',
            'CA': process.env.DATABASE_URL_CA || process.env.DATABASE_URL || '',
            'IN': process.env.DATABASE_URL_IN || process.env.DATABASE_URL || '',
            'SG': process.env.DATABASE_URL_SG || process.env.DATABASE_URL || '',
            'JP': process.env.DATABASE_URL_JP || process.env.DATABASE_URL || '',
            'AU': process.env.DATABASE_URL_AU || process.env.DATABASE_URL || '',
            'BR': process.env.DATABASE_URL_BR || process.env.DATABASE_URL || '',
            'CN': process.env.DATABASE_URL_CN || process.env.DATABASE_URL || ''
        };
        return regionalUrls[region] || process.env.DATABASE_URL || '';
    }
    // Disconnect all regional clients
    async disconnectAll() {
        const disconnectPromises = Array.from(this.regionalClients.values()).map((client)=>client.$disconnect());
        await Promise.all(disconnectPromises);
        this.regionalClients.clear();
    }
}
async function createAuditLog(eventType, action, resource, resourceId, userId, oldValues, newValues, metadata, region, ipAddress, userAgent) {
    try {
        const client = region ? RegionalDatabaseManager.getInstance().getRegionalClient(region) : prisma;
        await client.auditLog.create({
            data: {
                eventType,
                action,
                resource,
                resourceId,
                userId,
                oldValues: oldValues ? JSON.parse(JSON.stringify(oldValues)) : null,
                newValues: newValues ? JSON.parse(JSON.stringify(newValues)) : null,
                metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
                region,
                ipAddress,
                userAgent
            }
        });
    } catch (error) {
        console.error('Failed to create audit log:', error);
    // Don't throw error to avoid breaking main functionality
    }
}
async function cleanupExpiredData() {
    try {
        console.log('Starting data retention cleanup...');
        // Get all tenants and their retention policies
        const tenants = await prisma.tenant.findMany({
            include: {
                privacyConfig: true
            }
        });
        for (const tenant of tenants){
            if (!tenant.privacyConfig) continue;
            const config = tenant.privacyConfig;
            const now = new Date();
            // Clean up expired consents
            const expiredConsentsDate = new Date(now.getTime() - config.contactInfoRetention * 24 * 60 * 60 * 1000);
            await prisma.consent.deleteMany({
                where: {
                    user: {
                        tenantId: tenant.id
                    },
                    createdAt: {
                        lt: expiredConsentsDate
                    },
                    granted: false
                }
            });
            // Clean up old audit logs (keep for compliance period)
            const auditRetentionDate = new Date(now.getTime() - 2555 * 24 * 60 * 60 * 1000); // 7 years
            await prisma.auditLog.deleteMany({
                where: {
                    user: {
                        tenantId: tenant.id
                    },
                    timestamp: {
                        lt: auditRetentionDate
                    }
                }
            });
            console.log(`Cleaned up expired data for tenant: ${tenant.name}`);
        }
        console.log('Data retention cleanup completed');
    } catch (error) {
        console.error('Data retention cleanup failed:', error);
    }
}
async function seedDatabase() {
    try {
        console.log('Seeding database...');
        // Create default tenant
        const defaultTenant = await prisma.tenant.upsert({
            where: {
                slug: 'default'
            },
            update: {},
            create: {
                name: 'Privacy First Application',
                slug: 'default',
                regions: [
                    'US',
                    'EU',
                    'UK',
                    'CA',
                    'IN',
                    'SG',
                    'JP',
                    'AU',
                    'BR'
                ],
                defaultRegion: 'US',
                privacyConfig: {
                    create: {
                        requireExplicitConsent: true,
                        enableRightToBeForgotten: true,
                        enableDataPortability: true,
                        enableOptOut: true,
                        cookieConsentRequired: true,
                        minorProtection: true
                    }
                }
            }
        });
        // Create default data processing activities
        const activities = [
            {
                name: 'User Account Management',
                description: 'Creating and managing user accounts',
                dataCategories: [
                    'PERSONAL_IDENTIFIERS',
                    'CONTACT_INFO'
                ],
                purposes: [
                    'Account Creation',
                    'Authentication',
                    'User Support'
                ],
                legalBasis: 'Contract Performance',
                retentionPeriod: 2555,
                regions: [
                    'US',
                    'EU',
                    'UK',
                    'CA',
                    'IN',
                    'SG',
                    'JP',
                    'AU',
                    'BR'
                ],
                thirdParties: []
            },
            {
                name: 'Marketing Communications',
                description: 'Sending promotional emails and personalized offers',
                dataCategories: [
                    'CONTACT_INFO',
                    'BEHAVIORAL_DATA'
                ],
                purposes: [
                    'Marketing',
                    'Personalization'
                ],
                legalBasis: 'Consent',
                retentionPeriod: 1095,
                regions: [
                    'US',
                    'EU',
                    'UK',
                    'CA'
                ],
                thirdParties: []
            },
            {
                name: 'Usage Analytics',
                description: 'Analyzing user behavior to improve our services',
                dataCategories: [
                    'BEHAVIORAL_DATA',
                    'LOCATION_DATA'
                ],
                purposes: [
                    'Analytics',
                    'Service Improvement'
                ],
                legalBasis: 'Legitimate Interest',
                retentionPeriod: 730,
                regions: [
                    'US',
                    'EU',
                    'UK',
                    'CA',
                    'IN',
                    'SG',
                    'JP',
                    'AU',
                    'BR'
                ],
                thirdParties: []
            }
        ];
        for (const activity of activities){
            await prisma.dataProcessingActivity.upsert({
                where: {
                    tenantId_name: {
                        tenantId: defaultTenant.id,
                        name: activity.name
                    }
                },
                update: activity,
                create: {
                    ...activity,
                    tenantId: defaultTenant.id
                }
            });
        }
        // Create compliance status records
        const regions = [
            'US',
            'EU',
            'UK',
            'CA',
            'IN',
            'SG',
            'JP',
            'AU',
            'BR'
        ];
        const laws = {
            'US': [
                'CCPA',
                'CPRA'
            ],
            'EU': [
                'GDPR'
            ],
            'UK': [
                'UK GDPR',
                'DPA 2018'
            ],
            'CA': [
                'PIPEDA',
                'CPPA'
            ],
            'IN': [
                'DPDP'
            ],
            'SG': [
                'PDPA'
            ],
            'JP': [
                'APPI'
            ],
            'AU': [
                'Privacy Act'
            ],
            'BR': [
                'LGPD'
            ]
        };
        for (const region of regions){
            const regionLaws = laws[region] || [];
            for (const law of regionLaws){
                await prisma.complianceStatus.upsert({
                    where: {
                        tenantId_region_law: {
                            tenantId: defaultTenant.id,
                            region,
                            law
                        }
                    },
                    update: {},
                    create: {
                        tenantId: defaultTenant.id,
                        region,
                        law,
                        isCompliant: true,
                        lastAssessment: new Date(),
                        nextAssessment: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
                        complianceScore: 95,
                        findings: {
                            strengths: [
                                'Strong consent management',
                                'Comprehensive audit logging'
                            ],
                            improvements: [
                                'Enhanced data minimization',
                                'Automated compliance monitoring'
                            ]
                        }
                    }
                });
            }
        }
        // Create default roles
        const roles = [
            {
                name: 'super_admin',
                description: 'Super administrator with all permissions',
                permissions: [
                    '*'
                ],
                isSystem: true
            },
            {
                name: 'admin',
                description: 'Administrator with most permissions',
                permissions: [
                    'users:read',
                    'users:create',
                    'users:update',
                    'privacy:read',
                    'privacy:manage',
                    'consents:read',
                    'consents:manage',
                    'audit:read',
                    'api:read',
                    'api:write'
                ],
                isSystem: true
            },
            {
                name: 'privacy_officer',
                description: 'Privacy officer with privacy management permissions',
                permissions: [
                    'users:read',
                    'privacy:read',
                    'privacy:manage',
                    'privacy:export',
                    'privacy:delete',
                    'consents:read',
                    'consents:manage',
                    'audit:read'
                ],
                isSystem: true
            },
            {
                name: 'user',
                description: 'Standard user with basic permissions',
                permissions: [
                    'privacy:read',
                    'consents:read'
                ],
                isSystem: true
            }
        ];
        for (const role of roles){
            await prisma.role.upsert({
                where: {
                    name: role.name
                },
                update: role,
                create: role
            });
        }
        console.log('Database seeded successfully');
    } catch (error) {
        console.error('Database seeding failed:', error);
        throw error;
    }
}
const regionalDb = RegionalDatabaseManager.getInstance();
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PERMISSIONS": (()=>PERMISSIONS),
    "assignRole": (()=>assignRole),
    "authOptions": (()=>authOptions),
    "getUserRoles": (()=>getUserRoles),
    "hasPermission": (()=>hasPermission),
    "hashPassword": (()=>hashPassword),
    "revokeRole": (()=>revokeRole),
    "verifyPassword": (()=>verifyPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/prisma-adapter/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
;
;
const authOptions = {
    adapter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrismaAdapter"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                },
                tenantSlug: {
                    label: 'Organization',
                    type: 'text'
                }
            },
            async authorize (credentials, req) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                try {
                    // Find tenant
                    const tenant = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].tenant.findUnique({
                        where: {
                            slug: credentials.tenantSlug || 'default'
                        }
                    });
                    if (!tenant) {
                        return null;
                    }
                    // Find user
                    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                        where: {
                            tenantId_email: {
                                tenantId: tenant.id,
                                email: credentials.email
                            }
                        },
                        include: {
                            userRoles: {
                                include: {
                                    role: true
                                }
                            }
                        }
                    });
                    if (!user || !user.hashedPassword) {
                        return null;
                    }
                    // Verify password
                    const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.hashedPassword);
                    if (!isPasswordValid) {
                        return null;
                    }
                    // Update last login
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
                        where: {
                            id: user.id
                        },
                        data: {
                            lastLoginAt: new Date()
                        }
                    });
                    // Create audit log
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAuditLog"])('USER_LOGIN', 'READ', 'user', user.id, user.id, null, {
                        loginMethod: 'credentials'
                    }, {
                        userAgent: req.headers?.['user-agent'],
                        ip: req.headers?.['x-forwarded-for'] || req.headers?.['x-real-ip']
                    }, user.preferredRegion, req.headers?.['x-forwarded-for'] || req.headers?.['x-real-ip'], req.headers?.['user-agent']);
                    return {
                        id: user.id,
                        email: user.email,
                        name: `${user.firstName} ${user.lastName}`,
                        image: null,
                        tenantId: user.tenantId,
                        preferredRegion: user.preferredRegion,
                        roles: user.userRoles.map((ur)=>ur.role.name)
                    };
                } catch (error) {
                    console.error('Authentication error:', error);
                    return null;
                }
            }
        })
    ],
    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.tenantId = user.tenantId;
                token.preferredRegion = user.preferredRegion;
                token.roles = user.roles;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.tenantId = token.tenantId;
                session.user.preferredRegion = token.preferredRegion;
                session.user.roles = token.roles;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        signUp: '/auth/signup',
        error: '/auth/error'
    },
    events: {
        async signOut ({ token }) {
            if (token?.sub) {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAuditLog"])('USER_LOGOUT', 'READ', 'user', token.sub, token.sub, null, {
                    logoutMethod: 'manual'
                }, null, token.preferredRegion);
            }
        }
    }
};
async function hasPermission(userId, permission, tenantId) {
    try {
        const userRoles = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].userRole.findMany({
            where: {
                userId,
                ...tenantId && {
                    tenantId
                },
                OR: [
                    {
                        expiresAt: null
                    },
                    {
                        expiresAt: {
                            gt: new Date()
                        }
                    }
                ]
            },
            include: {
                role: true
            }
        });
        return userRoles.some((userRole)=>userRole.role.permissions.includes(permission) || userRole.role.permissions.includes('*') // Super admin
        );
    } catch (error) {
        console.error('Permission check failed:', error);
        return false;
    }
}
async function getUserRoles(userId, tenantId) {
    try {
        const userRoles = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].userRole.findMany({
            where: {
                userId,
                ...tenantId && {
                    tenantId
                },
                OR: [
                    {
                        expiresAt: null
                    },
                    {
                        expiresAt: {
                            gt: new Date()
                        }
                    }
                ]
            },
            include: {
                role: true
            }
        });
        return userRoles.map((ur)=>ur.role.name);
    } catch (error) {
        console.error('Failed to get user roles:', error);
        return [];
    }
}
async function assignRole(userId, roleName, tenantId, grantedBy, expiresAt) {
    try {
        const role = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].role.findUnique({
            where: {
                name: roleName
            }
        });
        if (!role) {
            return false;
        }
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].userRole.create({
            data: {
                userId,
                roleId: role.id,
                tenantId,
                grantedBy,
                expiresAt
            }
        });
        // Create audit log
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAuditLog"])('ROLE_ASSIGNMENT', 'CREATE', 'user_role', undefined, grantedBy || userId, null, {
            userId,
            roleName,
            tenantId
        }, {
            grantedBy,
            expiresAt
        });
        return true;
    } catch (error) {
        console.error('Failed to assign role:', error);
        return false;
    }
}
async function revokeRole(userId, roleName, tenantId, revokedBy) {
    try {
        const role = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].role.findUnique({
            where: {
                name: roleName
            }
        });
        if (!role) {
            return false;
        }
        const deletedRole = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].userRole.deleteMany({
            where: {
                userId,
                roleId: role.id,
                tenantId
            }
        });
        if (deletedRole.count > 0) {
            // Create audit log
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAuditLog"])('ROLE_REVOCATION', 'DELETE', 'user_role', undefined, revokedBy || userId, {
                userId,
                roleName,
                tenantId
            }, null, {
                revokedBy
            });
        }
        return deletedRole.count > 0;
    } catch (error) {
        console.error('Failed to revoke role:', error);
        return false;
    }
}
async function hashPassword(password) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, 12);
}
async function verifyPassword(password, hash) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hash);
}
const PERMISSIONS = {
    // User management
    'users:read': 'View users',
    'users:create': 'Create users',
    'users:update': 'Update users',
    'users:delete': 'Delete users',
    // Privacy management
    'privacy:read': 'View privacy data',
    'privacy:manage': 'Manage privacy settings',
    'privacy:export': 'Export user data',
    'privacy:delete': 'Delete user data',
    // Consent management
    'consents:read': 'View consents',
    'consents:manage': 'Manage consents',
    // Audit logs
    'audit:read': 'View audit logs',
    // System administration
    'system:admin': 'System administration',
    'tenants:manage': 'Manage tenants',
    'roles:manage': 'Manage roles',
    // API access
    'api:read': 'API read access',
    'api:write': 'API write access',
    // Super admin (all permissions)
    '*': 'All permissions'
};
}}),
"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>handler),
    "POST": (()=>handler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
;
;
const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__43df0ddb._.js.map
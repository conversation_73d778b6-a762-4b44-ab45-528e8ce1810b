{"name": "privacy-first-onboarding", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:cleanup": "tsx src/scripts/cleanup.ts", "postinstall": "prisma generate", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "supabase:setup": "echo 'Please follow SUPABASE_SETUP.md for database setup'", "supabase:types": "supabase gen types typescript --local > src/types/supabase.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@supabase/supabase-js": "^2.50.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "lucide-react": "^0.523.0", "next": "15.3.4", "next-auth": "^4.24.11", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/aria-query": "^5.0.4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/testing-library__jest-dom": "^5.14.9", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}
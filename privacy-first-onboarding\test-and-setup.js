const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const TENANT_ID = '550e8400-e29b-41d4-a716-446655440000';

async function testAndSetup() {
  console.log('🚀 Testing Document Management System Setup...\n');
  console.log(`📊 Configuration:`);
  console.log(`   - Tenant ID: ${TENANT_ID}`);
  console.log(`   - Supabase URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL}`);
  
  // Test if we can access existing tables
  console.log('\n🧪 Testing existing tables...');
  
  // Test users table (should exist from original setup)
  try {
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(1);
    
    if (usersError) {
      console.error('❌ Users table error:', usersError.message);
    } else {
      console.log('✅ Users table accessible:', users.length, 'records found');
    }
  } catch (error) {
    console.error('❌ Users table test failed:', error.message);
  }
  
  // Test tenants table
  try {
    const { data: tenants, error: tenantsError } = await supabase
      .from('tenants')
      .select('id, name')
      .eq('id', TENANT_ID)
      .single();
    
    if (tenantsError) {
      console.error('❌ Tenants table error:', tenantsError.message);
    } else {
      console.log('✅ Tenant found:', tenants.name);
    }
  } catch (error) {
    console.error('❌ Tenants table test failed:', error.message);
  }
  
  // Test if document management tables exist by trying to query them
  console.log('\n🧪 Testing document management tables...');
  
  const tables = [
    'document_templates',
    'document_versions', 
    'end_user_consents',
    'notification_queue',
    'document_audit_log'
  ];
  
  const existingTables = [];
  
  for (const tableName of tables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ ${tableName}: ${error.message}`);
      } else {
        console.log(`✅ ${tableName}: Table exists (${data.length} records)`);
        existingTables.push(tableName);
      }
    } catch (error) {
      console.log(`❌ ${tableName}: ${error.message}`);
    }
  }
  
  console.log(`\n📊 Summary: ${existingTables.length}/${tables.length} tables exist`);
  
  if (existingTables.length === 0) {
    console.log('\n⚠️ No document management tables found.');
    console.log('📋 To create the tables, you need to run the SQL migrations manually in Supabase:');
    console.log('   1. Go to your Supabase dashboard');
    console.log('   2. Navigate to SQL Editor');
    console.log('   3. Run the SQL from: supabase/migrations/20241226_document_management_schema.sql');
    console.log('   4. Then run: supabase/migrations/20241226_document_management_rls.sql');
    console.log('   5. Finally run: supabase/migrations/20241226_document_management_functions.sql');
    return;
  }
  
  // If tables exist, test the Document Management System
  if (existingTables.includes('document_templates')) {
    console.log('\n🧪 Testing Document Management System...');
    
    // Test templates
    try {
      const { data: templates, error } = await supabase
        .from('document_templates')
        .select('*')
        .eq('tenant_id', TENANT_ID);
      
      if (error) throw error;
      
      console.log('✅ Templates found:', templates.length);
      
      if (templates.length > 0) {
        const template = templates[0];
        console.log(`   - ${template.name} (${template.template_type})`);
        
        // Test versions for this template
        if (existingTables.includes('document_versions')) {
          const { data: versions, error: versionError } = await supabase
            .from('document_versions')
            .select('*')
            .eq('template_id', template.id);
          
          if (!versionError) {
            console.log('✅ Versions found:', versions.length);
          }
        }
      }
    } catch (error) {
      console.error('❌ Template test failed:', error.message);
    }
  }
  
  // Test the privacy service integration
  console.log('\n🧪 Testing Privacy Service Integration...');
  
  try {
    // Import and test the document service
    const { DocumentManagementService } = require('./src/lib/document-service.ts');
    const documentService = new DocumentManagementService(TENANT_ID);
    
    const response = await documentService.getTemplates();
    
    if (response.success) {
      console.log('✅ Document service working:', response.data.length, 'templates');
    } else {
      console.error('❌ Document service error:', response.error);
    }
  } catch (error) {
    console.log('⚠️ Document service test skipped (TypeScript files need compilation)');
  }
  
  // Test the web application
  console.log('\n🧪 Testing Web Application...');
  
  try {
    const response = await fetch('http://localhost:3001/api/documents?tenantId=' + TENANT_ID);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API endpoint working:', data.success ? 'Success' : 'Error');
    } else {
      console.log('⚠️ API endpoint not accessible (server may not be running)');
    }
  } catch (error) {
    console.log('⚠️ Web application test skipped (server not running)');
  }
  
  console.log('\n🎉 Document Management System Test Complete!');
  console.log('\n📋 Next Steps:');
  
  if (existingTables.length < tables.length) {
    console.log('   1. Create missing database tables using Supabase SQL Editor');
    console.log('   2. Run: npm run dev');
    console.log('   3. Visit: http://localhost:3001/documents');
  } else {
    console.log('   1. Run: npm run dev (if not already running)');
    console.log('   2. Visit: http://localhost:3001/documents');
    console.log('   3. Test document creation and management');
    console.log('   4. Test consent tracking and GDPR features');
  }
  
  console.log('\n✨ Your Privacy-First Document Management System is ready!');
}

testAndSetup().catch(console.error);

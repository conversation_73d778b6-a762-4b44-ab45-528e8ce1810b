// Document Viewer - Display and manage generated documents
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Download, 
  Edit3, 
  Share2, 
  Copy, 
  FileText, 
  Code,
  Eye,
  Calendar,
  User
} from 'lucide-react';
import { GeneratedDocument } from '@/lib/document-generator-types';
import { DocumentGeneratorService } from '@/lib/document-generator-service';

interface DocumentViewerProps {
  tenantId: string;
  documentId: string;
  onBack: () => void;
  onEdit: () => void;
}

export function DocumentViewer({ tenantId, documentId, onBack, onEdit }: DocumentViewerProps) {
  const [document, setDocument] = useState<GeneratedDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copying, setCopying] = useState(false);

  const documentService = new DocumentGeneratorService(tenantId);

  useEffect(() => {
    loadDocument();
  }, [documentId]);

  const loadDocument = async () => {
    try {
      setLoading(true);
      // Note: We need to add a getDocument method to DocumentGeneratorService
      // For now, we'll simulate the document structure
      const mockDocument: GeneratedDocument = {
        id: documentId,
        session_id: 'mock-session',
        tenant_id: tenantId,
        document_type: 'privacy_policy',
        title: 'Privacy Policy - Generated Document',
        generated_content: `
          <h1>Privacy Policy for Example Company</h1>
          <p><strong>Last Updated:</strong> ${new Date().toLocaleDateString()}</p>
          
          <h2>1. Information We Collect</h2>
          <p>We collect the following types of personal information:</p>
          <ul>
            <li>Personal identifiers: Name, Email, Phone Number</li>
            <li>Usage data and analytics</li>
            <li>Cookies and tracking technologies</li>
          </ul>
          
          <h2>2. How We Use Your Information</h2>
          <p>We use your personal information for:</p>
          <ul>
            <li>Service provision</li>
            <li>Customer support</li>
            <li>Analytics and improvements</li>
          </ul>
          
          <h2>3. Information Sharing</h2>
          <p>We may share your information with: Google Analytics, Stripe</p>
          
          <h2>4. Your Rights</h2>
          <p>Under GDPR, you have the right to access, correct, delete, and port your data.</p>
          <p>Under CCPA, you have the right to know, delete, and opt-out of the sale of your personal information.</p>
          
          <h2>5. Data Retention</h2>
          <p>We retain your data for: 2 years</p>
          
          <h2>6. Contact Information</h2>
          <p>For privacy questions, contact us at: <EMAIL></p>
          <p>Data Protection Officer: <EMAIL></p>
        `,
        markdown_content: `# Privacy Policy for Example Company

**Last Updated:** ${new Date().toLocaleDateString()}

## 1. Information We Collect

We collect the following types of personal information:
- Personal identifiers: Name, Email, Phone Number
- Usage data and analytics
- Cookies and tracking technologies

## 2. How We Use Your Information

We use your personal information for:
- Service provision
- Customer support
- Analytics and improvements

## 3. Information Sharing
We may share your information with: Google Analytics, Stripe

## 4. Your Rights

Under GDPR, you have the right to access, correct, delete, and port your data.
Under CCPA, you have the right to know, delete, and opt-out of the sale of your personal information.

## 5. Data Retention
We retain your data for: 2 years

## 6. Contact Information
For privacy questions, contact us at: <EMAIL>
Data Protection Officer: <EMAIL>`,
        status: 'draft',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      setDocument(mockDocument);
    } catch (err) {
      setError('Failed to load document');
      console.error('Error loading document:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = async (content: string) => {
    try {
      setCopying(true);
      await navigator.clipboard.writeText(content);
      // You might want to show a toast notification here
      setTimeout(() => setCopying(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      setCopying(false);
    }
  };

  const handleDownload = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getDocumentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      privacy_policy: 'Privacy Policy',
      terms_conditions: 'Terms and Conditions',
      cookie_policy: 'Cookie Policy',
      eula: 'End User License Agreement',
      acceptable_use: 'Acceptable Use Policy',
      return_policy: 'Return Policy',
      disclaimer: 'Disclaimer',
      shipping_policy: 'Shipping Policy'
    };
    return labels[type] || type;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className="text-center space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">Document Not Found</h2>
        <p className="text-gray-600">{error || 'The requested document could not be found.'}</p>
        <Button onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Generator Hub
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Generator Hub
        </Button>
        
        <div className="flex items-center space-x-2">
          <Badge variant="outline">
            {getDocumentTypeLabel(document.document_type)}
          </Badge>
          <Badge variant={document.status === 'published' ? 'default' : 'secondary'}>
            {document.status}
          </Badge>
        </div>
      </div>

      {/* Document Info */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl">{document.title}</CardTitle>
              <CardDescription className="mt-2 space-y-1">
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Created: {new Date(document.created_at).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Updated: {new Date(document.updated_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </CardDescription>
            </div>
            
            <div className="flex space-x-2">
              <Button variant="outline" onClick={onEdit}>
                <Edit3 className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Document Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Document Content</CardTitle>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDownload(document.generated_content, `${document.title}.html`, 'text/html')}
              >
                <Download className="h-4 w-4 mr-2" />
                HTML
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDownload(document.markdown_content, `${document.title}.md`, 'text/markdown')}
              >
                <Download className="h-4 w-4 mr-2" />
                Markdown
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="preview" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="preview" className="flex items-center space-x-2">
                <Eye className="h-4 w-4" />
                <span>Preview</span>
              </TabsTrigger>
              <TabsTrigger value="html" className="flex items-center space-x-2">
                <Code className="h-4 w-4" />
                <span>HTML</span>
              </TabsTrigger>
              <TabsTrigger value="markdown" className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>Markdown</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="preview" className="mt-6">
              <div className="border rounded-lg p-6 bg-white">
                <div 
                  className="prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{ __html: document.generated_content }}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="html" className="mt-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">HTML Source</h3>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleCopyToClipboard(document.generated_content)}
                    disabled={copying}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {copying ? 'Copied!' : 'Copy'}
                  </Button>
                </div>
                <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{document.generated_content}</code>
                </pre>
              </div>
            </TabsContent>
            
            <TabsContent value="markdown" className="mt-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Markdown Source</h3>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleCopyToClipboard(document.markdown_content)}
                    disabled={copying}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {copying ? 'Copied!' : 'Copy'}
                  </Button>
                </div>
                <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{document.markdown_content}</code>
                </pre>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={onEdit}>
          <Edit3 className="h-4 w-4 mr-2" />
          Edit Document
        </Button>
        <Button>
          <Share2 className="h-4 w-4 mr-2" />
          Publish Document
        </Button>
      </div>
    </div>
  );
}

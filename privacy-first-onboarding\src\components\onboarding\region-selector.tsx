'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Globe, Shield, MapPin } from 'lucide-react';
import { PRIVACY_REGIONS } from '@/lib/utils';
import type { PrivacyRegion } from '@/lib/utils';

interface RegionSelectorProps {
  selectedRegion: PrivacyRegion | null;
  onRegionSelect: (region: PrivacyRegion) => void;
  onNext: () => void;
}

export function RegionSelector({ selectedRegion, onRegionSelect, onNext }: RegionSelectorProps) {
  const [detectedRegion] = useState<PrivacyRegion>('US'); // In production, detect from IP

  const handleRegionChange = (value: string) => {
    onRegionSelect(value as PrivacyRegion);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <Globe className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <CardTitle className="text-2xl">Choose Your Data Region</CardTitle>
        <CardDescription className="text-lg">
          Select where you'd like your personal data to be stored and processed. 
          This ensures compliance with your local privacy laws.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Detected Region Info */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 mb-2">
            <MapPin className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-900">Detected Location</span>
          </div>
          <p className="text-sm text-blue-700">
            Based on your location, we recommend: <strong>{PRIVACY_REGIONS[detectedRegion].name}</strong>
          </p>
        </div>

        {/* Region Selection */}
        <div className="space-y-3">
          <Label htmlFor="region-select" className="text-base font-medium">
            Select Your Preferred Data Region
          </Label>
          <Select value={selectedRegion || ''} onValueChange={handleRegionChange}>
            <SelectTrigger id="region-select" className="h-12">
              <SelectValue placeholder="Choose a region..." />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(PRIVACY_REGIONS).map(([code, region]) => (
                <SelectItem key={code} value={code}>
                  <div className="flex items-center justify-between w-full">
                    <span>{region.name}</span>
                    <div className="flex gap-1 ml-2">
                      {region.laws.map((law) => (
                        <Badge key={law} variant="secondary" className="text-xs">
                          {law}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Selected Region Info */}
        {selectedRegion && (
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="font-medium text-green-900">Privacy Protection</span>
            </div>
            <p className="text-sm text-green-700 mb-2">
              Your data will be protected under the following privacy laws:
            </p>
            <div className="flex flex-wrap gap-2">
              {PRIVACY_REGIONS[selectedRegion].laws.map((law) => (
                <Badge key={law} variant="outline" className="text-green-700 border-green-300">
                  {law}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Privacy Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">What this means:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Your personal data will be stored in data centers within your selected region</li>
            <li>• We'll comply with the privacy laws applicable to your region</li>
            <li>• You can change this preference later in your privacy settings</li>
            <li>• Non-personal application data may be stored globally for performance</li>
          </ul>
        </div>

        {/* Continue Button */}
        <Button 
          onClick={onNext} 
          disabled={!selectedRegion}
          className="w-full h-12 text-base"
        >
          Continue to Privacy Preferences
        </Button>
      </CardContent>
    </Card>
  );
}

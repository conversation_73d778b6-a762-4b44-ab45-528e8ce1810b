import { prisma } from './database';
import { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from './utils';
import type { PrivacyRegion, DataCategory, UserRight } from './utils';

// Regional compliance rules and requirements
export interface ComplianceRule {
  id: string;
  region: PrivacyRegion;
  law: string;
  category: 'consent' | 'retention' | 'rights' | 'notification' | 'security';
  requirement: string;
  description: string;
  mandatory: boolean;
  deadline?: number; // days
  penalty?: string;
}

export const COMPLIANCE_RULES: ComplianceRule[] = [
  // GDPR (EU) Rules
  {
    id: 'gdpr-consent-explicit',
    region: 'EU',
    law: 'GDPR',
    category: 'consent',
    requirement: 'Explicit consent required for data processing',
    description: 'Consent must be freely given, specific, informed and unambiguous',
    mandatory: true,
  },
  {
    id: 'gdpr-right-access',
    region: 'EU',
    law: 'GDPR',
    category: 'rights',
    requirement: 'Right of access (Article 15)',
    description: 'Data subjects have the right to obtain confirmation and access to their personal data',
    mandatory: true,
    deadline: 30,
  },
  {
    id: 'gdpr-right-erasure',
    region: 'EU',
    law: 'GDPR',
    category: 'rights',
    requirement: 'Right to erasure (Article 17)',
    description: 'Right to be forgotten - data subjects can request deletion of their data',
    mandatory: true,
    deadline: 30,
  },
  {
    id: 'gdpr-data-portability',
    region: 'EU',
    law: 'GDPR',
    category: 'rights',
    requirement: 'Right to data portability (Article 20)',
    description: 'Data subjects have the right to receive their data in a structured format',
    mandatory: true,
    deadline: 30,
  },
  {
    id: 'gdpr-breach-notification',
    region: 'EU',
    law: 'GDPR',
    category: 'notification',
    requirement: 'Data breach notification (Article 33)',
    description: 'Must notify supervisory authority within 72 hours of becoming aware of a breach',
    mandatory: true,
    deadline: 3, // 72 hours = 3 days
    penalty: 'Up to €10 million or 2% of annual turnover',
  },

  // CCPA (US) Rules
  {
    id: 'ccpa-right-know',
    region: 'US',
    law: 'CCPA',
    category: 'rights',
    requirement: 'Right to know about personal information collected',
    description: 'Consumers have the right to know what personal information is collected',
    mandatory: true,
    deadline: 45,
  },
  {
    id: 'ccpa-right-delete',
    region: 'US',
    law: 'CCPA',
    category: 'rights',
    requirement: 'Right to delete personal information',
    description: 'Consumers have the right to request deletion of their personal information',
    mandatory: true,
    deadline: 45,
  },
  {
    id: 'ccpa-opt-out',
    region: 'US',
    law: 'CCPA',
    category: 'rights',
    requirement: 'Right to opt-out of sale',
    description: 'Consumers have the right to opt-out of the sale of their personal information',
    mandatory: true,
  },

  // DPDP (India) Rules
  {
    id: 'dpdp-consent-clear',
    region: 'IN',
    law: 'DPDP',
    category: 'consent',
    requirement: 'Clear and specific consent',
    description: 'Consent must be free, specific, informed, unconditional and unambiguous',
    mandatory: true,
  },
  {
    id: 'dpdp-data-localization',
    region: 'IN',
    law: 'DPDP',
    category: 'security',
    requirement: 'Data localization requirements',
    description: 'Certain categories of personal data must be stored within India',
    mandatory: true,
  },
  {
    id: 'dpdp-right-correction',
    region: 'IN',
    law: 'DPDP',
    category: 'rights',
    requirement: 'Right to correction and erasure',
    description: 'Data principals have the right to correct and erase their personal data',
    mandatory: true,
    deadline: 30,
  },
];

// Compliance assessment engine
export class ComplianceEngine {
  // Assess compliance for a specific region and law
  async assessCompliance(
    tenantId: string,
    region: PrivacyRegion,
    law: string
  ): Promise<{
    score: number;
    compliant: boolean;
    findings: any[];
    recommendations: string[];
  }> {
    const rules = COMPLIANCE_RULES.filter(r => r.region === region && r.law === law);
    const findings: any[] = [];
    const recommendations: string[] = [];
    let totalScore = 0;
    let maxScore = 0;

    for (const rule of rules) {
      maxScore += 100;
      const assessment = await this.assessRule(tenantId, rule);
      findings.push({
        ruleId: rule.id,
        requirement: rule.requirement,
        compliant: assessment.compliant,
        score: assessment.score,
        issues: assessment.issues,
        evidence: assessment.evidence,
      });

      totalScore += assessment.score;

      if (!assessment.compliant) {
        recommendations.push(...assessment.recommendations);
      }
    }

    const score = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 100;
    const compliant = score >= 80; // 80% threshold for compliance

    return {
      score,
      compliant,
      findings,
      recommendations: [...new Set(recommendations)], // Remove duplicates
    };
  }

  // Assess a specific compliance rule
  private async assessRule(
    tenantId: string,
    rule: ComplianceRule
  ): Promise<{
    compliant: boolean;
    score: number;
    issues: string[];
    evidence: any[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const evidence: any[] = [];
    const recommendations: string[] = [];
    let score = 100;

    try {
      switch (rule.category) {
        case 'consent':
          return await this.assessConsentCompliance(tenantId, rule);
        case 'rights':
          return await this.assessRightsCompliance(tenantId, rule);
        case 'retention':
          return await this.assessRetentionCompliance(tenantId, rule);
        case 'notification':
          return await this.assessNotificationCompliance(tenantId, rule);
        case 'security':
          return await this.assessSecurityCompliance(tenantId, rule);
        default:
          return { compliant: true, score: 100, issues: [], evidence: [], recommendations: [] };
      }
    } catch (error) {
      console.error(`Error assessing rule ${rule.id}:`, error);
      return {
        compliant: false,
        score: 0,
        issues: ['Assessment failed due to technical error'],
        evidence: [],
        recommendations: ['Review system configuration and try again'],
      };
    }
  }

  // Assess consent compliance
  private async assessConsentCompliance(
    tenantId: string,
    rule: ComplianceRule
  ): Promise<{
    compliant: boolean;
    score: number;
    issues: string[];
    evidence: any[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const evidence: any[] = [];
    const recommendations: string[] = [];

    // Check tenant privacy configuration
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: { privacyConfig: true },
    });

    if (!tenant?.privacyConfig) {
      issues.push('No privacy configuration found');
      recommendations.push('Configure tenant privacy settings');
      return { compliant: false, score: 0, issues, evidence, recommendations };
    }

    let score = 100;

    // Check if explicit consent is required
    if (rule.requirement.includes('explicit') || rule.requirement.includes('clear')) {
      if (!tenant.privacyConfig.requireExplicitConsent) {
        issues.push('Explicit consent not required in configuration');
        recommendations.push('Enable explicit consent requirement');
        score -= 50;
      } else {
        evidence.push({ type: 'config', value: 'Explicit consent required' });
      }
    }

    // Check consent records
    const consentCount = await prisma.consent.count({
      where: {
        user: { tenantId },
        granted: true,
      },
    });

    evidence.push({ type: 'metric', name: 'Active consents', value: consentCount });

    return {
      compliant: score >= 80,
      score,
      issues,
      evidence,
      recommendations,
    };
  }

  // Assess user rights compliance
  private async assessRightsCompliance(
    tenantId: string,
    rule: ComplianceRule
  ): Promise<{
    compliant: boolean;
    score: number;
    issues: string[];
    evidence: any[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const evidence: any[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check if rights are enabled in configuration
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: { privacyConfig: true },
    });

    if (!tenant?.privacyConfig) {
      return { compliant: false, score: 0, issues: ['No privacy configuration'], evidence, recommendations };
    }

    const config = tenant.privacyConfig;

    // Check specific rights based on rule
    if (rule.requirement.includes('access') || rule.requirement.includes('know')) {
      // Right to access is always available (built into system)
      evidence.push({ type: 'feature', value: 'Data export functionality available' });
    }

    if (rule.requirement.includes('erasure') || rule.requirement.includes('delete')) {
      if (!config.enableRightToBeForgotten) {
        issues.push('Right to be forgotten not enabled');
        recommendations.push('Enable right to be forgotten in privacy configuration');
        score -= 30;
      } else {
        evidence.push({ type: 'config', value: 'Right to be forgotten enabled' });
      }
    }

    if (rule.requirement.includes('portability')) {
      if (!config.enableDataPortability) {
        issues.push('Data portability not enabled');
        recommendations.push('Enable data portability in privacy configuration');
        score -= 30;
      } else {
        evidence.push({ type: 'config', value: 'Data portability enabled' });
      }
    }

    if (rule.requirement.includes('opt-out')) {
      if (!config.enableOptOut) {
        issues.push('Opt-out functionality not enabled');
        recommendations.push('Enable opt-out functionality');
        score -= 30;
      } else {
        evidence.push({ type: 'config', value: 'Opt-out functionality enabled' });
      }
    }

    // Check response times for privacy requests
    if (rule.deadline) {
      const overdueRequests = await prisma.privacyRequest.count({
        where: {
          user: { tenantId },
          status: { in: ['PENDING', 'IN_PROGRESS'] },
          legalDeadline: { lt: new Date() },
        },
      });

      if (overdueRequests > 0) {
        issues.push(`${overdueRequests} privacy requests are overdue`);
        recommendations.push('Process overdue privacy requests immediately');
        score -= Math.min(50, overdueRequests * 10);
      }

      evidence.push({ type: 'metric', name: 'Overdue requests', value: overdueRequests });
    }

    return {
      compliant: score >= 80,
      score,
      issues,
      evidence,
      recommendations,
    };
  }

  // Assess data retention compliance
  private async assessRetentionCompliance(
    tenantId: string,
    rule: ComplianceRule
  ): Promise<{
    compliant: boolean;
    score: number;
    issues: string[];
    evidence: any[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const evidence: any[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check if retention policies are configured
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: { privacyConfig: true },
    });

    if (!tenant?.privacyConfig) {
      return { compliant: false, score: 0, issues: ['No retention policies configured'], evidence, recommendations };
    }

    // Check if retention periods are reasonable
    const config = tenant.privacyConfig;
    const retentionPolicies = {
      personalIdentifiers: config.personalIdentifiersRetention,
      contactInfo: config.contactInfoRetention,
      behavioralData: config.behavioralDataRetention,
    };

    evidence.push({ type: 'config', value: 'Retention policies configured', policies: retentionPolicies });

    // Check for data that should be deleted
    const now = new Date();
    const expiredDataCount = await prisma.user.count({
      where: {
        tenantId,
        isActive: false,
        updatedAt: {
          lt: new Date(now.getTime() - config.contactInfoRetention * 24 * 60 * 60 * 1000),
        },
      },
    });

    if (expiredDataCount > 0) {
      issues.push(`${expiredDataCount} inactive users have data beyond retention period`);
      recommendations.push('Run data cleanup process to remove expired data');
      score -= Math.min(30, expiredDataCount * 5);
    }

    return {
      compliant: score >= 80,
      score,
      issues,
      evidence,
      recommendations,
    };
  }

  // Assess notification compliance
  private async assessNotificationCompliance(
    tenantId: string,
    rule: ComplianceRule
  ): Promise<{
    compliant: boolean;
    score: number;
    issues: string[];
    evidence: any[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const evidence: any[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check data breach notifications
    if (rule.requirement.includes('breach')) {
      const recentBreaches = await prisma.dataBreach.findMany({
        where: {
          detectedAt: {
            gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 90 days
          },
        },
      });

      for (const breach of recentBreaches) {
        const timeSinceDetection = Date.now() - breach.detectedAt.getTime();
        const hoursElapsed = timeSinceDetection / (1000 * 60 * 60);

        if (breach.requiresNotification && !breach.notificationSent && hoursElapsed > 72) {
          issues.push(`Data breach ${breach.id} not reported within 72 hours`);
          recommendations.push('Report data breach to supervisory authority immediately');
          score -= 50;
        }
      }

      evidence.push({ type: 'metric', name: 'Recent breaches', value: recentBreaches.length });
    }

    return {
      compliant: score >= 80,
      score,
      issues,
      evidence,
      recommendations,
    };
  }

  // Assess security compliance
  private async assessSecurityCompliance(
    tenantId: string,
    rule: ComplianceRule
  ): Promise<{
    compliant: boolean;
    score: number;
    issues: string[];
    evidence: any[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const evidence: any[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // For demo purposes, assume basic security measures are in place
    evidence.push({ type: 'security', value: 'Data encryption at rest and in transit' });
    evidence.push({ type: 'security', value: 'Access controls implemented' });
    evidence.push({ type: 'security', value: 'Audit logging enabled' });

    // Check data localization for regions that require it
    if (rule.requirement.includes('localization')) {
      const tenant = await prisma.tenant.findUnique({
        where: { id: tenantId },
        select: { regions: true, defaultRegion: true },
      });

      if (tenant && rule.region === 'IN' && !tenant.regions.includes('IN')) {
        issues.push('Data localization required for Indian users');
        recommendations.push('Set up data storage infrastructure in India');
        score -= 40;
      } else {
        evidence.push({ type: 'config', value: 'Regional data storage configured' });
      }
    }

    return {
      compliant: score >= 80,
      score,
      issues,
      evidence,
      recommendations,
    };
  }

  // Generate compliance report
  async generateComplianceReport(tenantId: string): Promise<any> {
    const tenant = await prisma.tenant.findUnique({
      where: { id: tenantId },
      include: { privacyConfig: true },
    });

    if (!tenant) {
      throw new Error('Tenant not found');
    }

    const report = {
      tenantId,
      tenantName: tenant.name,
      generatedAt: new Date(),
      regions: tenant.regions,
      assessments: [] as any[],
      overallScore: 0,
      overallCompliant: false,
      summary: {
        totalRules: 0,
        compliantRules: 0,
        criticalIssues: 0,
        recommendations: [] as string[],
      },
    };

    let totalScore = 0;
    let totalAssessments = 0;

    // Assess compliance for each region and applicable law
    for (const region of tenant.regions) {
      const laws = PRIVACY_REGIONS[region as PrivacyRegion].laws;
      
      for (const law of laws) {
        const assessment = await this.assessCompliance(tenantId, region as PrivacyRegion, law);
        
        report.assessments.push({
          region,
          law,
          ...assessment,
        });

        totalScore += assessment.score;
        totalAssessments++;
        
        report.summary.totalRules += assessment.findings.length;
        report.summary.compliantRules += assessment.findings.filter(f => f.compliant).length;
        report.summary.criticalIssues += assessment.findings.filter(f => !f.compliant).length;
        report.summary.recommendations.push(...assessment.recommendations);
      }
    }

    report.overallScore = totalAssessments > 0 ? Math.round(totalScore / totalAssessments) : 100;
    report.overallCompliant = report.overallScore >= 80;
    report.summary.recommendations = [...new Set(report.summary.recommendations)];

    return report;
  }
}

// Export singleton instance
export const complianceEngine = new ComplianceEngine();

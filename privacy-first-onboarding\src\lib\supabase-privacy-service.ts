import { supabase, supabaseAdmin, supabaseRegional, supabaseUtils } from './supabase';
import { 
  PrivacyConsent, 
  UserPrivacyProfile, 
  TenantConfiguration,
  OnboardingFormData 
} from './privacy-types';
import { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from './utils';
import type { PrivacyRegion, DataCategory, UserRight } from './utils';

// Supabase-powered Privacy Service
export class SupabasePrivacyService {
  private tenantId: string;

  constructor(tenantId: string = 'default') {
    this.tenantId = tenantId;
  }

  // Check if Supabase is configured
  isSupabaseConfigured(): boolean {
    return supabaseUtils.isConfigured();
  }

  // Get regional client for user
  private getRegionalClient(region: string) {
    return supabaseRegional.getRegionalClient(region);
  }

  // User Registration and Onboarding
  async registerUser(
    formData: OnboardingFormData,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ userId: string; success: boolean }> {
    try {
      // Get regional client based on preferred region
      const client = this.getRegionalClient(formData.preferredRegion);

          // Check if Supabase is configured
      if (!supabaseAdmin) {
        throw new Error('Supabase not configured. Please set up your environment variables.');
      }

      // Get or create tenant
      const { data: tenant, error: tenantError } = await supabaseAdmin
        .from('tenants')
        .select('*')
        .eq('slug', 'default')
        .single();

      if (tenantError && tenantError.code !== 'PGRST116') {
        throw tenantError;
      }

      let tenantData = tenant;
      if (!tenant) {
        // Create default tenant
        const { data: newTenant, error: createTenantError } = await supabaseAdmin
          .from('tenants')
          .insert([{
            name: 'Privacy First Application',
            slug: 'default',
            regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],
            default_region: 'US'
          }])
          .select()
          .single();

        if (createTenantError) throw createTenantError;
        tenantData = newTenant;
      }

      // Create user
      const { data: user, error: userError } = await client
        .from('users')
        .insert([{
          tenant_id: tenantData.id,
          email: formData.email,
          first_name: formData.firstName,
          last_name: formData.lastName,
          preferred_region: formData.preferredRegion,
          data_residency_preference: formData.dataResidencyPreference,
          marketing_consent: formData.communicationPreferences.marketing,
          analytics_consent: formData.communicationPreferences.analytics,
          personalization_consent: formData.communicationPreferences.personalization,
          third_party_sharing: formData.communicationPreferences.thirdPartySharing,
        }])
        .select()
        .single();

      if (userError) throw userError;

      // Create consents
      const consentInserts = formData.consents.map(consent => ({
        user_id: user.id,
        data_category: consent.dataCategory,
        purpose: consent.purpose,
        legal_basis: this.determineLegalBasis(consent.dataCategory, consent.purpose),
        granted: consent.granted,
        granted_at: consent.granted ? new Date().toISOString() : null,
        region: consent.region,
        ip_address: ipAddress,
        user_agent: userAgent,
        consent_method: 'WEB_FORM',
      }));

      const { error: consentError } = await client
        .from('consents')
        .insert(consentInserts);

      if (consentError) throw consentError;

      // Create audit log
      await this.createAuditLog(
        'USER_REGISTRATION',
        'CREATE',
        'user',
        user.id,
        user.id,
        null,
        { email: formData.email, region: formData.preferredRegion },
        { onboardingFlow: true },
        formData.preferredRegion,
        ipAddress,
        userAgent
      );

      return { userId: user.id, success: true };
    } catch (error) {
      console.error('User registration failed:', error);
      throw new Error('Registration failed');
    }
  }

  // Consent Management
  async getConsents(userId: string): Promise<PrivacyConsent[]> {
    try {
      if (!supabaseAdmin || !supabaseUtils.isConfigured()) {
        throw new Error('Supabase not configured');
      }

      const { data: consents, error } = await supabaseAdmin
        .from('consents')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return consents.map(consent => ({
        id: consent.id,
        userId: consent.user_id,
        dataCategory: consent.data_category as keyof typeof DATA_CATEGORIES,
        purpose: consent.purpose,
        granted: consent.granted,
        timestamp: new Date(consent.granted_at || consent.created_at),
        expiresAt: consent.expires_at ? new Date(consent.expires_at) : undefined,
        region: consent.region as keyof typeof PRIVACY_REGIONS,
        version: consent.version,
      }));
    } catch (error) {
      console.error('Failed to get consents:', error);
      return [];
    }
  }

  async updateConsent(
    userId: string, 
    consentId: string, 
    granted: boolean,
    ipAddress?: string,
    userAgent?: string
  ): Promise<boolean> {
    try {
      if (!supabaseAdmin) {
        throw new Error('Supabase not configured');
      }

      const { data: oldConsent, error: fetchError } = await supabaseAdmin
        .from('consents')
        .select('*')
        .eq('id', consentId)
        .single();

      if (fetchError) throw fetchError;

      if (!supabaseUtils.isConfigured()) {
        throw new Error('Supabase not configured');
      }

      const { error: updateError } = await supabaseUtils.updateConsent(consentId, granted);
      if (updateError) throw updateError;

      // Create audit log
      await this.createAuditLog(
        'CONSENT_CHANGE',
        'UPDATE',
        'consent',
        consentId,
        userId,
        { granted: oldConsent.granted },
        { granted },
        { method: 'dashboard' },
        oldConsent.region,
        ipAddress,
        userAgent
      );

      return true;
    } catch (error) {
      console.error('Failed to update consent:', error);
      return false;
    }
  }

  // User Rights Management
  async exerciseUserRight(
    userId: string, 
    right: UserRight,
    description?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<string> {
    try {
      if (!supabaseAdmin) {
        throw new Error('Supabase not configured');
      }

      const { data: user, error: userError } = await supabaseAdmin
        .from('users')
        .select('preferred_region')
        .eq('id', userId)
        .single();

      if (userError) throw userError;

      // Calculate legal deadline based on region and right
      const legalDeadline = this.calculateLegalDeadline(right, user.preferred_region as PrivacyRegion);

      if (!supabaseUtils.isConfigured()) {
        throw new Error('Supabase not configured');
      }

      const { data: request, error: requestError } = await supabaseUtils.createPrivacyRequest({
        user_id: userId,
        request_type: right,
        status: 'PENDING',
        description,
        region: user.preferred_region,
        legal_deadline: legalDeadline.toISOString(),
      });

      if (requestError) throw requestError;

      // Create audit log
      await this.createAuditLog(
        'PRIVACY_REQUEST',
        'CREATE',
        'privacy_request',
        request[0].id,
        userId,
        null,
        { requestType: right, status: 'PENDING' },
        { ipAddress, userAgent },
        user.preferred_region,
        ipAddress,
        userAgent
      );

      return request[0].id;
    } catch (error) {
      console.error('Failed to exercise user right:', error);
      throw error;
    }
  }

  // Data Export (Right to Portability)
  async exportUserData(userId: string): Promise<any> {
    try {
      const userData = await supabaseUtils.getUserPrivacyProfile(userId);

      const exportData = {
        user: {
          id: userData.id,
          email: userData.email,
          firstName: userData.first_name,
          lastName: userData.last_name,
          preferredRegion: userData.preferred_region,
          dataResidencyPreference: userData.data_residency_preference,
          createdAt: userData.created_at,
        },
        consents: userData.consents,
        privacyRequests: userData.privacy_requests,
        exportedAt: new Date().toISOString(),
        format: 'JSON',
        exportMethod: 'USER_REQUEST',
      };

      // Create audit log
      await this.createAuditLog(
        'DATA_EXPORT',
        'READ',
        'user',
        userId,
        userId,
        null,
        { exportSize: JSON.stringify(exportData).length },
        { method: 'dashboard' },
        userData.preferred_region
      );

      return exportData;
    } catch (error) {
      console.error('Failed to export user data:', error);
      throw error;
    }
  }

  // Data Deletion (Right to be Forgotten)
  async deleteUserData(userId: string): Promise<boolean> {
    try {
      if (!supabaseAdmin) {
        throw new Error('Supabase not configured');
      }

      const { data: user, error: fetchError } = await supabaseAdmin
        .from('users')
        .select('preferred_region, email')
        .eq('id', userId)
        .single();

      if (fetchError) throw fetchError;

      // Create final audit log before deletion
      await this.createAuditLog(
        'DATA_DELETION',
        'DELETE',
        'user',
        userId,
        userId,
        { email: user.email },
        null,
        { method: 'user_request', rightToBeForgotten: true },
        user.preferred_region
      );

      // Delete user (cascading deletes handle relationships)
      if (!supabaseAdmin) {
        throw new Error('Supabase not configured');
      }

      const { error: deleteError } = await supabaseAdmin
        .from('users')
        .delete()
        .eq('id', userId);

      if (deleteError) throw deleteError;

      return true;
    } catch (error) {
      console.error('Failed to delete user data:', error);
      return false;
    }
  }

  // Privacy Dashboard Data
  async getPrivacyDashboardData(userId: string): Promise<{
    profile: UserPrivacyProfile | null;
    consents: PrivacyConsent[];
    applicableLaws: string[];
    dataRetentionInfo: any;
  }> {
    try {
      console.log('SupabasePrivacyService: Getting dashboard data for user:', userId);

      // Check if Supabase is configured
      if (!supabaseUtils.isConfigured()) {
        console.log('Supabase not configured, falling back to mock data');
        throw new Error('Supabase not configured');
      }

      console.log('SupabasePrivacyService: Fetching user privacy profile...');
      let userData;
      try {
        userData = await supabaseUtils.getUserPrivacyProfile(userId);
        console.log('SupabasePrivacyService: User data received:', userData ? 'Found' : 'Not found');
      } catch (profileError) {
        console.error('SupabasePrivacyService: Error fetching user profile:', profileError);
        throw profileError;
      }

      if (!userData) {
        console.log('SupabasePrivacyService: No user data found, returning empty profile');
        return {
          profile: null,
          consents: [],
          applicableLaws: [],
          dataRetentionInfo: {}
        };
      }

      console.log('SupabasePrivacyService: Building user profile...');
      let profile: UserPrivacyProfile;
      try {
        // Build profile with safe defaults for missing properties
        profile = {
          userId: userData.id,
          preferredRegion: (userData.preferred_region as PrivacyRegion) || 'US',
          dataResidencyPreference: userData.data_residency_preference || 'same_region',
          consentPreferences: (userData.consents || []).map((c: any) => ({
            id: c.id || c.document_version_id,
            userId: c.user_id || c.end_user_identifier,
            dataCategory: (c.data_category || 'personal_identifiers') as keyof typeof DATA_CATEGORIES,
            purpose: c.purpose || c.consent_type || 'service_provision',
            granted: c.granted !== undefined ? c.granted : c.consent_given,
            timestamp: new Date(c.granted_at || c.consent_timestamp || c.created_at),
            region: (c.region || userData.preferred_region || 'US') as keyof typeof PRIVACY_REGIONS,
            version: c.version || '1.0.0',
          })),
          communicationPreferences: {
            marketing: userData.marketing_consent !== undefined ? userData.marketing_consent : false,
            analytics: userData.analytics_consent !== undefined ? userData.analytics_consent : false,
            personalization: userData.personalization_consent !== undefined ? userData.personalization_consent : false,
            thirdPartySharing: userData.third_party_sharing !== undefined ? userData.third_party_sharing : false,
          },
          rightsExercised: (userData.privacy_requests || []).map((r: any) => ({
            right: (r.request_type || 'access') as keyof typeof USER_RIGHTS,
            requestedAt: new Date(r.requested_at || r.created_at),
            completedAt: r.completed_at ? new Date(r.completed_at) : undefined,
            status: r.status || 'pending',
          })),
        };
        console.log('SupabasePrivacyService: Profile built successfully');
      } catch (profileBuildError) {
        console.error('SupabasePrivacyService: Error building profile:', profileBuildError);
        console.error('SupabasePrivacyService: User data structure:', JSON.stringify(userData, null, 2));

        // Create a minimal profile if building fails
        profile = {
          userId: userData.id,
          preferredRegion: 'US',
          dataResidencyPreference: 'same_region',
          consentPreferences: [],
          communicationPreferences: {
            marketing: false,
            analytics: false,
            personalization: false,
            thirdPartySharing: false,
          },
          rightsExercised: [],
        };
        console.log('SupabasePrivacyService: Created minimal profile as fallback');
      }

      console.log('SupabasePrivacyService: Getting consents...');
      let consents;
      try {
        consents = await this.getConsents(userId);
        console.log('SupabasePrivacyService: Found consents:', consents.length);
      } catch (consentError) {
        console.error('SupabasePrivacyService: Error getting consents:', consentError);
        consents = [];
      }

      let applicableLaws;
      try {
        const region = (userData.preferred_region as PrivacyRegion) || 'US';
        applicableLaws = PRIVACY_REGIONS[region] ? [...PRIVACY_REGIONS[region].laws] : ['CCPA'];
        console.log('SupabasePrivacyService: Applicable laws:', applicableLaws);
      } catch (lawsError) {
        console.error('SupabasePrivacyService: Error getting applicable laws:', lawsError);
        applicableLaws = ['CCPA']; // Default fallback
      }

      let dataRetentionInfo;
      try {
        const region = (userData.preferred_region as PrivacyRegion) || 'US';
        dataRetentionInfo = this.getDataRetentionInfo(region);
      } catch (retentionError) {
        console.error('SupabasePrivacyService: Error getting retention info:', retentionError);
        dataRetentionInfo = { defaultRetention: '2 years', categories: {} };
      }

      return {
        profile,
        consents,
        applicableLaws,
        dataRetentionInfo
      };
    } catch (error) {
      console.error('SupabasePrivacyService: Failed to get dashboard data:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorDetails = {
        message: errorMessage,
        stack: error instanceof Error ? error.stack : 'No stack trace',
        userId,
        isConfigured: supabaseUtils.isConfigured(),
        timestamp: new Date().toISOString()
      };

      console.error('SupabasePrivacyService: Error details:', errorDetails);

      // Don't throw the error, return empty data to allow fallback
      // The dashboard component will handle the empty profile gracefully
      return {
        profile: null,
        consents: [],
        applicableLaws: [],
        dataRetentionInfo: {}
      };
    }
  }

  // Helper Methods
  private determineLegalBasis(dataCategory: string, purpose: string): string {
    if (purpose.toLowerCase().includes('marketing')) return 'CONSENT';
    if (purpose.toLowerCase().includes('account') || purpose.toLowerCase().includes('service')) return 'CONTRACT';
    if (purpose.toLowerCase().includes('analytics') || purpose.toLowerCase().includes('improvement')) return 'LEGITIMATE_INTEREST';
    return 'CONSENT';
  }

  private calculateLegalDeadline(right: UserRight, region: PrivacyRegion): Date {
    const deadlines: Record<PrivacyRegion, number> = {
      'EU': 30, 'UK': 30, 'CA': 30, 'US': 45, 'IN': 30,
      'SG': 30, 'JP': 30, 'AU': 30, 'BR': 15, 'CN': 15,
    };
    const days = deadlines[region] || 30;
    return new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  }

  private async createAuditLog(
    eventType: string,
    action: string,
    resource: string,
    resourceId?: string,
    userId?: string,
    oldValues?: any,
    newValues?: any,
    metadata?: any,
    region?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      await supabaseUtils.createAuditLog({
        user_id: userId,
        event_type: eventType,
        action,
        resource,
        resource_id: resourceId,
        old_values: oldValues,
        new_values: newValues,
        metadata,
        region,
        ip_address: ipAddress,
        user_agent: userAgent,
      });
    } catch (error) {
      console.error('Failed to create audit log:', error);
    }
  }

  private getDataRetentionInfo(region: PrivacyRegion): any {
    return {
      personalIdentifiers: { period: '7 years', basis: 'Legal requirement' },
      contactInfo: { period: '3 years', basis: 'Business necessity' },
      consents: { period: 'Until withdrawn + 1 year', basis: 'Compliance audit' },
      auditLogs: { period: '7 years', basis: 'Legal requirement' },
    };
  }
}

// Export singleton instance
export const supabasePrivacyService = new SupabasePrivacyService();

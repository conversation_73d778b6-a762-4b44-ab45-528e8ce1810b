import { useAuth } from './useAuth';
import { useState, useEffect } from 'react';

interface Tenant {
  id: string;
  name: string;
  slug: string;
}

export function useTenant() {
  const { user } = useAuth();
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user?.tenantId) {
      // In a real app, you'd fetch tenant data from API
      // For now, we'll create a mock tenant based on the user's tenantId
      setTenant({
        id: user.tenantId,
        name: 'Demo Organization',
        slug: 'demo-org'
      });
    } else {
      setTenant(null);
    }
    setIsLoading(false);
  }, [user]);

  return {
    tenant,
    isLoading,
  };
}

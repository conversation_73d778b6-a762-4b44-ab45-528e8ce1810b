// Document Versions API
// Manage document versions for a specific template

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';

interface RouteParams {
  params: {
    templateId: string;
  };
}

// GET /api/documents/[templateId]/versions - Get all versions for template
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.getVersions(params.templateId);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in GET /api/documents/[templateId]/versions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Document Generator Wizard - Multi-step form for generating documents
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  ArrowRight, 
  Save, 
  FileText, 
  Download,
  Eye,
  CheckCircle
} from 'lucide-react';
import { DocumentGeneratorType, DocumentGeneratorSession } from '@/lib/document-generator-types';
import { DocumentGeneratorService } from '@/lib/document-generator-service';
import { PrivacyPolicyForm } from './forms/PrivacyPolicyForm';

interface DocumentGeneratorWizardProps {
  tenantId: string;
  userId?: string;
  documentType: DocumentGeneratorType;
  sessionId?: string;
  onBack: () => void;
  onComplete: (documentId: string) => void;
}

interface FormStep {
  id: string;
  title: string;
  description: string;
  fields: string[];
}

const formSteps: Record<DocumentGeneratorType, FormStep[]> = {
  privacy_policy: [
    {
      id: 'company_info',
      title: 'Company Information',
      description: 'Basic information about your company',
      fields: ['company_name', 'website_url', 'contact_email', 'contact_address']
    },
    {
      id: 'data_collection',
      title: 'Data Collection',
      description: 'What personal information do you collect?',
      fields: ['collects_personal_info', 'personal_info_types', 'collects_usage_data', 'uses_cookies', 'uses_analytics']
    },
    {
      id: 'data_usage',
      title: 'Data Usage',
      description: 'How do you use the collected data?',
      fields: ['data_usage_purposes', 'shares_data_third_party', 'third_party_services']
    },
    {
      id: 'compliance',
      title: 'Legal Compliance',
      description: 'Compliance requirements and user rights',
      fields: ['gdpr_applicable', 'ccpa_applicable', 'data_retention_period', 'privacy_contact_email', 'dpo_contact']
    }
  ],
  terms_conditions: [
    {
      id: 'service_info',
      title: 'Service Information',
      description: 'Basic information about your service',
      fields: ['service_name', 'service_type', 'website_url', 'company_name']
    },
    {
      id: 'user_accounts',
      title: 'User Accounts',
      description: 'Account requirements and policies',
      fields: ['requires_registration', 'minimum_age', 'account_termination_policy']
    },
    {
      id: 'usage_rules',
      title: 'Usage Rules',
      description: 'Service usage guidelines and restrictions',
      fields: ['prohibited_activities', 'content_guidelines', 'intellectual_property_policy']
    },
    {
      id: 'legal_terms',
      title: 'Legal Terms',
      description: 'Liability, disclaimers, and legal information',
      fields: ['liability_limitations', 'warranty_disclaimers', 'governing_law', 'dispute_resolution', 'contact_email']
    }
  ],
  cookie_policy: [
    {
      id: 'website_info',
      title: 'Website Information',
      description: 'Basic information about your website',
      fields: ['website_name', 'website_url']
    },
    {
      id: 'cookie_types',
      title: 'Cookie Types',
      description: 'What types of cookies do you use?',
      fields: ['uses_essential_cookies', 'uses_analytics_cookies', 'uses_marketing_cookies', 'uses_functional_cookies']
    },
    {
      id: 'third_party',
      title: 'Third-Party Services',
      description: 'External services and tracking',
      fields: ['google_analytics', 'facebook_pixel', 'other_tracking_services']
    },
    {
      id: 'user_control',
      title: 'User Control',
      description: 'Cookie management and user options',
      fields: ['cookie_consent_mechanism', 'cookie_management_instructions', 'contact_email']
    }
  ],
  // Add other document types with similar structure
  eula: [
    {
      id: 'software_info',
      title: 'Software Information',
      description: 'Basic information about your software',
      fields: ['software_name', 'company_name', 'contact_email']
    }
  ],
  acceptable_use: [
    {
      id: 'service_info',
      title: 'Service Information',
      description: 'Basic information about your service',
      fields: ['service_name', 'contact_email']
    }
  ],
  return_policy: [
    {
      id: 'company_info',
      title: 'Company Information',
      description: 'Basic information about your company',
      fields: ['company_name', 'contact_email']
    }
  ],
  disclaimer: [
    {
      id: 'website_info',
      title: 'Website Information',
      description: 'Basic information about your website',
      fields: ['website_name', 'contact_email']
    }
  ],
  shipping_policy: [
    {
      id: 'company_info',
      title: 'Company Information',
      description: 'Basic information about your company',
      fields: ['company_name', 'contact_email']
    }
  ]
};

export function DocumentGeneratorWizard({ 
  tenantId, 
  userId, 
  documentType, 
  sessionId, 
  onBack, 
  onComplete 
}: DocumentGeneratorWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [session, setSession] = useState<DocumentGeneratorSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const documentService = new DocumentGeneratorService(tenantId);
  const steps = formSteps[documentType] || [];
  const currentStepData = steps[currentStep];
  const totalSteps = steps.length;
  const progress = ((currentStep + 1) / totalSteps) * 100;

  useEffect(() => {
    if (sessionId) {
      loadSession();
    }
  }, [sessionId]);

  // Auto-save every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (session && Object.keys(formData).length > 0) {
        saveSession();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [session, formData]);

  const loadSession = async () => {
    if (!sessionId) return;

    try {
      setLoading(true);
      const response = await documentService.getSession(sessionId);
      if (response.success && response.data) {
        setSession(response.data);
        setFormData(response.data.form_data || {});
      } else {
        setError(response.error || 'Failed to load session');
      }
    } catch (err) {
      setError('Failed to load session');
      console.error('Error loading session:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveSession = async () => {
    if (!session) return;

    try {
      setSaving(true);
      const response = await documentService.updateSession(session.id, {
        form_data: formData,
        completion_status: currentStep === totalSteps - 1 ? 'completed' : 'draft'
      });

      if (!response.success) {
        console.error('Failed to save session:', response.error);
      }
    } catch (err) {
      console.error('Error saving session:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  // Function for PrivacyPolicyForm component
  const updateFormData = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleNext = async () => {
    await saveSession();
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleGenerate = async () => {
    if (!session) return;

    try {
      setLoading(true);
      
      // Save final session
      await saveSession();
      
      // Generate document
      const response = await documentService.generateDocument({
        session_id: session.id,
        title: `${documentType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} - Generated Document`
      });

      if (response.success && response.data) {
        onComplete(response.data.id);
      } else {
        setError(response.error || 'Failed to generate document');
      }
    } catch (err) {
      setError('Failed to generate document');
      console.error('Error generating document:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderField = (fieldName: string) => {
    const value = formData[fieldName] || '';

    // Field configurations
    const fieldConfigs: Record<string, any> = {
      company_name: { type: 'text', label: 'Company Name', placeholder: 'Enter your company name' },
      website_url: { type: 'url', label: 'Website URL', placeholder: 'https://example.com' },
      contact_email: { type: 'email', label: 'Contact Email', placeholder: '<EMAIL>' },
      contact_address: { type: 'textarea', label: 'Contact Address', placeholder: 'Enter your business address' },
      service_name: { type: 'text', label: 'Service Name', placeholder: 'Enter your service name' },
      service_type: { type: 'select', label: 'Service Type', options: ['Website', 'Mobile App', 'SaaS Platform', 'E-commerce', 'Other'] },
      minimum_age: { type: 'number', label: 'Minimum Age', placeholder: '13' },
      data_retention_period: { type: 'select', label: 'Data Retention Period', options: ['1 year', '2 years', '3 years', '5 years', 'Until account deletion'] },
      governing_law: { type: 'text', label: 'Governing Law', placeholder: 'e.g., State of California' },
      
      // Boolean fields
      collects_personal_info: { type: 'checkbox', label: 'We collect personal information' },
      collects_usage_data: { type: 'checkbox', label: 'We collect usage data' },
      uses_cookies: { type: 'checkbox', label: 'We use cookies' },
      uses_analytics: { type: 'checkbox', label: 'We use analytics' },
      gdpr_applicable: { type: 'checkbox', label: 'GDPR applies to our service' },
      ccpa_applicable: { type: 'checkbox', label: 'CCPA applies to our service' },
      requires_registration: { type: 'checkbox', label: 'Service requires user registration' },
      shares_data_third_party: { type: 'checkbox', label: 'We share data with third parties' },
      
      // Array fields
      personal_info_types: { type: 'textarea', label: 'Types of Personal Information', placeholder: 'e.g., Name, Email, Phone Number' },
      data_usage_purposes: { type: 'textarea', label: 'Data Usage Purposes', placeholder: 'e.g., Service provision, Customer support' },
      third_party_services: { type: 'textarea', label: 'Third-Party Services', placeholder: 'e.g., Google Analytics, Stripe' },
      prohibited_activities: { type: 'textarea', label: 'Prohibited Activities', placeholder: 'List activities that are not allowed' },
      content_guidelines: { type: 'textarea', label: 'Content Guidelines', placeholder: 'Guidelines for user-generated content' }
    };

    const config = fieldConfigs[fieldName] || { type: 'text', label: fieldName, placeholder: '' };

    switch (config.type) {
      case 'text':
      case 'email':
      case 'url':
      case 'number':
        return (
          <div key={fieldName} className="space-y-2">
            <Label htmlFor={fieldName}>{config.label}</Label>
            <Input
              id={fieldName}
              type={config.type}
              value={value}
              placeholder={config.placeholder}
              onChange={(e) => handleFieldChange(fieldName, e.target.value)}
            />
          </div>
        );

      case 'textarea':
        return (
          <div key={fieldName} className="space-y-2">
            <Label htmlFor={fieldName}>{config.label}</Label>
            <Textarea
              id={fieldName}
              value={value}
              placeholder={config.placeholder}
              onChange={(e) => handleFieldChange(fieldName, e.target.value)}
              rows={3}
            />
          </div>
        );

      case 'select':
        return (
          <div key={fieldName} className="space-y-2">
            <Label htmlFor={fieldName}>{config.label}</Label>
            <Select value={value} onValueChange={(val) => handleFieldChange(fieldName, val)}>
              <SelectTrigger>
                <SelectValue placeholder={`Select ${config.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {config.options?.map((option: string) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'checkbox':
        return (
          <div key={fieldName} className="flex items-center space-x-2">
            <Checkbox
              id={fieldName}
              checked={value === true}
              onCheckedChange={(checked) => handleFieldChange(fieldName, checked)}
            />
            <Label htmlFor={fieldName}>{config.label}</Label>
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Generators
        </Button>
        
        <div className="flex items-center space-x-2">
          {saving && (
            <Badge variant="secondary" className="text-xs">
              <Save className="h-3 w-3 mr-1" />
              Saving...
            </Badge>
          )}
          <Badge variant="outline">
            Step {currentStep + 1} of {totalSteps}
          </Badge>
        </div>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-gray-600">
          <span>Progress</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* Form Step */}
      {currentStepData && (
        <Card>
          <CardHeader>
            <CardTitle>{currentStepData.title}</CardTitle>
            <CardDescription>{currentStepData.description}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {documentType === 'privacy_policy' ? (
              <PrivacyPolicyForm
                step={currentStepData.id}
                formData={formData}
                onUpdate={updateFormData}
              />
            ) : (
              // Fallback to generic fields for other document types
              currentStepData.fields.map(fieldName => renderField(fieldName))
            )}
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex space-x-2">
          {currentStep < totalSteps - 1 ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button onClick={handleGenerate} disabled={loading}>
              <FileText className="h-4 w-4 mr-2" />
              Generate Document
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

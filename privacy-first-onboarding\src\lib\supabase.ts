import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// Client-side Supabase client (with RLS enabled)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'privacy-first-app'
    }
  }
});

// Server-side Supabase client (bypasses RLS for admin operations)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  db: {
    schema: 'public'
  }
});

// Regional Supabase clients for data residency compliance
export class SupabaseRegionalManager {
  private static instance: SupabaseRegionalManager;
  private regionalClients: Map<string, any> = new Map();

  private constructor() {}

  static getInstance(): SupabaseRegionalManager {
    if (!SupabaseRegionalManager.instance) {
      SupabaseRegionalManager.instance = new SupabaseRegionalManager();
    }
    return SupabaseRegionalManager.instance;
  }

  // Get region-specific Supabase client
  getRegionalClient(region: string) {
    if (!this.regionalClients.has(region)) {
      // In production, you would have different Supabase projects for each region
      const regionalUrls: Record<string, string> = {
        'US': process.env.NEXT_PUBLIC_SUPABASE_URL_US || supabaseUrl,
        'EU': process.env.NEXT_PUBLIC_SUPABASE_URL_EU || supabaseUrl,
        'UK': process.env.NEXT_PUBLIC_SUPABASE_URL_UK || supabaseUrl,
        'CA': process.env.NEXT_PUBLIC_SUPABASE_URL_CA || supabaseUrl,
        'IN': process.env.NEXT_PUBLIC_SUPABASE_URL_IN || supabaseUrl,
        'SG': process.env.NEXT_PUBLIC_SUPABASE_URL_SG || supabaseUrl,
        'JP': process.env.NEXT_PUBLIC_SUPABASE_URL_JP || supabaseUrl,
        'AU': process.env.NEXT_PUBLIC_SUPABASE_URL_AU || supabaseUrl,
        'BR': process.env.NEXT_PUBLIC_SUPABASE_URL_BR || supabaseUrl,
        'CN': process.env.NEXT_PUBLIC_SUPABASE_URL_CN || supabaseUrl,
      };

      const regionalKeys: Record<string, string> = {
        'US': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_US || supabaseAnonKey,
        'EU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_EU || supabaseAnonKey,
        'UK': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_UK || supabaseAnonKey,
        'CA': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CA || supabaseAnonKey,
        'IN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_IN || supabaseAnonKey,
        'SG': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_SG || supabaseAnonKey,
        'JP': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_JP || supabaseAnonKey,
        'AU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_AU || supabaseAnonKey,
        'BR': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_BR || supabaseAnonKey,
        'CN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CN || supabaseAnonKey,
      };

      const client = createClient(
        regionalUrls[region] || supabaseUrl,
        regionalKeys[region] || supabaseAnonKey,
        {
          auth: {
            autoRefreshToken: true,
            persistSession: true
          }
        }
      );

      this.regionalClients.set(region, client);
    }

    return this.regionalClients.get(region);
  }
}

// Utility functions for Supabase operations
export const supabaseUtils = {
  // Check if Supabase is properly configured
  isConfigured: () => {
    return !!(supabaseUrl && supabaseAnonKey && 
             supabaseUrl !== 'YOUR_SUPABASE_PROJECT_URL' && 
             supabaseAnonKey !== 'YOUR_SUPABASE_ANON_KEY');
  },

  // Get current user
  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  // Sign in with email and password
  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) throw error;
    return data;
  },

  // Sign up with email and password
  signUp: async (email: string, password: string, metadata?: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
    if (error) throw error;
    return data;
  },

  // Sign out
  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Create audit log entry
  createAuditLog: async (logData: any) => {
    const { data, error } = await supabase
      .from('audit_logs')
      .insert([logData]);
    
    if (error) throw error;
    return data;
  },

  // Get user's privacy profile
  getUserPrivacyProfile: async (userId: string) => {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        consents(*),
        privacy_requests(*),
        tenant:tenants(*)
      `)
      .eq('id', userId)
      .single();
    
    if (error) throw error;
    return data;
  },

  // Update user consent
  updateConsent: async (consentId: string, granted: boolean) => {
    const { data, error } = await supabase
      .from('consents')
      .update({
        granted,
        granted_at: granted ? new Date().toISOString() : null,
        withdrawn_at: !granted ? new Date().toISOString() : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', consentId);
    
    if (error) throw error;
    return data;
  },

  // Create privacy request
  createPrivacyRequest: async (requestData: any) => {
    const { data, error } = await supabase
      .from('privacy_requests')
      .insert([requestData]);
    
    if (error) throw error;
    return data;
  },

  // Get compliance status
  getComplianceStatus: async (tenantId: string) => {
    const { data, error } = await supabase
      .from('compliance_status')
      .select('*')
      .eq('tenant_id', tenantId);
    
    if (error) throw error;
    return data;
  }
};

// Export regional manager instance
export const supabaseRegional = SupabaseRegionalManager.getInstance();

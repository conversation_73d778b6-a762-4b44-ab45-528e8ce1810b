import { createClient } from '@supabase/supabase-js';

// Supabase configuration with fallbacks
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-key';

// Check if Supabase is properly configured
const isSupabaseConfigured = !!(
  supabaseUrl &&
  supabaseAnonKey &&
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseAnonKey !== 'placeholder-key' &&
  supabaseUrl !== 'YOUR_SUPABASE_PROJECT_URL' &&
  supabaseAnonKey !== 'YOUR_SUPABASE_ANON_KEY'
);

// Client-side Supabase client (with RLS enabled) - only create if configured
export const supabase = isSupabaseConfigured ? createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'privacy-first-app'
    }
  }
}) : null;

// Server-side Supabase client (bypasses RLS for admin operations) - only create if configured
export const supabaseAdmin = isSupabaseConfigured ? createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  db: {
    schema: 'public'
  }
}) : null;

// Regional Supabase clients for data residency compliance
export class SupabaseRegionalManager {
  private static instance: SupabaseRegionalManager;
  private regionalClients: Map<string, any> = new Map();

  private constructor() {}

  static getInstance(): SupabaseRegionalManager {
    if (!SupabaseRegionalManager.instance) {
      SupabaseRegionalManager.instance = new SupabaseRegionalManager();
    }
    return SupabaseRegionalManager.instance;
  }

  // Get region-specific Supabase client
  getRegionalClient(region: string) {
    if (!this.regionalClients.has(region)) {
      // In production, you would have different Supabase projects for each region
      const regionalUrls: Record<string, string> = {
        'US': process.env.NEXT_PUBLIC_SUPABASE_URL_US || supabaseUrl,
        'EU': process.env.NEXT_PUBLIC_SUPABASE_URL_EU || supabaseUrl,
        'UK': process.env.NEXT_PUBLIC_SUPABASE_URL_UK || supabaseUrl,
        'CA': process.env.NEXT_PUBLIC_SUPABASE_URL_CA || supabaseUrl,
        'IN': process.env.NEXT_PUBLIC_SUPABASE_URL_IN || supabaseUrl,
        'SG': process.env.NEXT_PUBLIC_SUPABASE_URL_SG || supabaseUrl,
        'JP': process.env.NEXT_PUBLIC_SUPABASE_URL_JP || supabaseUrl,
        'AU': process.env.NEXT_PUBLIC_SUPABASE_URL_AU || supabaseUrl,
        'BR': process.env.NEXT_PUBLIC_SUPABASE_URL_BR || supabaseUrl,
        'CN': process.env.NEXT_PUBLIC_SUPABASE_URL_CN || supabaseUrl,
      };

      const regionalKeys: Record<string, string> = {
        'US': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_US || supabaseAnonKey,
        'EU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_EU || supabaseAnonKey,
        'UK': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_UK || supabaseAnonKey,
        'CA': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CA || supabaseAnonKey,
        'IN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_IN || supabaseAnonKey,
        'SG': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_SG || supabaseAnonKey,
        'JP': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_JP || supabaseAnonKey,
        'AU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_AU || supabaseAnonKey,
        'BR': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_BR || supabaseAnonKey,
        'CN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CN || supabaseAnonKey,
      };

      const client = createClient(
        regionalUrls[region] || supabaseUrl,
        regionalKeys[region] || supabaseAnonKey,
        {
          auth: {
            autoRefreshToken: true,
            persistSession: true
          }
        }
      );

      this.regionalClients.set(region, client);
    }

    return this.regionalClients.get(region);
  }
}

// Utility functions for Supabase operations
export const supabaseUtils = {
  // Check if Supabase is properly configured
  isConfigured: () => {
    return isSupabaseConfigured;
  },

  // Get current user
  getCurrentUser: async () => {
    if (!supabase) throw new Error('Supabase not configured');
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  // Sign in with email and password
  signIn: async (email: string, password: string) => {
    if (!supabase) throw new Error('Supabase not configured');
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) throw error;
    return data;
  },

  // Sign up with email and password
  signUp: async (email: string, password: string, metadata?: any) => {
    if (!supabase) throw new Error('Supabase not configured');
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
    if (error) throw error;
    return data;
  },

  // Sign out
  signOut: async () => {
    if (!supabase) throw new Error('Supabase not configured');
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Create audit log entry
  createAuditLog: async (logData: any) => {
    if (!supabaseAdmin) throw new Error('Supabase admin not configured');
    const { data, error } = await supabaseAdmin
      .from('audit_logs')
      .insert([logData]);

    if (error) throw error;
    return data;
  },

  // Get user's privacy profile
  getUserPrivacyProfile: async (userId: string) => {
    if (!supabaseAdmin) throw new Error('Supabase admin not configured');

    try {
      // Get basic user data
      const { data: userData, error: userError } = await supabaseAdmin
        .from('users')
        .select(`
          *,
          tenant:tenants(*)
        `)
        .eq('id', userId)
        .single();

      if (userError) {
        console.error('Error fetching user:', userError);
        throw userError;
      }

      if (!userData) {
        throw new Error('User not found');
      }

      // Try to get consent data from document management tables if they exist
      let consents = [];
      try {
        const { data: consentData, error: consentError } = await supabaseAdmin
          .from('end_user_consents')
          .select('*')
          .eq('end_user_identifier', userData.email)
          .eq('tenant_id', userData.tenant_id);

        if (!consentError && consentData) {
          consents = consentData;
        }
      } catch (e) {
        // Document management tables don't exist yet, that's okay
        console.log('Document management tables not available yet');
      }

      // Try to get privacy requests if table exists
      let privacy_requests = [];
      try {
        const { data: requestData, error: requestError } = await supabaseAdmin
          .from('privacy_requests')
          .select('*')
          .eq('user_id', userId);

        if (!requestError && requestData) {
          privacy_requests = requestData;
        }
      } catch (e) {
        // Privacy requests table doesn't exist, that's okay
        console.log('Privacy requests table not available yet');
      }

      return {
        ...userData,
        consents,
        privacy_requests
      };
    } catch (error) {
      console.error('getUserPrivacyProfile error:', error);
      throw error;
    }
  },

  // Update user consent
  updateConsent: async (consentId: string, granted: boolean) => {
    if (!supabaseAdmin) throw new Error('Supabase admin not configured');

    try {
      // Try to update in the new document management schema first
      const { data: docConsentData, error: docConsentError } = await supabaseAdmin
        .from('end_user_consents')
        .update({
          consent_given: granted,
          consent_timestamp: new Date().toISOString()
        })
        .eq('id', consentId);

      if (!docConsentError) {
        return docConsentData;
      }

      // Fallback to old consents table if it exists
      const { data, error } = await supabaseAdmin
        .from('consents')
        .update({
          granted,
          granted_at: granted ? new Date().toISOString() : null,
          withdrawn_at: !granted ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', consentId);

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('updateConsent error:', error);
      throw error;
    }
  },

  // Create privacy request
  createPrivacyRequest: async (requestData: any) => {
    if (!supabaseAdmin) throw new Error('Supabase admin not configured');
    const { data, error } = await supabaseAdmin
      .from('privacy_requests')
      .insert([requestData]);

    if (error) throw error;
    return data;
  },

  // Get compliance status
  getComplianceStatus: async (tenantId: string) => {
    if (!supabaseAdmin) throw new Error('Supabase admin not configured');
    const { data, error } = await supabaseAdmin
      .from('compliance_status')
      .select('*')
      .eq('tenant_id', tenantId);

    if (error) throw error;
    return data;
  }
};

// Export regional manager instance
export const supabaseRegional = SupabaseRegionalManager.getInstance();

// Individual Template API Routes
// CRUD operations for specific document templates

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';

interface RouteParams {
  params: {
    templateId: string;
  };
}

// GET /api/documents/[templateId] - Get specific template
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.getTemplate(params.templateId);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: response.error?.includes('not found') ? 404 : 500 }
      );
    }
  } catch (error) {
    console.error('Error in GET /api/documents/[templateId]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/documents/[templateId] - Update template
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const updates = {
      name: body.name,
      description: body.description,
      markdown_template: body.markdown_template,
      template_content: body.template_content,
      compliance_frameworks: body.compliance_frameworks,
      is_active: body.is_active
    };

    // Remove undefined values
    Object.keys(updates).forEach(key => {
      if (updates[key as keyof typeof updates] === undefined) {
        delete updates[key as keyof typeof updates];
      }
    });

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.updateTemplate(params.templateId, updates);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in PUT /api/documents/[templateId]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/documents/[templateId] - Soft delete template
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.updateTemplate(params.templateId, { is_active: false });

    if (response.success) {
      return NextResponse.json({
        success: true,
        message: 'Template deactivated successfully'
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in DELETE /api/documents/[templateId]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Document Management API Routes
// RESTful API endpoints for document templates and versions

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';
import { TemplateCreationRequest } from '@/lib/document-types';

// GET /api/documents - Get all templates for tenant
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.getTemplates();

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in GET /api/documents:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/documents - Create new template
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const templateRequest: TemplateCreationRequest = {
      template_type: body.template_type,
      name: body.name,
      description: body.description,
      markdown_template: body.markdown_template,
      template_content: body.template_content || {},
      compliance_frameworks: body.compliance_frameworks || []
    };

    // Validate required fields
    if (!templateRequest.template_type || !templateRequest.name || !templateRequest.markdown_template) {
      return NextResponse.json(
        { error: 'Missing required fields: template_type, name, markdown_template' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.createTemplate(templateRequest);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data
      }, { status: 201 });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in POST /api/documents:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Privacy Framework Utilities\nexport const PRIVACY_REGIONS = {\n  US: { code: 'US', name: 'United States', laws: ['CCPA', 'CPRA'] },\n  EU: { code: 'EU', name: 'European Union', laws: ['GDPR'] },\n  UK: { code: 'UK', name: 'United Kingdom', laws: ['UK GDPR', 'DPA 2018'] },\n  CA: { code: 'CA', name: 'Canada', laws: ['PIPEDA', 'CPPA'] },\n  IN: { code: 'IN', name: 'India', laws: ['DPDP'] },\n  SG: { code: 'SG', name: 'Singapore', laws: ['PDPA'] },\n  JP: { code: 'JP', name: 'Japan', laws: ['APPI'] },\n  AU: { code: 'AU', name: 'Australia', laws: ['Privacy Act'] },\n  BR: { code: 'BR', name: 'Brazil', laws: ['LGPD'] },\n  CN: { code: 'CN', name: 'China', laws: ['PIPL'] },\n} as const;\n\nexport type PrivacyRegion = keyof typeof PRIVACY_REGIONS;\n\nexport const DATA_CATEGORIES = {\n  PERSONAL_IDENTIFIERS: 'Personal Identifiers',\n  CONTACT_INFO: 'Contact Information',\n  FINANCIAL_INFO: 'Financial Information',\n  BIOMETRIC_DATA: 'Biometric Data',\n  LOCATION_DATA: 'Location Data',\n  BEHAVIORAL_DATA: 'Behavioral Data',\n  SENSITIVE_PERSONAL: 'Sensitive Personal Data',\n  HEALTH_DATA: 'Health Data',\n  EMPLOYMENT_DATA: 'Employment Data',\n  EDUCATION_DATA: 'Education Data',\n} as const;\n\nexport type DataCategory = keyof typeof DATA_CATEGORIES;\n\nexport const USER_RIGHTS = {\n  ACCESS: 'Right to Access',\n  CORRECTION: 'Right to Correction',\n  DELETION: 'Right to Deletion',\n  PORTABILITY: 'Right to Data Portability',\n  OPT_OUT: 'Right to Opt-Out',\n  RESTRICT_PROCESSING: 'Right to Restrict Processing',\n  OBJECT: 'Right to Object',\n  NON_DISCRIMINATION: 'Right to Non-Discrimination',\n} as const;\n\nexport type UserRight = keyof typeof USER_RIGHTS;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QAAE,MAAM;QAAM,MAAM;QAAiB,MAAM;YAAC;YAAQ;SAAO;IAAC;IAChE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;SAAO;IAAC;IACzD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;YAAW;SAAW;IAAC;IACxE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;YAAU;SAAO;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAO;IAAC;IACpD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAc;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;SAAO;IAAC;IACjD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;AAClD;AAIO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;AAClB;AAIO,MAAM,cAAc;IACzB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,aAAa;IACb,SAAS;IACT,qBAAqB;IACrB,QAAQ;IACR,oBAAoB;AACtB", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration with fallbacks\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-key';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured = !!(\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'https://placeholder.supabase.co' &&\n  supabaseAnonKey !== 'placeholder-key' &&\n  supabaseUrl !== 'YOUR_SUPABASE_PROJECT_URL' &&\n  supabaseAnonKey !== 'YOUR_SUPABASE_ANON_KEY'\n);\n\n// Client-side Supabase client (with RLS enabled) - only create if configured\nexport const supabase = isSupabaseConfigured ? createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  db: {\n    schema: 'public'\n  },\n  global: {\n    headers: {\n      'X-Client-Info': 'privacy-first-app'\n    }\n  }\n}) : null;\n\n// Server-side Supabase client (bypasses RLS for admin operations) - only create if configured\nexport const supabaseAdmin = isSupabaseConfigured ? createClient(supabaseUrl, supabaseServiceKey, {\n  auth: {\n    autoRefreshToken: false,\n    persistSession: false\n  },\n  db: {\n    schema: 'public'\n  }\n}) : null;\n\n// Regional Supabase clients for data residency compliance\nexport class SupabaseRegionalManager {\n  private static instance: SupabaseRegionalManager;\n  private regionalClients: Map<string, any> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): SupabaseRegionalManager {\n    if (!SupabaseRegionalManager.instance) {\n      SupabaseRegionalManager.instance = new SupabaseRegionalManager();\n    }\n    return SupabaseRegionalManager.instance;\n  }\n\n  // Get region-specific Supabase client\n  getRegionalClient(region: string) {\n    if (!this.regionalClients.has(region)) {\n      // In production, you would have different Supabase projects for each region\n      const regionalUrls: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_URL_US || supabaseUrl,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_URL_EU || supabaseUrl,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_URL_UK || supabaseUrl,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_URL_CA || supabaseUrl,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_URL_IN || supabaseUrl,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_URL_SG || supabaseUrl,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_URL_JP || supabaseUrl,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_URL_AU || supabaseUrl,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_URL_BR || supabaseUrl,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_URL_CN || supabaseUrl,\n      };\n\n      const regionalKeys: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_US || supabaseAnonKey,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_EU || supabaseAnonKey,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_UK || supabaseAnonKey,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CA || supabaseAnonKey,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_IN || supabaseAnonKey,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_SG || supabaseAnonKey,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_JP || supabaseAnonKey,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_AU || supabaseAnonKey,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_BR || supabaseAnonKey,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CN || supabaseAnonKey,\n      };\n\n      const client = createClient(\n        regionalUrls[region] || supabaseUrl,\n        regionalKeys[region] || supabaseAnonKey,\n        {\n          auth: {\n            autoRefreshToken: true,\n            persistSession: true\n          }\n        }\n      );\n\n      this.regionalClients.set(region, client);\n    }\n\n    return this.regionalClients.get(region);\n  }\n}\n\n// Utility functions for Supabase operations\nexport const supabaseUtils = {\n  // Check if Supabase is properly configured\n  isConfigured: () => {\n    return isSupabaseConfigured;\n  },\n\n  // Get current user\n  getCurrentUser: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n  },\n\n  // Sign in with email and password\n  signIn: async (email: string, password: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign up with email and password\n  signUp: async (email: string, password: string, metadata?: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: metadata\n      }\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign out\n  signOut: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n  },\n\n  // Create audit log entry\n  createAuditLog: async (logData: any) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n    const { data, error } = await supabaseAdmin\n      .from('audit_logs')\n      .insert([logData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get user's privacy profile\n  getUserPrivacyProfile: async (userId: string) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n\n    try {\n      // Get basic user data\n      const { data: userData, error: userError } = await supabaseAdmin\n        .from('users')\n        .select(`\n          id,\n          tenant_id,\n          email,\n          first_name,\n          last_name,\n          preferred_region,\n          data_residency_preference,\n          marketing_consent,\n          analytics_consent,\n          personalization_consent,\n          third_party_sharing,\n          is_active,\n          email_verified,\n          created_at,\n          updated_at,\n          tenant:tenants(id, name, created_at)\n        `)\n        .eq('id', userId)\n        .single();\n\n      if (userError) {\n        console.error('Error fetching user:', userError);\n        throw new Error(`Failed to fetch user: ${userError.message || 'Unknown error'}`);\n      }\n\n      if (!userData) {\n        throw new Error(`User not found with ID: ${userId}`);\n      }\n\n      // Try to get consent data from document management tables if they exist\n      let consents = [];\n      try {\n        const { data: consentData, error: consentError } = await supabaseAdmin\n          .from('end_user_consents')\n          .select('*')\n          .eq('end_user_identifier', userData.email)\n          .eq('tenant_id', userData.tenant_id);\n\n        if (consentError) {\n          console.log('Consent table not available:', consentError.message);\n        } else if (consentData) {\n          consents = consentData;\n        }\n      } catch (e) {\n        // Document management tables don't exist yet, that's okay\n        console.log('Document management tables not available yet');\n      }\n\n      // Try to get privacy requests if table exists\n      let privacy_requests = [];\n      try {\n        const { data: requestData, error: requestError } = await supabaseAdmin\n          .from('privacy_requests')\n          .select('*')\n          .eq('user_id', userId);\n\n        if (!requestError && requestData) {\n          privacy_requests = requestData;\n        }\n      } catch (e) {\n        // Privacy requests table doesn't exist, that's okay\n        console.log('Privacy requests table not available yet');\n      }\n\n      return {\n        ...userData,\n        consents,\n        privacy_requests\n      };\n    } catch (error) {\n      console.error('getUserPrivacyProfile error:', error);\n      throw new Error(`getUserPrivacyProfile failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  },\n\n  // Update user consent\n  updateConsent: async (consentId: string, granted: boolean) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n\n    try {\n      // Try to update in the new document management schema first\n      const { data: docConsentData, error: docConsentError } = await supabaseAdmin\n        .from('end_user_consents')\n        .update({\n          consent_given: granted,\n          consent_timestamp: new Date().toISOString()\n        })\n        .eq('id', consentId);\n\n      if (!docConsentError) {\n        return docConsentData;\n      }\n\n      // Fallback to old consents table if it exists\n      const { data, error } = await supabaseAdmin\n        .from('consents')\n        .update({\n          granted,\n          granted_at: granted ? new Date().toISOString() : null,\n          withdrawn_at: !granted ? new Date().toISOString() : null,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', consentId);\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error('updateConsent error:', error);\n      throw error;\n    }\n  },\n\n  // Create privacy request\n  createPrivacyRequest: async (requestData: any) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n    const { data, error } = await supabaseAdmin\n      .from('privacy_requests')\n      .insert([requestData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get compliance status\n  getComplianceStatus: async (tenantId: string) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n    const { data, error } = await supabaseAdmin\n      .from('compliance_status')\n      .select('*')\n      .eq('tenant_id', tenantId);\n\n    if (error) throw error;\n    return data;\n  }\n};\n\n// Export regional manager instance\nexport const supabaseRegional = SupabaseRegionalManager.getInstance();\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,wCAAwC;AACxC,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAEpE,2CAA2C;AAC3C,MAAM,uBAAuB,CAAC,CAAC,CAC7B,eACA,mBACA,gBAAgB,qCAChB,oBAAoB,qBACpB,gBAAgB,+BAChB,oBAAoB,wBACtB;AAGO,MAAM,WAAW,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACxF,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,gBAAgB,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oBAAoB;IAChG,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;IACA,IAAI;QACF,QAAQ;IACV;AACF;AAGO,MAAM;IACX,OAAe,SAAkC;IACzC,kBAAoC,IAAI,MAAM;IAEtD,aAAsB,CAAC;IAEvB,OAAO,cAAuC;QAC5C,IAAI,CAAC,wBAAwB,QAAQ,EAAE;YACrC,wBAAwB,QAAQ,GAAG,IAAI;QACzC;QACA,OAAO,wBAAwB,QAAQ;IACzC;IAEA,sCAAsC;IACtC,kBAAkB,MAAc,EAAE;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS;YACrC,4EAA4E;YAC5E,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;YACnD;YAEA,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;YACxD;YAEA,MAAM,SAAS,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACxB,YAAY,CAAC,OAAO,IAAI,aACxB,YAAY,CAAC,OAAO,IAAI,iBACxB;gBACE,MAAM;oBACJ,kBAAkB;oBAClB,gBAAgB;gBAClB;YACF;YAGF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ;QACnC;QAEA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;AACF;AAGO,MAAM,gBAAgB;IAC3B,2CAA2C;IAC3C,cAAc;QACZ,OAAO;IACT;IAEA,mBAAmB;IACnB,gBAAgB;QACd,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe;QAC5B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe,UAAkB;QAC9C,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,WAAW;IACX,SAAS;QACP,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,yBAAyB;IACzB,gBAAgB,OAAO;QACrB,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,cACL,MAAM,CAAC;YAAC;SAAQ;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,6BAA6B;IAC7B,uBAAuB,OAAO;QAC5B,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QAEpC,IAAI;YACF,sBAAsB;YACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAChD,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;QAiBT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,WAAW;gBACb,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,UAAU,OAAO,IAAI,iBAAiB;YACjF;YAEA,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,QAAQ;YACrD;YAEA,wEAAwE;YACxE,IAAI,WAAW,EAAE;YACjB,IAAI;gBACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,cACtD,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,SAAS,KAAK,EACxC,EAAE,CAAC,aAAa,SAAS,SAAS;gBAErC,IAAI,cAAc;oBAChB,QAAQ,GAAG,CAAC,gCAAgC,aAAa,OAAO;gBAClE,OAAO,IAAI,aAAa;oBACtB,WAAW;gBACb;YACF,EAAE,OAAO,GAAG;gBACV,0DAA0D;gBAC1D,QAAQ,GAAG,CAAC;YACd;YAEA,8CAA8C;YAC9C,IAAI,mBAAmB,EAAE;YACzB,IAAI;gBACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,cACtD,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW;gBAEjB,IAAI,CAAC,gBAAgB,aAAa;oBAChC,mBAAmB;gBACrB;YACF,EAAE,OAAO,GAAG;gBACV,oDAAoD;gBACpD,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;gBACL,GAAG,QAAQ;gBACX;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC7G;IACF;IAEA,sBAAsB;IACtB,eAAe,OAAO,WAAmB;QACvC,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QAEpC,IAAI;YACF,4DAA4D;YAC5D,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,cAC5D,IAAI,CAAC,qBACL,MAAM,CAAC;gBACN,eAAe;gBACf,mBAAmB,IAAI,OAAO,WAAW;YAC3C,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,CAAC,iBAAiB;gBACpB,OAAO;YACT;YAEA,8CAA8C;YAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,YACL,MAAM,CAAC;gBACN;gBACA,YAAY,UAAU,IAAI,OAAO,WAAW,KAAK;gBACjD,cAAc,CAAC,UAAU,IAAI,OAAO,WAAW,KAAK;gBACpD,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,sBAAsB,OAAO;QAC3B,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAY;QAEvB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,qBAAqB,OAAO;QAC1B,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB,wBAAwB,WAAW", "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/document-service.ts"], "sourcesContent": ["// Document Management Service\n// Core service for managing document templates, versions, and consent tracking\n\nimport { supabaseAdmin } from './supabase';\nimport {\n  DocumentTemplate,\n  DocumentVersion,\n  EndUserConsent,\n  DocumentGenerationRequest,\n  DocumentGenerationResponse,\n  TemplateCreationRequest,\n  ConsentTrackingRequest,\n  VersionPublishingRequest,\n  DocumentAnalytics,\n  GDPRDataExport,\n  ApiResponse\n} from './document-types';\n\nexport class DocumentManagementService {\n  private tenantId: string;\n\n  constructor(tenantId: string) {\n    if (!tenantId || tenantId.trim() === '') {\n      throw new Error('Tenant ID is required for DocumentManagementService');\n    }\n    this.tenantId = tenantId.trim();\n  }\n\n  // Set tenant context for RLS (simplified - just log for now)\n  private async setTenantContext() {\n    try {\n      if (!supabaseAdmin) throw new Error('Supabase admin client not configured');\n\n      // For now, just log the tenant context\n      // In a real implementation, you would set up proper RLS policies\n      console.log('Setting tenant context for:', this.tenantId);\n\n      // TODO: Implement proper RLS context setting when needed\n      // This could be done through custom database functions or session variables\n\n    } catch (error) {\n      console.warn('setTenantContext failed:', error instanceof Error ? error.message : 'Unknown error');\n      // Don't throw error to prevent breaking the entire service\n    }\n  }\n\n  // Template Management\n  async createTemplate(request: TemplateCreationRequest): Promise<ApiResponse<DocumentTemplate>> {\n    try {\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin\n        .from('document_templates')\n        .insert([{\n          tenant_id: this.tenantId,\n          template_type: request.template_type,\n          name: request.name,\n          description: request.description,\n          markdown_template: request.markdown_template,\n          template_content: request.template_content,\n          compliance_frameworks: request.compliance_frameworks,\n          is_active: true\n        }])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error creating template:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n\n  async getTemplates(): Promise<ApiResponse<DocumentTemplate[]>> {\n    try {\n      // Check if we're running on client-side\n      if (typeof window !== 'undefined') {\n        // Use API endpoint for client-side requests\n        const response = await fetch(`/api/documents/templates?tenantId=${this.tenantId}`);\n        if (!response.ok) {\n          throw new Error(`API request failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        return result;\n      }\n\n      // Server-side direct database access\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin\n        .from('document_templates')\n        .select('*')\n        .eq('tenant_id', this.tenantId)\n        .eq('is_active', true)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n\n      return { success: true, data: data || [] };\n    } catch (error) {\n      console.error('Error fetching templates:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  async getTemplate(templateId: string): Promise<ApiResponse<DocumentTemplate>> {\n    try {\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin\n        .from('document_templates')\n        .select('*')\n        .eq('id', templateId)\n        .eq('tenant_id', this.tenantId)\n        .single();\n\n      if (error) throw error;\n\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error fetching template:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n\n  async updateTemplate(templateId: string, updates: Partial<DocumentTemplate>): Promise<ApiResponse<DocumentTemplate>> {\n    try {\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin\n        .from('document_templates')\n        .update({\n          ...updates,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', templateId)\n        .eq('tenant_id', this.tenantId)\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error updating template:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n\n  // Document Generation\n  async generateDocument(request: DocumentGenerationRequest): Promise<ApiResponse<DocumentGenerationResponse>> {\n    try {\n      console.log('DocumentManagementService: generateDocument called (this should not be used in document generator)');\n\n      // Return a mock success response for demo purposes\n      const mockResponse: DocumentGenerationResponse = {\n        success: true,\n        version_id: `mock-version-${Date.now()}`,\n        version_number: '1.0.0',\n        preview_url: '/mock-preview',\n        pdf_url: undefined\n      };\n\n      console.log('DocumentManagementService: Returning mock response');\n      return {\n        success: true,\n        data: mockResponse\n      };\n    } catch (error) {\n      console.error('Error generating document:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  // Version Management\n  async getVersions(templateId: string): Promise<ApiResponse<DocumentVersion[]>> {\n    try {\n      // Check if we're running on client-side\n      if (typeof window !== 'undefined') {\n        // Use API endpoint for client-side requests\n        const response = await fetch(`/api/documents/templates/${templateId}/versions?tenantId=${this.tenantId}`);\n        if (!response.ok) {\n          throw new Error(`API request failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        return result;\n      }\n\n      // Server-side direct database access\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin\n        .from('document_versions')\n        .select(`\n          *,\n          template:document_templates(name, template_type)\n        `)\n        .eq('template_id', templateId)\n        .eq('tenant_id', this.tenantId)\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        console.log('Versions table not available:', error.message);\n        return { success: true, data: [] };\n      }\n\n      return { success: true, data: data || [] };\n    } catch (error) {\n      console.warn('Error fetching versions, returning empty array:',\n        error instanceof Error ? error.message : 'Unknown error');\n      return {\n        success: true,\n        data: []\n      };\n    }\n  }\n\n  async getVersion(versionId: string): Promise<ApiResponse<DocumentVersion>> {\n    try {\n      // For now, use server-side access only since this is less commonly used\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin\n        .from('document_versions')\n        .select(`\n          *,\n          template:document_templates(name, template_type, compliance_frameworks)\n        `)\n        .eq('id', versionId)\n        .eq('tenant_id', this.tenantId)\n        .single();\n\n      if (error) {\n        console.log('Version not found or table not available:', error.message);\n        return { success: false, error: 'Version not found' };\n      }\n\n      return { success: true, data };\n    } catch (error) {\n      console.warn('Error fetching version:',\n        error instanceof Error ? error.message : 'Unknown error');\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  async publishVersion(request: VersionPublishingRequest): Promise<ApiResponse<any>> {\n    try {\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin.rpc('publish_document_version', {\n        p_tenant_id: this.tenantId,\n        p_version_id: request.version_id,\n        p_notify_existing_users: request.notify_existing_users || false,\n        p_published_by: null // TODO: Get from auth context\n      });\n\n      if (error) throw error;\n\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error publishing version:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n\n  // Consent Tracking\n  async trackConsent(request: ConsentTrackingRequest): Promise<ApiResponse<EndUserConsent>> {\n    try {\n      await this.setTenantContext();\n\n      // Get document version to determine consent type\n      const { data: version } = await supabaseAdmin\n        .from('document_versions')\n        .select('template:document_templates(template_type)')\n        .eq('id', request.document_version_id)\n        .single();\n\n      const { data, error } = await supabaseAdmin\n        .from('end_user_consents')\n        .insert([{\n          tenant_id: this.tenantId,\n          document_version_id: request.document_version_id,\n          end_user_identifier: request.end_user_identifier,\n          end_user_email: request.end_user_email,\n          consent_type: version?.template?.template_type || 'privacy_policy',\n          consent_given: request.consent_given,\n          ip_address: request.ip_address,\n          user_agent: request.user_agent,\n          consent_method: request.consent_method,\n          consent_timestamp: new Date().toISOString()\n        }])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // TODO: Trigger notification if consent given\n      if (request.consent_given) {\n        // Queue notification\n      }\n\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error tracking consent:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n\n  // GDPR Compliance\n  async exportUserData(userEmail: string): Promise<ApiResponse<GDPRDataExport>> {\n    try {\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin.rpc('export_user_consent_data', {\n        p_tenant_id: this.tenantId,\n        p_user_email: userEmail\n      });\n\n      if (error) throw error;\n\n      return {\n        success: true,\n        data: {\n          user_email: userEmail,\n          export_date: new Date().toISOString(),\n          consents: data || [],\n          notifications: [] // TODO: Add notification history\n        }\n      };\n    } catch (error) {\n      console.error('Error exporting user data:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n\n  async deleteUserData(userEmail: string): Promise<ApiResponse<any>> {\n    try {\n      await this.setTenantContext();\n\n      const { data, error } = await supabaseAdmin.rpc('delete_user_consent_data', {\n        p_tenant_id: this.tenantId,\n        p_user_email: userEmail,\n        p_deleted_by: null // TODO: Get from auth context\n      });\n\n      if (error) throw error;\n\n      return { success: true, data };\n    } catch (error) {\n      console.error('Error deleting user data:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,+EAA+E;;;;AAE/E;;AAeO,MAAM;IACH,SAAiB;IAEzB,YAAY,QAAgB,CAAE;QAC5B,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;YACvC,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI;IAC/B;IAEA,6DAA6D;IAC7D,MAAc,mBAAmB;QAC/B,IAAI;YACF,IAAI,CAAC,sHAAA,CAAA,gBAAa,EAAE,MAAM,IAAI,MAAM;YAEpC,uCAAuC;YACvC,iEAAiE;YACjE,QAAQ,GAAG,CAAC,+BAA+B,IAAI,CAAC,QAAQ;QAExD,yDAAyD;QACzD,4EAA4E;QAE9E,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,4BAA4B,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClF,2DAA2D;QAC7D;IACF;IAEA,sBAAsB;IACtB,MAAM,eAAe,OAAgC,EAA0C;QAC7F,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC;gBAAC;oBACP,WAAW,IAAI,CAAC,QAAQ;oBACxB,eAAe,QAAQ,aAAa;oBACpC,MAAM,QAAQ,IAAI;oBAClB,aAAa,QAAQ,WAAW;oBAChC,mBAAmB,QAAQ,iBAAiB;oBAC5C,kBAAkB,QAAQ,gBAAgB;oBAC1C,uBAAuB,QAAQ,qBAAqB;oBACpD,WAAW;gBACb;aAAE,EACD,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,eAAyD;QAC7D,IAAI;YACF,wCAAwC;YACxC,uCAAmC;;YAQnC;YAEA,qCAAqC;YACrC,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,IAAI,CAAC,QAAQ,EAC7B,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;gBAAM,MAAM,QAAQ,EAAE;YAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,YAAY,UAAkB,EAA0C;QAC5E,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,aAAa,IAAI,CAAC,QAAQ,EAC7B,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,eAAe,UAAkB,EAAE,OAAkC,EAA0C;QACnH,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC;gBACN,GAAG,OAAO;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,aAAa,IAAI,CAAC,QAAQ,EAC7B,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,iBAAiB,OAAkC,EAAoD;QAC3G,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,mDAAmD;YACnD,MAAM,eAA2C;gBAC/C,SAAS;gBACT,YAAY,CAAC,aAAa,EAAE,KAAK,GAAG,IAAI;gBACxC,gBAAgB;gBAChB,aAAa;gBACb,SAAS;YACX;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,YAAY,UAAkB,EAA2C;QAC7E,IAAI;YACF,wCAAwC;YACxC,uCAAmC;;YAQnC;YAEA,qCAAqC;YACrC,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,aAAa,IAAI,CAAC,QAAQ,EAC7B,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,GAAG,CAAC,iCAAiC,MAAM,OAAO;gBAC1D,OAAO;oBAAE,SAAS;oBAAM,MAAM,EAAE;gBAAC;YACnC;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM,QAAQ,EAAE;YAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,mDACX,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;YACV;QACF;IACF;IAEA,MAAM,WAAW,SAAiB,EAAyC;QACzE,IAAI;YACF,wEAAwE;YACxE,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,MAAM,WACT,EAAE,CAAC,aAAa,IAAI,CAAC,QAAQ,EAC7B,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,GAAG,CAAC,6CAA6C,MAAM,OAAO;gBACtE,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAoB;YACtD;YAEA,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,2BACX,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,eAAe,OAAiC,EAA6B;QACjF,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,4BAA4B;gBAC1E,aAAa,IAAI,CAAC,QAAQ;gBAC1B,cAAc,QAAQ,UAAU;gBAChC,yBAAyB,QAAQ,qBAAqB,IAAI;gBAC1D,gBAAgB,KAAK,8BAA8B;YACrD;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa,OAA+B,EAAwC;QACxF,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB;YAE3B,iDAAiD;YACjD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CAC1C,IAAI,CAAC,qBACL,MAAM,CAAC,8CACP,EAAE,CAAC,MAAM,QAAQ,mBAAmB,EACpC,MAAM;YAET,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CACxC,IAAI,CAAC,qBACL,MAAM,CAAC;gBAAC;oBACP,WAAW,IAAI,CAAC,QAAQ;oBACxB,qBAAqB,QAAQ,mBAAmB;oBAChD,qBAAqB,QAAQ,mBAAmB;oBAChD,gBAAgB,QAAQ,cAAc;oBACtC,cAAc,SAAS,UAAU,iBAAiB;oBAClD,eAAe,QAAQ,aAAa;oBACpC,YAAY,QAAQ,UAAU;oBAC9B,YAAY,QAAQ,UAAU;oBAC9B,gBAAgB,QAAQ,cAAc;oBACtC,mBAAmB,IAAI,OAAO,WAAW;gBAC3C;aAAE,EACD,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,8CAA8C;YAC9C,IAAI,QAAQ,aAAa,EAAE;YACzB,qBAAqB;YACvB;YAEA,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,eAAe,SAAiB,EAAwC;QAC5E,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,4BAA4B;gBAC1E,aAAa,IAAI,CAAC,QAAQ;gBAC1B,cAAc;YAChB;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,YAAY;oBACZ,aAAa,IAAI,OAAO,WAAW;oBACnC,UAAU,QAAQ,EAAE;oBACpB,eAAe,EAAE,CAAC,iCAAiC;gBACrD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,eAAe,SAAiB,EAA6B;QACjE,IAAI;YACF,MAAM,IAAI,CAAC,gBAAgB;YAE3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,4BAA4B;gBAC1E,aAAa,IAAI,CAAC,QAAQ;gBAC1B,cAAc;gBACd,cAAc,KAAK,8BAA8B;YACnD;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1079, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/documents/template-editor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { ArrowLeft, Save, Eye, FileText, Code, Settings } from 'lucide-react';\nimport { DocumentTemplate, DocumentType, TemplateCreationRequest } from '@/lib/document-types';\nimport { DocumentManagementService } from '@/lib/document-service';\n\ninterface TemplateEditorProps {\n  template?: DocumentTemplate | null;\n  tenantId: string;\n  onSave: () => void;\n  onCancel: () => void;\n}\n\nconst DOCUMENT_TYPES: { value: DocumentType; label: string; description: string }[] = [\n  {\n    value: 'privacy_policy',\n    label: 'Privacy Policy',\n    description: 'GDPR and CCPA compliant privacy policy'\n  },\n  {\n    value: 'terms_of_service',\n    label: 'Terms of Service',\n    description: 'Standard terms and conditions'\n  },\n  {\n    value: 'cookie_policy',\n    label: 'Cookie Policy',\n    description: 'Cookie usage and consent policy'\n  },\n  {\n    value: 'data_processing_agreement',\n    label: 'Data Processing Agreement',\n    description: 'GDPR Article 28 DPA template'\n  }\n];\n\nconst COMPLIANCE_FRAMEWORKS = [\n  'GDPR',\n  'CCPA',\n  'CPRA',\n  'PIPEDA',\n  'LGPD',\n  'ePrivacy',\n  'HIPAA',\n  'SOX',\n  'PCI DSS'\n];\n\nconst DEFAULT_TEMPLATES = {\n  privacy_policy: `# Privacy Policy for {{company_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. Information We Collect\n\nWe collect the following types of personal information:\n- Personal identifiers (name, email address)\n- Contact information\n- Behavioral data and usage analytics\n\n## 2. How We Use Your Information\n\nWe use your personal information for:\n- Providing and improving our services\n- Communication and customer support\n- Legal compliance and fraud prevention\n\n## 3. Data Retention\n\nWe retain your personal information for {{retention_period}} or as required by law.\n\n## 4. Your Rights\n\nUnder applicable privacy laws, you have the right to:\n- Access your personal data\n- Correct inaccurate data\n- Delete your data\n- Data portability\n- Opt-out of data processing\n\n## 5. Contact Information\n\nFor privacy-related questions, contact us at: {{contact_email}}`,\n\n  terms_of_service: `# Terms of Service for {{company_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. Acceptance of Terms\n\nBy using {{service_name}}, you agree to these Terms of Service.\n\n## 2. Description of Service\n\n{{service_name}} provides {{service_description}}.\n\n## 3. User Responsibilities\n\nYou are responsible for:\n- Providing accurate information\n- Maintaining account security\n- Complying with applicable laws\n\n## 4. Prohibited Uses\n\nYou may not use our service for illegal activities or unauthorized access.\n\n## 5. Termination\n\nWe may terminate your account for violation of these terms.\n\n## 6. Contact Information\n\nQuestions about these terms? Contact: {{contact_email}}`,\n\n  cookie_policy: `# Cookie Policy for {{company_name}}\n\n**Last Updated:** {{current_date}}\n\n## What Are Cookies\n\nCookies are small text files stored on your device when you visit {{website_url}}.\n\n## Types of Cookies We Use\n\n### Essential Cookies\nRequired for basic website functionality.\n\n### Analytics Cookies\nHelp us understand how visitors use our website.\n\n### Marketing Cookies\nUsed to deliver relevant advertisements.\n\n## Managing Cookies\n\nYou can control cookies through your browser settings.\n\n## Contact Us\n\nQuestions about our cookie policy? Contact: {{contact_email}}`\n};\n\nexport function TemplateEditor({ template, tenantId, onSave, onCancel }: TemplateEditorProps) {\n  // Early return if tenantId is not provided\n  if (!tenantId || tenantId.trim() === '') {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold mb-2\">Invalid Configuration</h2>\n          <p className=\"text-muted-foreground mb-4\">Tenant ID is required to use the template editor.</p>\n          <Button onClick={onCancel}>Go Back</Button>\n        </div>\n      </div>\n    );\n  }\n\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    template_type: 'privacy_policy' as DocumentType,\n    markdown_template: '',\n    template_content: {},\n    compliance_frameworks: [] as string[]\n  });\n  const [saving, setSaving] = useState(false);\n  const [activeTab, setActiveTab] = useState('basic');\n  const [previewContent, setPreviewContent] = useState('');\n\n  const documentService = new DocumentManagementService(tenantId);\n\n  useEffect(() => {\n    if (template) {\n      setFormData({\n        name: template.name,\n        description: template.description || '',\n        template_type: template.template_type,\n        markdown_template: template.markdown_template,\n        template_content: template.template_content,\n        compliance_frameworks: template.compliance_frameworks\n      });\n    } else {\n      // Set default template for new documents\n      setFormData(prev => ({\n        ...prev,\n        markdown_template: DEFAULT_TEMPLATES[prev.template_type] || ''\n      }));\n    }\n  }, [template]);\n\n  useEffect(() => {\n    // Update default template when type changes\n    if (!template && formData.template_type) {\n      setFormData(prev => ({\n        ...prev,\n        markdown_template: DEFAULT_TEMPLATES[prev.template_type] || ''\n      }));\n    }\n  }, [formData.template_type, template]);\n\n  const handleSave = async () => {\n    setSaving(true);\n    try {\n      const request: TemplateCreationRequest = {\n        template_type: formData.template_type,\n        name: formData.name,\n        description: formData.description,\n        markdown_template: formData.markdown_template,\n        template_content: formData.template_content,\n        compliance_frameworks: formData.compliance_frameworks\n      };\n\n      let response;\n      if (template) {\n        response = await documentService.updateTemplate(template.id, request);\n      } else {\n        response = await documentService.createTemplate(request);\n      }\n\n      if (response.success) {\n        onSave();\n      } else {\n        console.error('Error saving template:', response.error);\n      }\n    } catch (error) {\n      console.error('Error saving template:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleComplianceFrameworkToggle = (framework: string) => {\n    setFormData(prev => ({\n      ...prev,\n      compliance_frameworks: prev.compliance_frameworks.includes(framework)\n        ? prev.compliance_frameworks.filter(f => f !== framework)\n        : [...prev.compliance_frameworks, framework]\n    }));\n  };\n\n  const generatePreview = () => {\n    let preview = formData.markdown_template;\n    \n    // Replace common template variables with sample data\n    const sampleData = {\n      company_name: 'Sample Company Inc.',\n      contact_email: '<EMAIL>',\n      website_url: 'https://samplecompany.com',\n      service_name: 'Sample Service',\n      service_description: 'privacy-first user onboarding and document management',\n      retention_period: '2 years',\n      current_date: new Date().toLocaleDateString(),\n      current_year: new Date().getFullYear().toString()\n    };\n\n    Object.entries(sampleData).forEach(([key, value]) => {\n      preview = preview.replace(new RegExp(`{{${key}}}`, 'g'), value);\n    });\n\n    setPreviewContent(preview);\n  };\n\n  useEffect(() => {\n    generatePreview();\n  }, [formData.markdown_template]);\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-4\">\n          <Button variant=\"ghost\" onClick={onCancel}>\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold\">\n              {template ? 'Edit Template' : 'Create Template'}\n            </h1>\n            <p className=\"text-muted-foreground\">\n              {template ? 'Modify existing template' : 'Create a new document template'}\n            </p>\n          </div>\n        </div>\n        <Button onClick={handleSave} disabled={saving || !formData.name.trim()}>\n          <Save className=\"h-4 w-4 mr-2\" />\n          {saving ? 'Saving...' : 'Save Template'}\n        </Button>\n      </div>\n\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList>\n          <TabsTrigger value=\"basic\">Basic Info</TabsTrigger>\n          <TabsTrigger value=\"content\">Content</TabsTrigger>\n          <TabsTrigger value=\"preview\">Preview</TabsTrigger>\n          <TabsTrigger value=\"compliance\">Compliance</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"basic\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Template Information</CardTitle>\n              <CardDescription>\n                Basic information about your document template\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"name\">Template Name</Label>\n                  <Input\n                    id=\"name\"\n                    value={formData.name}\n                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                    placeholder=\"e.g., Standard Privacy Policy\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"type\">Document Type</Label>\n                  <Select\n                    value={formData.template_type}\n                    onValueChange={(value: DocumentType) => \n                      setFormData(prev => ({ ...prev, template_type: value }))\n                    }\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {DOCUMENT_TYPES.map((type) => (\n                        <SelectItem key={type.value} value={type.value}>\n                          <div>\n                            <div className=\"font-medium\">{type.label}</div>\n                            <div className=\"text-sm text-muted-foreground\">{type.description}</div>\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">Description</Label>\n                <Textarea\n                  id=\"description\"\n                  value={formData.description}\n                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                  placeholder=\"Describe what this template is used for...\"\n                  rows={3}\n                />\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"content\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Template Content</CardTitle>\n              <CardDescription>\n                Write your document template using Markdown. Use {{variable_name}} for dynamic content.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"markdown\">Markdown Template</Label>\n                <Textarea\n                  id=\"markdown\"\n                  value={formData.markdown_template}\n                  onChange={(e) => setFormData(prev => ({ ...prev, markdown_template: e.target.value }))}\n                  placeholder=\"Write your template content here...\"\n                  rows={20}\n                  className=\"font-mono text-sm\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"preview\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Preview</CardTitle>\n              <CardDescription>\n                Preview how your template will look with sample data\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"prose max-w-none\">\n                <pre className=\"whitespace-pre-wrap text-sm\">{previewContent}</pre>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"compliance\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Compliance Frameworks</CardTitle>\n              <CardDescription>\n                Select the compliance frameworks this template addresses\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                {COMPLIANCE_FRAMEWORKS.map((framework) => (\n                  <div key={framework} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={framework}\n                      checked={formData.compliance_frameworks.includes(framework)}\n                      onCheckedChange={() => handleComplianceFrameworkToggle(framework)}\n                    />\n                    <Label htmlFor={framework}>{framework}</Label>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4\">\n                <Label>Selected Frameworks:</Label>\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {formData.compliance_frameworks.map((framework) => (\n                    <Badge key={framework} variant=\"secondary\">\n                      {framework}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAdA;;;;;;;;;;;;;;AAuBA,MAAM,iBAAgF;IACpF;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,wBAAwB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,oBAAoB;IACxB,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+DAiC4C,CAAC;IAE9D,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDA6BkC,CAAC;IAEtD,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;6DAyB2C,CAAC;AAC9D;AAEO,SAAS,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAuB;IAC1F,2CAA2C;IAC3C,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;kCAAU;;;;;;;;;;;;;;;;;IAInC;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,eAAe;QACf,mBAAmB;QACnB,kBAAkB,CAAC;QACnB,uBAAuB,EAAE;IAC3B;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,kBAAkB,IAAI,iIAAA,CAAA,4BAAyB,CAAC;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,YAAY;gBACV,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,IAAI;gBACrC,eAAe,SAAS,aAAa;gBACrC,mBAAmB,SAAS,iBAAiB;gBAC7C,kBAAkB,SAAS,gBAAgB;gBAC3C,uBAAuB,SAAS,qBAAqB;YACvD;QACF,OAAO;YACL,yCAAyC;YACzC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,mBAAmB,iBAAiB,CAAC,KAAK,aAAa,CAAC,IAAI;gBAC9D,CAAC;QACH;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4CAA4C;QAC5C,IAAI,CAAC,YAAY,SAAS,aAAa,EAAE;YACvC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,mBAAmB,iBAAiB,CAAC,KAAK,aAAa,CAAC,IAAI;gBAC9D,CAAC;QACH;IACF,GAAG;QAAC,SAAS,aAAa;QAAE;KAAS;IAErC,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,MAAM,UAAmC;gBACvC,eAAe,SAAS,aAAa;gBACrC,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,mBAAmB,SAAS,iBAAiB;gBAC7C,kBAAkB,SAAS,gBAAgB;gBAC3C,uBAAuB,SAAS,qBAAqB;YACvD;YAEA,IAAI;YACJ,IAAI,UAAU;gBACZ,WAAW,MAAM,gBAAgB,cAAc,CAAC,SAAS,EAAE,EAAE;YAC/D,OAAO;gBACL,WAAW,MAAM,gBAAgB,cAAc,CAAC;YAClD;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,0BAA0B,SAAS,KAAK;YACxD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,kCAAkC,CAAC;QACvC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,uBAAuB,KAAK,qBAAqB,CAAC,QAAQ,CAAC,aACvD,KAAK,qBAAqB,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,aAC7C;uBAAI,KAAK,qBAAqB;oBAAE;iBAAU;YAChD,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,IAAI,UAAU,SAAS,iBAAiB;QAExC,qDAAqD;QACrD,MAAM,aAAa;YACjB,cAAc;YACd,eAAe;YACf,aAAa;YACb,cAAc;YACd,qBAAqB;YACrB,kBAAkB;YAClB,cAAc,IAAI,OAAO,kBAAkB;YAC3C,cAAc,IAAI,OAAO,WAAW,GAAG,QAAQ;QACjD;QAEA,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC9C,UAAU,QAAQ,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM;QAC3D;QAEA,kBAAkB;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,SAAS,iBAAiB;KAAC;IAE/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,SAAS;;kDAC/B,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,WAAW,kBAAkB;;;;;;kDAEhC,8OAAC;wCAAE,WAAU;kDACV,WAAW,6BAA6B;;;;;;;;;;;;;;;;;;kCAI/C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,UAAU,UAAU,CAAC,SAAS,IAAI,CAAC,IAAI;;0CAClE,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,SAAS,cAAc;;;;;;;;;;;;;0BAI5B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAQ;;;;;;0CAC3B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAa;;;;;;;;;;;;kCAGlC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAO;;;;;;sEACtB,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACvE,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAO;;;;;;sEACtB,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,aAAa;4DAC7B,eAAe,CAAC,QACd,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,eAAe;oEAAM,CAAC;;8EAGxD,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,8OAAC,kIAAA,CAAA,gBAAa;8EACX,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,aAAU;4EAAkB,OAAO,KAAK,KAAK;sFAC5C,cAAA,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGAAe,KAAK,KAAK;;;;;;kGACxC,8OAAC;wFAAI,WAAU;kGAAiC,KAAK,WAAW;;;;;;;;;;;;2EAHnD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC9E,aAAY;oDACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;;gDAAC;gDACmC;oDAAC;gDAAa;gDAAE;;;;;;;;;;;;;8CAGtE,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,SAAS,iBAAiB;gDACjC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACpF,aAAY;gDACZ,MAAM;gDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOpB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMtD,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAa,WAAU;kCACxC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,sBAAsB,GAAG,CAAC,CAAC,0BAC1B,8OAAC;oDAAoB,WAAU;;sEAC7B,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAI;4DACJ,SAAS,SAAS,qBAAqB,CAAC,QAAQ,CAAC;4DACjD,iBAAiB,IAAM,gCAAgC;;;;;;sEAEzD,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAS;sEAAY;;;;;;;mDANpB;;;;;;;;;;sDAUd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,8OAAC;oDAAI,WAAU;8DACZ,SAAS,qBAAqB,CAAC,GAAG,CAAC,CAAC,0BACnC,8OAAC,iIAAA,CAAA,QAAK;4DAAiB,SAAQ;sEAC5B;2DADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhC", "debugId": null}}, {"offset": {"line": 2299, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/documents/document-generator.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { ArrowLeft, Save, Eye, Wand2, FileText } from 'lucide-react';\nimport { DocumentTemplate, VersionType, DocumentGenerationRequest } from '@/lib/document-types';\nimport { DocumentManagementService } from '@/lib/document-service';\n\ninterface DocumentGeneratorProps {\n  template: DocumentTemplate;\n  tenantId: string;\n  onSave: () => void;\n  onCancel: () => void;\n}\n\ninterface TemplateVariable {\n  key: string;\n  label: string;\n  type: 'text' | 'email' | 'url' | 'number' | 'date';\n  required: boolean;\n  placeholder?: string;\n  description?: string;\n}\n\nconst COMMON_VARIABLES: TemplateVariable[] = [\n  {\n    key: 'company_name',\n    label: 'Company Name',\n    type: 'text',\n    required: true,\n    placeholder: 'Your Company Inc.',\n    description: 'The legal name of your company'\n  },\n  {\n    key: 'contact_email',\n    label: 'Contact Email',\n    type: 'email',\n    required: true,\n    placeholder: '<EMAIL>',\n    description: 'Email address for privacy-related inquiries'\n  },\n  {\n    key: 'website_url',\n    label: 'Website URL',\n    type: 'url',\n    required: false,\n    placeholder: 'https://yourcompany.com',\n    description: 'Your company website URL'\n  },\n  {\n    key: 'service_name',\n    label: 'Service Name',\n    type: 'text',\n    required: false,\n    placeholder: 'Your Service',\n    description: 'Name of your service or product'\n  },\n  {\n    key: 'service_description',\n    label: 'Service Description',\n    type: 'text',\n    required: false,\n    placeholder: 'Brief description of your service',\n    description: 'What your service does'\n  },\n  {\n    key: 'retention_period',\n    label: 'Data Retention Period',\n    type: 'text',\n    required: false,\n    placeholder: '2 years',\n    description: 'How long you retain personal data'\n  },\n  {\n    key: 'legal_basis',\n    label: 'Legal Basis for Processing',\n    type: 'text',\n    required: false,\n    placeholder: 'Legitimate interest and consent',\n    description: 'GDPR legal basis for data processing'\n  },\n  {\n    key: 'governing_law',\n    label: 'Governing Law',\n    type: 'text',\n    required: false,\n    placeholder: 'State of California',\n    description: 'Jurisdiction governing your terms'\n  }\n];\n\nexport function DocumentGenerator({ template, tenantId, onSave, onCancel }: DocumentGeneratorProps) {\n  const [formData, setFormData] = useState<Record<string, string>>({});\n  const [versionType, setVersionType] = useState<VersionType>('minor');\n  const [changelog, setChangelog] = useState('');\n  const [autoPublish, setAutoPublish] = useState(false);\n  const [notifyUsers, setNotifyUsers] = useState(false);\n  const [generating, setGenerating] = useState(false);\n  const [previewContent, setPreviewContent] = useState('');\n  const [activeTab, setActiveTab] = useState('variables');\n\n  const documentService = new DocumentManagementService(tenantId);\n\n  // Extract variables from template\n  const templateVariables = React.useMemo(() => {\n    const variableRegex = /\\{\\{(\\w+)\\}\\}/g;\n    const matches = template.markdown_template.match(variableRegex) || [];\n    const uniqueVariables = [...new Set(matches.map(match => match.replace(/[{}]/g, '')))];\n    \n    return uniqueVariables.map(varName => {\n      const commonVar = COMMON_VARIABLES.find(v => v.key === varName);\n      return commonVar || {\n        key: varName,\n        label: varName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n        type: 'text' as const,\n        required: false,\n        placeholder: `Enter ${varName.replace(/_/g, ' ')}`\n      };\n    });\n  }, [template.markdown_template]);\n\n  useEffect(() => {\n    // Initialize form data with empty values\n    const initialData: Record<string, string> = {};\n    templateVariables.forEach(variable => {\n      initialData[variable.key] = '';\n    });\n    setFormData(initialData);\n  }, [templateVariables]);\n\n  useEffect(() => {\n    generatePreview();\n  }, [formData, template.markdown_template]);\n\n  const generatePreview = () => {\n    let preview = template.markdown_template;\n    \n    // Replace template variables with form data\n    Object.entries(formData).forEach(([key, value]) => {\n      preview = preview.replace(new RegExp(`{{${key}}}`, 'g'), value || `[${key}]`);\n    });\n\n    // Replace system variables\n    preview = preview.replace(/\\{\\{current_date\\}\\}/g, new Date().toLocaleDateString());\n    preview = preview.replace(/\\{\\{current_year\\}\\}/g, new Date().getFullYear().toString());\n\n    setPreviewContent(preview);\n  };\n\n  const handleGenerate = async () => {\n    setGenerating(true);\n    try {\n      const request: DocumentGenerationRequest = {\n        template_id: template.id,\n        template_data: {\n          ...formData,\n          current_date: new Date().toLocaleDateString(),\n          current_year: new Date().getFullYear().toString()\n        },\n        version_type: versionType,\n        changelog: changelog.trim() || undefined,\n        auto_publish: autoPublish,\n        notify_users: notifyUsers\n      };\n\n      const response = await documentService.generateDocument(request);\n      \n      if (response.success) {\n        onSave();\n      } else {\n        console.error('Error generating document:', response.error);\n      }\n    } catch (error) {\n      console.error('Error generating document:', error);\n    } finally {\n      setGenerating(false);\n    }\n  };\n\n  const isFormValid = () => {\n    const requiredVariables = templateVariables.filter(v => v.required);\n    return requiredVariables.every(variable => formData[variable.key]?.trim());\n  };\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-4\">\n          <Button variant=\"ghost\" onClick={onCancel}>\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold\">Generate Document</h1>\n            <p className=\"text-muted-foreground\">\n              Create a new version of {template.name}\n            </p>\n          </div>\n        </div>\n        <Button \n          onClick={handleGenerate} \n          disabled={generating || !isFormValid()}\n          className=\"flex items-center gap-2\"\n        >\n          <Wand2 className=\"h-4 w-4\" />\n          {generating ? 'Generating...' : 'Generate Document'}\n        </Button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Form */}\n        <div className=\"space-y-6\">\n          <Tabs value={activeTab} onValueChange={setActiveTab}>\n            <TabsList className=\"grid w-full grid-cols-3\">\n              <TabsTrigger value=\"variables\">Variables</TabsTrigger>\n              <TabsTrigger value=\"version\">Version</TabsTrigger>\n              <TabsTrigger value=\"settings\">Settings</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"variables\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Template Variables</CardTitle>\n                  <CardDescription>\n                    Fill in the variables to customize your document\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  {templateVariables.map((variable) => (\n                    <div key={variable.key} className=\"space-y-2\">\n                      <Label htmlFor={variable.key}>\n                        {variable.label}\n                        {variable.required && <span className=\"text-red-500 ml-1\">*</span>}\n                      </Label>\n                      <Input\n                        id={variable.key}\n                        type={variable.type}\n                        value={formData[variable.key] || ''}\n                        onChange={(e) => setFormData(prev => ({\n                          ...prev,\n                          [variable.key]: e.target.value\n                        }))}\n                        placeholder={variable.placeholder}\n                        required={variable.required}\n                      />\n                      {variable.description && (\n                        <p className=\"text-sm text-muted-foreground\">\n                          {variable.description}\n                        </p>\n                      )}\n                    </div>\n                  ))}\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"version\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Version Information</CardTitle>\n                  <CardDescription>\n                    Configure the new version details\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"version-type\">Version Type</Label>\n                    <Select value={versionType} onValueChange={(value: VersionType) => setVersionType(value)}>\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"patch\">\n                          <div>\n                            <div className=\"font-medium\">Patch (x.x.X)</div>\n                            <div className=\"text-sm text-muted-foreground\">Bug fixes and minor changes</div>\n                          </div>\n                        </SelectItem>\n                        <SelectItem value=\"minor\">\n                          <div>\n                            <div className=\"font-medium\">Minor (x.X.0)</div>\n                            <div className=\"text-sm text-muted-foreground\">New features and improvements</div>\n                          </div>\n                        </SelectItem>\n                        <SelectItem value=\"major\">\n                          <div>\n                            <div className=\"font-medium\">Major (X.0.0)</div>\n                            <div className=\"text-sm text-muted-foreground\">Breaking changes and major updates</div>\n                          </div>\n                        </SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"changelog\">Changelog</Label>\n                    <Textarea\n                      id=\"changelog\"\n                      value={changelog}\n                      onChange={(e) => setChangelog(e.target.value)}\n                      placeholder=\"Describe what changed in this version...\"\n                      rows={3}\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"settings\" className=\"space-y-4\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Publishing Settings</CardTitle>\n                  <CardDescription>\n                    Configure how this version should be handled\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id=\"auto-publish\"\n                      checked={autoPublish}\n                      onCheckedChange={(checked) => setAutoPublish(checked as boolean)}\n                    />\n                    <Label htmlFor=\"auto-publish\">Auto-publish this version</Label>\n                  </div>\n                  <p className=\"text-sm text-muted-foreground\">\n                    If enabled, this version will be automatically published and made live.\n                  </p>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id=\"notify-users\"\n                      checked={notifyUsers}\n                      onCheckedChange={(checked) => setNotifyUsers(checked as boolean)}\n                      disabled={!autoPublish}\n                    />\n                    <Label htmlFor=\"notify-users\">Notify existing users</Label>\n                  </div>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Send notifications to users who have previously consented to this document type.\n                  </p>\n                </CardContent>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n\n        {/* Preview */}\n        <div className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Eye className=\"h-5 w-5\" />\n                Live Preview\n              </CardTitle>\n              <CardDescription>\n                Preview of your generated document\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"prose prose-sm max-w-none\">\n                <pre className=\"whitespace-pre-wrap text-xs bg-muted p-4 rounded max-h-96 overflow-y-auto\">\n                  {previewContent}\n                </pre>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAbA;;;;;;;;;;;;;AA+BA,MAAM,mBAAuC;IAC3C;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;IACf;CACD;AAEM,SAAS,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAA0B;IAChG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,IAAI,iIAAA,CAAA,4BAAyB,CAAC;IAEtD,kCAAkC;IAClC,MAAM,oBAAoB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACtC,MAAM,gBAAgB;QACtB,MAAM,UAAU,SAAS,iBAAiB,CAAC,KAAK,CAAC,kBAAkB,EAAE;QACrE,MAAM,kBAAkB;eAAI,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,QAAS,MAAM,OAAO,CAAC,SAAS;SAAM;QAEtF,OAAO,gBAAgB,GAAG,CAAC,CAAA;YACzB,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;YACvD,OAAO,aAAa;gBAClB,KAAK;gBACL,OAAO,QAAQ,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;gBACrE,MAAM;gBACN,UAAU;gBACV,aAAa,CAAC,MAAM,EAAE,QAAQ,OAAO,CAAC,MAAM,MAAM;YACpD;QACF;IACF,GAAG;QAAC,SAAS,iBAAiB;KAAC;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,MAAM,cAAsC,CAAC;QAC7C,kBAAkB,OAAO,CAAC,CAAA;YACxB,WAAW,CAAC,SAAS,GAAG,CAAC,GAAG;QAC9B;QACA,YAAY;IACd,GAAG;QAAC;KAAkB;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU,SAAS,iBAAiB;KAAC;IAEzC,MAAM,kBAAkB;QACtB,IAAI,UAAU,SAAS,iBAAiB;QAExC,4CAA4C;QAC5C,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC5C,UAAU,QAAQ,OAAO,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC9E;QAEA,2BAA2B;QAC3B,UAAU,QAAQ,OAAO,CAAC,yBAAyB,IAAI,OAAO,kBAAkB;QAChF,UAAU,QAAQ,OAAO,CAAC,yBAAyB,IAAI,OAAO,WAAW,GAAG,QAAQ;QAEpF,kBAAkB;IACpB;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI;YACF,MAAM,UAAqC;gBACzC,aAAa,SAAS,EAAE;gBACxB,eAAe;oBACb,GAAG,QAAQ;oBACX,cAAc,IAAI,OAAO,kBAAkB;oBAC3C,cAAc,IAAI,OAAO,WAAW,GAAG,QAAQ;gBACjD;gBACA,cAAc;gBACd,WAAW,UAAU,IAAI,MAAM;gBAC/B,cAAc;gBACd,cAAc;YAChB;YAEA,MAAM,WAAW,MAAM,gBAAgB,gBAAgB,CAAC;YAExD,IAAI,SAAS,OAAO,EAAE;gBACpB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,8BAA8B,SAAS,KAAK;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ;QAClE,OAAO,kBAAkB,KAAK,CAAC,CAAA,WAAY,QAAQ,CAAC,SAAS,GAAG,CAAC,EAAE;IACrE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,SAAS;;kDAC/B,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;;4CAAwB;4CACV,SAAS,IAAI;;;;;;;;;;;;;;;;;;;kCAI5C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,cAAc,CAAC;wBACzB,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAChB,aAAa,kBAAkB;;;;;;;;;;;;;0BAIpC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;;8CACrC,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAY;;;;;;sDAC/B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;sDAC7B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;;;;;;;8CAGhC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CACvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;wDAAuB,WAAU;;0EAChC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAS,SAAS,GAAG;;oEACzB,SAAS,KAAK;oEACd,SAAS,QAAQ,kBAAI,8OAAC;wEAAK,WAAU;kFAAoB;;;;;;;;;;;;0EAE5D,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAI,SAAS,GAAG;gEAChB,MAAM,SAAS,IAAI;gEACnB,OAAO,QAAQ,CAAC,SAAS,GAAG,CAAC,IAAI;gEACjC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EACpC,GAAG,IAAI;4EACP,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;wEAChC,CAAC;gEACD,aAAa,SAAS,WAAW;gEACjC,UAAU,SAAS,QAAQ;;;;;;4DAE5B,SAAS,WAAW,kBACnB,8OAAC;gEAAE,WAAU;0EACV,SAAS,WAAW;;;;;;;uDAlBjB,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;8CA2B9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACrC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;0EAC9B,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAa,eAAe,CAAC,QAAuB,eAAe;;kFAChF,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAChB,cAAA,8OAAC;;sGACC,8OAAC;4FAAI,WAAU;sGAAc;;;;;;sGAC7B,8OAAC;4FAAI,WAAU;sGAAgC;;;;;;;;;;;;;;;;;0FAGnD,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAChB,cAAA,8OAAC;;sGACC,8OAAC;4FAAI,WAAU;sGAAc;;;;;;sGAC7B,8OAAC;4FAAI,WAAU;sGAAgC;;;;;;;;;;;;;;;;;0FAGnD,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAChB,cAAA,8OAAC;;sGACC,8OAAC;4FAAI,WAAU;sGAAc;;;;;;sGAC7B,8OAAC;4FAAI,WAAU;sGAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAOzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,aAAY;gEACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOhB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,SAAS;gEACT,iBAAiB,CAAC,UAAY,eAAe;;;;;;0EAE/C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;;;;;;;kEAEhC,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;kEAI7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,SAAS;gEACT,iBAAiB,CAAC,UAAY,eAAe;gEAC7C,UAAU,CAAC;;;;;;0EAEb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;;;;;;;kEAEhC,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG7B,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnB", "debugId": null}}, {"offset": {"line": 3133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3307, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3372, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/documents/version-publisher.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Badge } from '@/components/ui/badge';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport {\n  Send,\n  Users,\n  Mail,\n  MessageSquare,\n  Webhook,\n  AlertTriangle,\n  CheckCircle,\n  Clock\n} from 'lucide-react';\nimport { DocumentVersion, VersionPublishingRequest } from '@/lib/document-types';\nimport { DocumentManagementService } from '@/lib/document-service';\n\ninterface VersionPublisherProps {\n  version: DocumentVersion;\n  tenantId: string;\n  onPublish: () => void;\n  onCancel: () => void;\n}\n\nexport function VersionPublisher({ version, tenantId, onPublish, onCancel }: VersionPublisherProps) {\n  const [notifyUsers, setNotifyUsers] = useState(true);\n  const [notificationMessage, setNotificationMessage] = useState('');\n  const [publishing, setPublishing] = useState(false);\n  const [estimatedUsers, setEstimatedUsers] = useState(0);\n\n  const documentService = new DocumentManagementService(tenantId);\n\n  React.useEffect(() => {\n    // TODO: Fetch estimated number of users who will be notified\n    setEstimatedUsers(42); // Mock data\n  }, []);\n\n  const handlePublish = async () => {\n    setPublishing(true);\n    try {\n      const request: VersionPublishingRequest = {\n        version_id: version.id,\n        notify_existing_users: notifyUsers,\n        notification_message: notificationMessage.trim() || undefined\n      };\n\n      const response = await documentService.publishVersion(request);\n      \n      if (response.success) {\n        onPublish();\n      } else {\n        console.error('Error publishing version:', response.error);\n      }\n    } catch (error) {\n      console.error('Error publishing version:', error);\n    } finally {\n      setPublishing(false);\n    }\n  };\n\n  const getVersionTypeColor = (type: string) => {\n    switch (type) {\n      case 'major':\n        return 'bg-red-100 text-red-800';\n      case 'minor':\n        return 'bg-blue-100 text-blue-800';\n      case 'patch':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getImpactLevel = (versionType: string) => {\n    switch (versionType) {\n      case 'major':\n        return {\n          level: 'High Impact',\n          description: 'Major changes that may affect user rights or data processing',\n          color: 'text-red-600',\n          icon: <AlertTriangle className=\"h-4 w-4\" />\n        };\n      case 'minor':\n        return {\n          level: 'Medium Impact',\n          description: 'New features or improvements to existing policies',\n          color: 'text-yellow-600',\n          icon: <Clock className=\"h-4 w-4\" />\n        };\n      case 'patch':\n        return {\n          level: 'Low Impact',\n          description: 'Minor fixes and clarifications',\n          color: 'text-green-600',\n          icon: <CheckCircle className=\"h-4 w-4\" />\n        };\n      default:\n        return {\n          level: 'Unknown Impact',\n          description: 'Impact level not determined',\n          color: 'text-gray-600',\n          icon: <AlertTriangle className=\"h-4 w-4\" />\n        };\n    }\n  };\n\n  const impact = getImpactLevel(version.version_type);\n\n  return (\n    <Dialog open={true} onOpenChange={onCancel}>\n      <DialogContent className=\"max-w-2xl\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Send className=\"h-5 w-5\" />\n            Publish Version {version.version_number}\n          </DialogTitle>\n          <DialogDescription>\n            Review and configure the publishing settings for this document version\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Version Summary */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Version Summary</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center gap-4\">\n                <Badge className={getVersionTypeColor(version.version_type)}>\n                  {version.version_type} v{version.version_number}\n                </Badge>\n                <div className={`flex items-center gap-2 ${impact.color}`}>\n                  {impact.icon}\n                  <span className=\"font-medium\">{impact.level}</span>\n                </div>\n              </div>\n\n              <div>\n                <Label className=\"text-sm font-medium\">Changelog</Label>\n                <p className=\"text-sm text-muted-foreground mt-1\">\n                  {version.changelog || 'No changelog provided'}\n                </p>\n              </div>\n\n              <div>\n                <Label className=\"text-sm font-medium\">Impact Description</Label>\n                <p className=\"text-sm text-muted-foreground mt-1\">\n                  {impact.description}\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Publishing Impact */}\n          <Alert>\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription>\n              <strong>Publishing Impact:</strong> This action will make version {version.version_number} the \n              live version of this document. Previous published versions will be archived.\n            </AlertDescription>\n          </Alert>\n\n          {/* Notification Settings */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg flex items-center gap-2\">\n                <Users className=\"h-5 w-5\" />\n                User Notifications\n              </CardTitle>\n              <CardDescription>\n                Configure how existing users will be notified about this update\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id=\"notify-users\"\n                  checked={notifyUsers}\n                  onCheckedChange={(checked) => setNotifyUsers(checked as boolean)}\n                />\n                <Label htmlFor=\"notify-users\" className=\"flex items-center gap-2\">\n                  <Mail className=\"h-4 w-4\" />\n                  Notify existing users about this update\n                </Label>\n              </div>\n\n              {notifyUsers && (\n                <div className=\"space-y-4 pl-6 border-l-2 border-muted\">\n                  <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4\" />\n                      <span>Estimated recipients: {estimatedUsers} users</span>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"notification-message\">\n                      Custom notification message (optional)\n                    </Label>\n                    <Textarea\n                      id=\"notification-message\"\n                      value={notificationMessage}\n                      onChange={(e) => setNotificationMessage(e.target.value)}\n                      placeholder=\"Add a custom message to explain the changes to your users...\"\n                      rows={3}\n                    />\n                    <p className=\"text-xs text-muted-foreground\">\n                      This message will be included in the notification email along with the document changes.\n                    </p>\n                  </div>\n\n                  <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                    <div className=\"flex items-center gap-2 text-muted-foreground\">\n                      <Mail className=\"h-4 w-4\" />\n                      <span>Email</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 text-muted-foreground\">\n                      <MessageSquare className=\"h-4 w-4\" />\n                      <span>SMS (if enabled)</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 text-muted-foreground\">\n                      <Webhook className=\"h-4 w-4\" />\n                      <span>Webhooks</span>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Compliance Notice */}\n          {version.version_type === 'major' && (\n            <Alert>\n              <AlertTriangle className=\"h-4 w-4\" />\n              <AlertDescription>\n                <strong>Compliance Notice:</strong> Major version changes may require explicit user consent \n                under GDPR and other privacy regulations. Ensure you have the legal basis for these changes.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"flex justify-end gap-3 pt-4 border-t\">\n            <Button variant=\"outline\" onClick={onCancel} disabled={publishing}>\n              Cancel\n            </Button>\n            <Button \n              onClick={handlePublish} \n              disabled={publishing}\n              className=\"flex items-center gap-2\"\n            >\n              <Send className=\"h-4 w-4\" />\n              {publishing ? 'Publishing...' : `Publish Version ${version.version_number}`}\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAtBA;;;;;;;;;;;;;AA+BO,SAAS,iBAAiB,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAyB;IAChG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,kBAAkB,IAAI,iIAAA,CAAA,4BAAyB,CAAC;IAEtD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,6DAA6D;QAC7D,kBAAkB,KAAK,YAAY;IACrC,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,cAAc;QACd,IAAI;YACF,MAAM,UAAoC;gBACxC,YAAY,QAAQ,EAAE;gBACtB,uBAAuB;gBACvB,sBAAsB,oBAAoB,IAAI,MAAM;YACtD;YAEA,MAAM,WAAW,MAAM,gBAAgB,cAAc,CAAC;YAEtD,IAAI,SAAS,OAAO,EAAE;gBACpB;YACF,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B,SAAS,KAAK;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;gBACjC;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;gBACzB;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,oBAAM,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;gBAC/B;YACF;gBACE,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,OAAO;oBACP,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;gBACjC;QACJ;IACF;IAEA,MAAM,SAAS,eAAe,QAAQ,YAAY;IAElD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;gCACX,QAAQ,cAAc;;;;;;;sCAEzC,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;;;;;;8CAEjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAW,oBAAoB,QAAQ,YAAY;;wDACvD,QAAQ,YAAY;wDAAC;wDAAG,QAAQ,cAAc;;;;;;;8DAEjD,8OAAC;oDAAI,WAAW,CAAC,wBAAwB,EAAE,OAAO,KAAK,EAAE;;wDACtD,OAAO,IAAI;sEACZ,8OAAC;4DAAK,WAAU;sEAAe,OAAO,KAAK;;;;;;;;;;;;;;;;;;sDAI/C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DACV,QAAQ,SAAS,IAAI;;;;;;;;;;;;sDAI1B,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DACvC,8OAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAO3B,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;;sDACf,8OAAC;sDAAO;;;;;;wCAA2B;wCAAgC,QAAQ,cAAc;wCAAC;;;;;;;;;;;;;sCAM9F,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG/B,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,SAAS;oDACT,iBAAiB,CAAC,UAAY,eAAe;;;;;;8DAE/C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAe,WAAU;;sEACtC,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;wCAK/B,6BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;;oEAAK;oEAAuB;oEAAe;;;;;;;;;;;;;;;;;;8DAIhD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAuB;;;;;;sEAGtC,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO;4DACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;4DACtD,aAAY;4DACZ,MAAM;;;;;;sEAER,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAK/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;8EACzB,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASjB,QAAQ,YAAY,KAAK,yBACxB,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;;sDACf,8OAAC;sDAAO;;;;;;wCAA2B;;;;;;;;;;;;;sCAOzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;oCAAU,UAAU;8CAAY;;;;;;8CAGnE,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,aAAa,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,cAAc,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzF", "debugId": null}}, {"offset": {"line": 4034, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/documents/version-history.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport {\n  ArrowLeft,\n  Plus,\n  Eye,\n  Download,\n  Share,\n  Clock,\n  Users,\n  CheckCircle,\n  AlertCircle,\n  FileText,\n  GitBranch,\n  Send,\n  FolderArchive\n} from 'lucide-react';\nimport { DocumentTemplate, DocumentVersion } from '@/lib/document-types';\nimport { DocumentManagementService } from '@/lib/document-service';\nimport { DocumentGenerator } from './document-generator';\nimport { VersionPublisher } from './version-publisher';\n\ninterface VersionHistoryProps {\n  template: DocumentTemplate;\n  tenantId: string;\n  onBack: () => void;\n}\n\nexport function VersionHistory({ template, tenantId, onBack }: VersionHistoryProps) {\n  // Early return if required props are not provided\n  if (!template || !template.id || !tenantId || tenantId.trim() === '') {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold mb-2\">Invalid Configuration</h2>\n          <p className=\"text-muted-foreground mb-4\">Template and tenant ID are required to view version history.</p>\n          <Button onClick={onBack}>Go Back</Button>\n        </div>\n      </div>\n    );\n  }\n\n  const [versions, setVersions] = useState<DocumentVersion[]>([]);\n  const [selectedVersion, setSelectedVersion] = useState<DocumentVersion | null>(null);\n  const [showGenerator, setShowGenerator] = useState(false);\n  const [showPublisher, setShowPublisher] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [previewContent, setPreviewContent] = useState('');\n\n  const documentService = new DocumentManagementService(tenantId);\n\n  useEffect(() => {\n    loadVersions();\n  }, [template.id]);\n\n  const loadVersions = async () => {\n    setLoading(true);\n    try {\n      const response = await documentService.getVersions(template.id);\n      if (response.success && response.data) {\n        setVersions(response.data);\n      } else {\n        console.warn('No versions found or error loading versions:', response.error || 'Unknown error');\n        setVersions([]); // Set empty array as fallback\n      }\n    } catch (error) {\n      console.warn('Error loading versions, using empty array:',\n        error instanceof Error ? error.message : 'Unknown error');\n      setVersions([]); // Set empty array as fallback\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateVersion = () => {\n    setShowGenerator(true);\n  };\n\n  const handlePublishVersion = (version: DocumentVersion) => {\n    setSelectedVersion(version);\n    setShowPublisher(true);\n  };\n\n  const handleViewVersion = async (version: DocumentVersion) => {\n    setSelectedVersion(version);\n    setPreviewContent(version.markdown_content);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'published':\n        return 'bg-green-100 text-green-800';\n      case 'draft':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'archived':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'published':\n        return <CheckCircle className=\"h-4 w-4\" />;\n      case 'draft':\n        return <Clock className=\"h-4 w-4\" />;\n      case 'archived':\n        return <FolderArchive className=\"h-4 w-4\" />;\n      default:\n        return <AlertCircle className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getVersionTypeColor = (type: string) => {\n    switch (type) {\n      case 'major':\n        return 'bg-red-100 text-red-800';\n      case 'minor':\n        return 'bg-blue-100 text-blue-800';\n      case 'patch':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (showGenerator) {\n    return (\n      <DocumentGenerator\n        template={template}\n        tenantId={tenantId}\n        onSave={() => {\n          setShowGenerator(false);\n          loadVersions();\n        }}\n        onCancel={() => setShowGenerator(false)}\n      />\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-4\">\n          <Button variant=\"ghost\" onClick={onBack}>\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Templates\n          </Button>\n          <div>\n            <h1 className=\"text-3xl font-bold\">{template.name}</h1>\n            <p className=\"text-muted-foreground\">Version History & Management</p>\n          </div>\n        </div>\n        <Button onClick={handleCreateVersion} className=\"flex items-center gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          New Version\n        </Button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Version List */}\n        <div className=\"lg:col-span-2 space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <GitBranch className=\"h-5 w-5\" />\n                Version History\n              </CardTitle>\n              <CardDescription>\n                All versions of {template.name}\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {loading ? (\n                <div className=\"space-y-4\">\n                  {[1, 2, 3].map((i) => (\n                    <div key={i} className=\"animate-pulse\">\n                      <div className=\"h-16 bg-gray-200 rounded\"></div>\n                    </div>\n                  ))}\n                </div>\n              ) : versions.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <FileText className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">No versions yet</h3>\n                  <p className=\"text-muted-foreground mb-4\">\n                    Create your first version to get started.\n                  </p>\n                  <Button onClick={handleCreateVersion}>\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Create Version\n                  </Button>\n                </div>\n              ) : (\n                <div className=\"space-y-4\">\n                  {versions.map((version) => (\n                    <div\n                      key={version.id}\n                      className=\"border rounded-lg p-4 hover:bg-muted/50 transition-colors\"\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <Badge className={getVersionTypeColor(version.version_type)}>\n                              v{version.version_number}\n                            </Badge>\n                            <Badge className={getStatusColor(version.status)}>\n                              <span className=\"flex items-center gap-1\">\n                                {getStatusIcon(version.status)}\n                                {version.status}\n                              </span>\n                            </Badge>\n                          </div>\n                          \n                          <p className=\"text-sm text-muted-foreground mb-2\">\n                            {version.changelog || 'No changelog provided'}\n                          </p>\n                          \n                          <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                            <span>Created: {new Date(version.created_at).toLocaleDateString()}</span>\n                            {version.published_at && (\n                              <span>Published: {new Date(version.published_at).toLocaleDateString()}</span>\n                            )}\n                          </div>\n                        </div>\n                        \n                        <div className=\"flex gap-2\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => handleViewVersion(version)}\n                          >\n                            <Eye className=\"h-3 w-3 mr-1\" />\n                            View\n                          </Button>\n                          \n                          {version.status === 'draft' && (\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => handlePublishVersion(version)}\n                            >\n                              <Send className=\"h-3 w-3 mr-1\" />\n                              Publish\n                            </Button>\n                          )}\n                          \n                          {version.pdf_url && (\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={() => window.open(version.pdf_url, '_blank')}\n                            >\n                              <Download className=\"h-3 w-3 mr-1\" />\n                              PDF\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Version Preview */}\n        <div className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Eye className=\"h-5 w-5\" />\n                Preview\n              </CardTitle>\n              <CardDescription>\n                {selectedVersion \n                  ? `Version ${selectedVersion.version_number}` \n                  : 'Select a version to preview'\n                }\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {selectedVersion ? (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center gap-2\">\n                    <Badge className={getStatusColor(selectedVersion.status)}>\n                      {selectedVersion.status}\n                    </Badge>\n                    <Badge className={getVersionTypeColor(selectedVersion.version_type)}>\n                      {selectedVersion.version_type}\n                    </Badge>\n                  </div>\n                  \n                  <div className=\"prose prose-sm max-w-none\">\n                    <pre className=\"whitespace-pre-wrap text-xs bg-muted p-3 rounded max-h-96 overflow-y-auto\">\n                      {previewContent}\n                    </pre>\n                  </div>\n                  \n                  <div className=\"flex gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"flex-1\"\n                      onClick={() => {\n                        navigator.clipboard.writeText(previewContent);\n                      }}\n                    >\n                      Copy Content\n                    </Button>\n                    {selectedVersion.pdf_url && (\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => window.open(selectedVersion.pdf_url, '_blank')}\n                      >\n                        <Download className=\"h-3 w-3\" />\n                      </Button>\n                    )}\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  <FileText className=\"h-8 w-8 mx-auto mb-2\" />\n                  <p>Select a version to preview its content</p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Version Publisher Dialog */}\n      {showPublisher && selectedVersion && (\n        <VersionPublisher\n          version={selectedVersion}\n          tenantId={tenantId}\n          onPublish={() => {\n            setShowPublisher(false);\n            loadVersions();\n          }}\n          onCancel={() => setShowPublisher(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AA1BA;;;;;;;;;;AAkCO,SAAS,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAuB;IAChF,kDAAkD;IAClD,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;QACpE,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;kCAAQ;;;;;;;;;;;;;;;;;IAIjC;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,kBAAkB,IAAI,iIAAA,CAAA,4BAAyB,CAAC;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,SAAS,EAAE;KAAC;IAEhB,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,WAAW,CAAC,SAAS,EAAE;YAC9D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,YAAY,SAAS,IAAI;YAC3B,OAAO;gBACL,QAAQ,IAAI,CAAC,gDAAgD,SAAS,KAAK,IAAI;gBAC/E,YAAY,EAAE,GAAG,8BAA8B;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,8CACX,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,YAAY,EAAE,GAAG,8BAA8B;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,iBAAiB;IACnB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,mBAAmB;QACnB,kBAAkB,QAAQ,gBAAgB;IAC5C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,eAAe;QACjB,qBACE,8OAAC,wJAAA,CAAA,oBAAiB;YAChB,UAAU;YACV,UAAU;YACV,QAAQ;gBACN,iBAAiB;gBACjB;YACF;YACA,UAAU,IAAM,iBAAiB;;;;;;IAGvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,SAAS;;kDAC/B,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsB,SAAS,IAAI;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAGzC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAqB,WAAU;;0CAC9C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAKhC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGnC,8OAAC,gIAAA,CAAA,kBAAe;;gDAAC;gDACE,SAAS,IAAI;;;;;;;;;;;;;8CAGlC,8OAAC,gIAAA,CAAA,cAAW;8CACT,wBACC,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;gDAAY,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;;;;;+CADP;;;;;;;;;+CAKZ,SAAS,MAAM,KAAK,kBACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;;kEACf,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;6DAKrC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAEC,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAW,oBAAoB,QAAQ,YAAY;;gFAAG;gFACzD,QAAQ,cAAc;;;;;;;sFAE1B,8OAAC,iIAAA,CAAA,QAAK;4EAAC,WAAW,eAAe,QAAQ,MAAM;sFAC7C,cAAA,8OAAC;gFAAK,WAAU;;oFACb,cAAc,QAAQ,MAAM;oFAC5B,QAAQ,MAAM;;;;;;;;;;;;;;;;;;8EAKrB,8OAAC;oEAAE,WAAU;8EACV,QAAQ,SAAS,IAAI;;;;;;8EAGxB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAK;gFAAU,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;wEAC9D,QAAQ,YAAY,kBACnB,8OAAC;;gFAAK;gFAAY,IAAI,KAAK,QAAQ,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;sEAKzE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,kBAAkB;;sFAEjC,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAIjC,QAAQ,MAAM,KAAK,yBAClB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB;;sFAEpC,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAKpC,QAAQ,OAAO,kBACd,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;;sFAE5C,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;+CAxDxC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAuE7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG7B,8OAAC,gIAAA,CAAA,kBAAe;sDACb,kBACG,CAAC,QAAQ,EAAE,gBAAgB,cAAc,EAAE,GAC3C;;;;;;;;;;;;8CAIR,8OAAC,gIAAA,CAAA,cAAW;8CACT,gCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAW,eAAe,gBAAgB,MAAM;kEACpD,gBAAgB,MAAM;;;;;;kEAEzB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAW,oBAAoB,gBAAgB,YAAY;kEAC/D,gBAAgB,YAAY;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;0DAIL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS;4DACP,UAAU,SAAS,CAAC,SAAS,CAAC;wDAChC;kEACD;;;;;;oDAGA,gBAAgB,OAAO,kBACtB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,OAAO,IAAI,CAAC,gBAAgB,OAAO,EAAE;kEAEpD,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;6DAM5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASd,iBAAiB,iCAChB,8OAAC,uJAAA,CAAA,mBAAgB;gBACf,SAAS;gBACT,UAAU;gBACV,WAAW;oBACT,iBAAiB;oBACjB;gBACF;gBACA,UAAU,IAAM,iBAAiB;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 4804, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/documents/consent-analytics.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';\n// Temporarily disabled recharts import to fix compilation\n// import {\n//   Bar<PERSON><PERSON>,\n//   Bar,\n//   XAxis,\n//   YAxis,\n//   CartesianGrid,\n//   Tooltip,\n//   ResponsiveContainer,\n//   Pie<PERSON>hart,\n//   Pie,\n//   Cell,\n//   LineChart,\n//   Line\n// } from 'recharts';\nimport {\n  Users,\n  FileText,\n  CheckCircle,\n  XCircle,\n  TrendingUp,\n  Calendar,\n  Download,\n  Mail,\n  PieChart,\n  BarChart3\n} from 'lucide-react';\nimport { DocumentAnalytics } from '@/lib/document-types';\n\ninterface ConsentAnalyticsProps {\n  tenantId: string;\n}\n\n// Mock data - in real implementation, fetch from API\nconst mockAnalytics = {\n  overview: {\n    totalDocuments: 3,\n    totalVersions: 8,\n    totalConsents: 156,\n    consentRate: 87.5,\n    activeUsers: 142\n  },\n  documentStats: [\n    {\n      template_id: '1',\n      template_name: 'Privacy Policy',\n      template_type: 'privacy_policy',\n      total_versions: 3,\n      current_version: '1.2.0',\n      total_consents: 89,\n      consent_rate: 92.3\n    },\n    {\n      template_id: '2',\n      template_name: 'Terms of Service',\n      template_type: 'terms_of_service',\n      total_versions: 3,\n      current_version: '1.1.0',\n      total_consents: 45,\n      consent_rate: 78.9\n    },\n    {\n      template_id: '3',\n      template_name: 'Cookie Policy',\n      template_type: 'cookie_policy',\n      total_versions: 2,\n      current_version: '1.0.1',\n      total_consents: 22,\n      consent_rate: 95.7\n    }\n  ],\n  consentTrends: [\n    { date: '2024-01-01', consents: 12, notifications: 15 },\n    { date: '2024-01-02', consents: 18, notifications: 22 },\n    { date: '2024-01-03', consents: 25, notifications: 28 },\n    { date: '2024-01-04', consents: 31, notifications: 35 },\n    { date: '2024-01-05', consents: 28, notifications: 32 },\n    { date: '2024-01-06', consents: 35, notifications: 38 },\n    { date: '2024-01-07', consents: 42, notifications: 45 }\n  ],\n  consentByType: [\n    { name: 'Privacy Policy', value: 89, color: '#3b82f6' },\n    { name: 'Terms of Service', value: 45, color: '#10b981' },\n    { name: 'Cookie Policy', value: 22, color: '#f59e0b' }\n  ]\n};\n\nexport function ConsentAnalytics({ tenantId }: ConsentAnalyticsProps) {\n  const [analytics, setAnalytics] = useState(mockAnalytics);\n  const [loading, setLoading] = useState(false);\n  const [timeRange, setTimeRange] = useState('7d');\n\n  useEffect(() => {\n    loadAnalytics();\n  }, [tenantId, timeRange]);\n\n  const loadAnalytics = async () => {\n    setLoading(true);\n    try {\n      // TODO: Fetch real analytics data from API\n      // const response = await fetch(`/api/analytics?tenantId=${tenantId}&range=${timeRange}`);\n      // const data = await response.json();\n      // setAnalytics(data);\n      \n      // For now, use mock data\n      setTimeout(() => {\n        setAnalytics(mockAnalytics);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      console.error('Error loading analytics:', error);\n      setLoading(false);\n    }\n  };\n\n  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Overview Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Documents</p>\n                <p className=\"text-2xl font-bold\">{analytics.overview.totalDocuments}</p>\n              </div>\n              <FileText className=\"h-8 w-8 text-blue-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Versions</p>\n                <p className=\"text-2xl font-bold\">{analytics.overview.totalVersions}</p>\n              </div>\n              <Calendar className=\"h-8 w-8 text-green-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Consents</p>\n                <p className=\"text-2xl font-bold\">{analytics.overview.totalConsents}</p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-emerald-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Consent Rate</p>\n                <p className=\"text-2xl font-bold\">{formatPercentage(analytics.overview.consentRate)}</p>\n              </div>\n              <TrendingUp className=\"h-8 w-8 text-orange-500\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Active Users</p>\n                <p className=\"text-2xl font-bold\">{analytics.overview.activeUsers}</p>\n              </div>\n              <Users className=\"h-8 w-8 text-purple-500\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      <Tabs defaultValue=\"documents\" className=\"space-y-4\">\n        <TabsList>\n          <TabsTrigger value=\"documents\">Document Performance</TabsTrigger>\n          <TabsTrigger value=\"trends\">Consent Trends</TabsTrigger>\n          <TabsTrigger value=\"distribution\">Distribution</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"documents\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Document Performance</CardTitle>\n              <CardDescription>\n                Consent rates and engagement for each document type\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {analytics.documentStats.map((doc) => (\n                  <div key={doc.template_id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-2 mb-2\">\n                        <h4 className=\"font-medium\">{doc.template_name}</h4>\n                        <Badge variant=\"outline\">v{doc.current_version}</Badge>\n                      </div>\n                      <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                        <span>{doc.total_versions} versions</span>\n                        <span>{doc.total_consents} consents</span>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {formatPercentage(doc.consent_rate)}\n                      </div>\n                      <div className=\"text-sm text-muted-foreground\">consent rate</div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"trends\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Consent Trends</CardTitle>\n              <CardDescription>\n                Daily consent and notification activity over time\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <TrendingUp className=\"h-12 w-12 mx-auto text-gray-400 mb-2\" />\n                  <p className=\"text-gray-500\">Consent Trends Chart</p>\n                  <p className=\"text-xs text-gray-400\">Charts will be available once recharts is properly loaded</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"distribution\" className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Consent Distribution</CardTitle>\n                <CardDescription>\n                  Breakdown of consents by document type\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <PieChart className=\"h-12 w-12 mx-auto text-gray-400 mb-2\" />\n                    <p className=\"text-gray-500\">Consent Distribution Chart</p>\n                    <p className=\"text-xs text-gray-400\">Charts will be available once recharts is properly loaded</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>Document Versions</CardTitle>\n                <CardDescription>\n                  Number of versions per document type\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <BarChart3 className=\"h-12 w-12 mx-auto text-gray-400 mb-2\" />\n                    <p className=\"text-gray-500\">Document Versions Chart</p>\n                    <p className=\"text-xs text-gray-400\">Charts will be available once recharts is properly loaded</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D,WAAW;AACX,cAAc;AACd,SAAS;AACT,WAAW;AACX,WAAW;AACX,mBAAmB;AACnB,aAAa;AACb,yBAAyB;AACzB,cAAc;AACd,SAAS;AACT,UAAU;AACV,eAAe;AACf,SAAS;AACT,qBAAqB;AACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AArBA;;;;;;;AAuCA,qDAAqD;AACrD,MAAM,gBAAgB;IACpB,UAAU;QACR,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,aAAa;QACb,aAAa;IACf;IACA,eAAe;QACb;YACE,aAAa;YACb,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;QAChB;QACA;YACE,aAAa;YACb,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;QAChB;QACA;YACE,aAAa;YACb,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;QAChB;KACD;IACD,eAAe;QACb;YAAE,MAAM;YAAc,UAAU;YAAI,eAAe;QAAG;QACtD;YAAE,MAAM;YAAc,UAAU;YAAI,eAAe;QAAG;QACtD;YAAE,MAAM;YAAc,UAAU;YAAI,eAAe;QAAG;QACtD;YAAE,MAAM;YAAc,UAAU;YAAI,eAAe;QAAG;QACtD;YAAE,MAAM;YAAc,UAAU;YAAI,eAAe;QAAG;QACtD;YAAE,MAAM;YAAc,UAAU;YAAI,eAAe;QAAG;QACtD;YAAE,MAAM;YAAc,UAAU;YAAI,eAAe;QAAG;KACvD;IACD,eAAe;QACb;YAAE,MAAM;YAAkB,OAAO;YAAI,OAAO;QAAU;QACtD;YAAE,MAAM;YAAoB,OAAO;YAAI,OAAO;QAAU;QACxD;YAAE,MAAM;YAAiB,OAAO;YAAI,OAAO;QAAU;KACtD;AACH;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,2CAA2C;YAC3C,0FAA0F;YAC1F,sCAAsC;YACtC,sBAAsB;YAEtB,yBAAyB;YACzB,WAAW;gBACT,aAAa;gBACb,WAAW;YACb,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAkB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;IAElE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,UAAU,QAAQ,CAAC,cAAc;;;;;;;;;;;;kDAEtE,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,UAAU,QAAQ,CAAC,aAAa;;;;;;;;;;;;kDAErE,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,UAAU,QAAQ,CAAC,aAAa;;;;;;;;;;;;kDAErE,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,iBAAiB,UAAU,QAAQ,CAAC,WAAW;;;;;;;;;;;;kDAEpF,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,UAAU,QAAQ,CAAC,WAAW;;;;;;;;;;;;kDAEnE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAY,WAAU;;kCACvC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAe;;;;;;;;;;;;kCAGpC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,UAAU,aAAa,CAAC,GAAG,CAAC,CAAC,oBAC5B,8OAAC;gDAA0B,WAAU;;kEACnC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAe,IAAI,aAAa;;;;;;kFAC9C,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;;4EAAU;4EAAE,IAAI,eAAe;;;;;;;;;;;;;0EAEhD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAM,IAAI,cAAc;4EAAC;;;;;;;kFAC1B,8OAAC;;4EAAM,IAAI,cAAc;4EAAC;;;;;;;;;;;;;;;;;;;kEAG9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,IAAI,YAAY;;;;;;0EAEpC,8OAAC;gEAAI,WAAU;0EAAgC;;;;;;;;;;;;;+CAfzC,IAAI,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;kCAwBnC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAS,WAAU;kCACpC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAe,WAAU;kCAC1C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}, {"offset": {"line": 5695, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/documents/document-management.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { \n  FileText, \n  Plus, \n  Eye, \n  Edit, \n  Trash2, \n  Download, \n  Share,\n  Clock,\n  Users,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react';\nimport { DocumentTemplate, DocumentVersion } from '@/lib/document-types';\nimport { DocumentManagementService } from '@/lib/document-service';\nimport { TemplateEditor } from './template-editor';\nimport { VersionHistory } from './version-history';\nimport { ConsentAnalytics } from './consent-analytics';\n\ninterface DocumentManagementProps {\n  tenantId: string;\n}\n\nexport function DocumentManagement({ tenantId }: DocumentManagementProps) {\n  // Early return if tenantId is not provided\n  if (!tenantId || tenantId.trim() === '') {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold mb-2\">Invalid Configuration</h2>\n          <p className=\"text-muted-foreground mb-4\">Tenant ID is required to use document management.</p>\n        </div>\n      </div>\n    );\n  }\n\n  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);\n  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);\n  const [showTemplateEditor, setShowTemplateEditor] = useState(false);\n  const [showVersionHistory, setShowVersionHistory] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('templates');\n\n  const documentService = new DocumentManagementService(tenantId);\n\n  useEffect(() => {\n    loadTemplates();\n  }, [tenantId]);\n\n  const loadTemplates = async () => {\n    setLoading(true);\n    try {\n      const response = await documentService.getTemplates();\n      if (response.success && response.data) {\n        setTemplates(response.data);\n      }\n    } catch (error) {\n      console.error('Error loading templates:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateTemplate = () => {\n    setSelectedTemplate(null);\n    setShowTemplateEditor(true);\n  };\n\n  const handleEditTemplate = (template: DocumentTemplate) => {\n    setSelectedTemplate(template);\n    setShowTemplateEditor(true);\n  };\n\n  const handleViewVersions = (template: DocumentTemplate) => {\n    setSelectedTemplate(template);\n    setShowVersionHistory(true);\n  };\n\n  const getTemplateTypeIcon = (type: string) => {\n    switch (type) {\n      case 'privacy_policy':\n        return <FileText className=\"h-5 w-5 text-blue-500\" />;\n      case 'terms_of_service':\n        return <FileText className=\"h-5 w-5 text-green-500\" />;\n      case 'cookie_policy':\n        return <FileText className=\"h-5 w-5 text-orange-500\" />;\n      default:\n        return <FileText className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getTemplateTypeName = (type: string) => {\n    switch (type) {\n      case 'privacy_policy':\n        return 'Privacy Policy';\n      case 'terms_of_service':\n        return 'Terms of Service';\n      case 'cookie_policy':\n        return 'Cookie Policy';\n      case 'data_processing_agreement':\n        return 'Data Processing Agreement';\n      default:\n        return type.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n    }\n  };\n\n  if (showTemplateEditor) {\n    return (\n      <TemplateEditor\n        template={selectedTemplate}\n        tenantId={tenantId}\n        onSave={() => {\n          setShowTemplateEditor(false);\n          loadTemplates();\n        }}\n        onCancel={() => setShowTemplateEditor(false)}\n      />\n    );\n  }\n\n  if (showVersionHistory && selectedTemplate) {\n    return (\n      <VersionHistory\n        template={selectedTemplate}\n        tenantId={tenantId}\n        onBack={() => setShowVersionHistory(false)}\n      />\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold\">Document Management</h1>\n          <p className=\"text-muted-foreground\">\n            Create and manage privacy policies, terms of service, and other legal documents\n          </p>\n        </div>\n        <Button onClick={handleCreateTemplate} className=\"flex items-center gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          New Template\n        </Button>\n      </div>\n\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList>\n          <TabsTrigger value=\"templates\">Templates</TabsTrigger>\n          <TabsTrigger value=\"analytics\">Analytics</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"templates\" className=\"space-y-6\">\n          {loading ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {[1, 2, 3].map((i) => (\n                <Card key={i} className=\"animate-pulse\">\n                  <CardHeader>\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-2\">\n                      <div className=\"h-3 bg-gray-200 rounded\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : templates.length === 0 ? (\n            <Card>\n              <CardContent className=\"flex flex-col items-center justify-center py-12\">\n                <FileText className=\"h-12 w-12 text-muted-foreground mb-4\" />\n                <h3 className=\"text-lg font-semibold mb-2\">No templates yet</h3>\n                <p className=\"text-muted-foreground text-center mb-4\">\n                  Create your first document template to get started with automated policy generation.\n                </p>\n                <Button onClick={handleCreateTemplate}>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Create Template\n                </Button>\n              </CardContent>\n            </Card>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {templates.map((template) => (\n                <Card key={template.id} className=\"hover:shadow-lg transition-shadow\">\n                  <CardHeader>\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center gap-2\">\n                        {getTemplateTypeIcon(template.template_type)}\n                        <div>\n                          <CardTitle className=\"text-lg\">{template.name}</CardTitle>\n                          <CardDescription>\n                            {getTemplateTypeName(template.template_type)}\n                          </CardDescription>\n                        </div>\n                      </div>\n                      <Badge variant={template.is_active ? 'default' : 'secondary'}>\n                        {template.is_active ? 'Active' : 'Inactive'}\n                      </Badge>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      <p className=\"text-sm text-muted-foreground line-clamp-2\">\n                        {template.description || 'No description provided'}\n                      </p>\n                      \n                      <div className=\"flex flex-wrap gap-1\">\n                        {template.compliance_frameworks.map((framework) => (\n                          <Badge key={framework} variant=\"outline\" className=\"text-xs\">\n                            {framework}\n                          </Badge>\n                        ))}\n                      </div>\n\n                      <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                        <div className=\"flex items-center gap-1\">\n                          <Clock className=\"h-3 w-3\" />\n                          {new Date(template.updated_at).toLocaleDateString()}\n                        </div>\n                      </div>\n\n                      <div className=\"flex gap-2 pt-2\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleViewVersions(template)}\n                          className=\"flex-1\"\n                        >\n                          <Eye className=\"h-3 w-3 mr-1\" />\n                          Versions\n                        </Button>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleEditTemplate(template)}\n                          className=\"flex-1\"\n                        >\n                          <Edit className=\"h-3 w-3 mr-1\" />\n                          Edit\n                        </Button>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"analytics\">\n          <ConsentAnalytics tenantId={tenantId} />\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AAxBA;;;;;;;;;;;;AA8BO,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,2CAA2C;IAC3C,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,IAAI,iIAAA,CAAA,4BAAyB,CAAC;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,YAAY;YACnD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,SAAS,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;QACpB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,sBAAsB;IACxB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,KAAK,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;QACrE;IACF;IAEA,IAAI,oBAAoB;QACtB,qBACE,8OAAC,qJAAA,CAAA,iBAAc;YACb,UAAU;YACV,UAAU;YACV,QAAQ;gBACN,sBAAsB;gBACtB;YACF;YACA,UAAU,IAAM,sBAAsB;;;;;;IAG5C;IAEA,IAAI,sBAAsB,kBAAkB;QAC1C,qBACE,8OAAC,qJAAA,CAAA,iBAAc;YACb,UAAU;YACV,UAAU;YACV,QAAQ,IAAM,sBAAsB;;;;;;IAG1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAsB,WAAU;;0CAC/C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAKhC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;;;;;;;kCAGjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACtC,wBACC,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,gIAAA,CAAA,OAAI;oCAAS,WAAU;;sDACtB,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;mCARV;;;;;;;;;mCAcb,UAAU,MAAM,KAAK,kBACvB,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;kDAGtD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;iDAMvC,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;oCAAmB,WAAU;;sDAChC,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,oBAAoB,SAAS,aAAa;0EAC3C,8OAAC;;kFACC,8OAAC,gIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAW,SAAS,IAAI;;;;;;kFAC7C,8OAAC,gIAAA,CAAA,kBAAe;kFACb,oBAAoB,SAAS,aAAa;;;;;;;;;;;;;;;;;;kEAIjD,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,SAAS,SAAS,GAAG,YAAY;kEAC9C,SAAS,SAAS,GAAG,WAAW;;;;;;;;;;;;;;;;;sDAIvC,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,SAAS,WAAW,IAAI;;;;;;kEAG3B,8OAAC;wDAAI,WAAU;kEACZ,SAAS,qBAAqB,CAAC,GAAG,CAAC,CAAC,0BACnC,8OAAC,iIAAA,CAAA,QAAK;gEAAiB,SAAQ;gEAAU,WAAU;0EAChD;+DADS;;;;;;;;;;kEAMhB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;;;;;;kEAIrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,mBAAmB;gEAClC,WAAU;;kFAEV,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,mBAAmB;gEAClC,WAAU;;kFAEV,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;mCAtDhC,SAAS,EAAE;;;;;;;;;;;;;;;kCAkE9B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,uJAAA,CAAA,mBAAgB;4BAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 6329, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/app/documents/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { DocumentManagement } from '@/components/documents/document-management';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { FileText, AlertCircle, ArrowLeft } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function DocumentsPage() {\n  const [tenantId, setTenantId] = useState<string>('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    // Get tenant ID from URL params or default tenant\n    const urlParams = new URLSearchParams(window.location.search);\n    const paramTenantId = urlParams.get('tenantId');\n    \n    if (paramTenantId) {\n      setTenantId(paramTenantId);\n    } else {\n      // Use default tenant ID for demo\n      setTenantId('550e8400-e29b-41d4-a716-446655440000');\n    }\n    \n    setLoading(false);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <Alert variant=\"destructive\">\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      </div>\n    );\n  }\n\n  if (!tenantId || tenantId.trim() === '') {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <FileText className=\"h-6 w-6\" />\n              Document Management\n            </CardTitle>\n            <CardDescription>\n              Tenant ID is required to access document management\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <p className=\"text-muted-foreground\">\n                Please provide a tenant ID to access the document management system.\n              </p>\n              <div className=\"flex gap-2\">\n                <Link href=\"/dashboard\">\n                  <Button variant=\"outline\">\n                    <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    Back to Dashboard\n                  </Button>\n                </Link>\n                <Button onClick={() => setTenantId('550e8400-e29b-41d4-a716-446655440000')}>\n                  Use Demo Tenant\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return <DocumentManagement tenantId={tenantId} />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC5D,MAAM,gBAAgB,UAAU,GAAG,CAAC;QAEpC,IAAI,eAAe;YACjB,YAAY;QACd,OAAO;YACL,iCAAiC;YACjC,YAAY;QACd;QAEA,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC,iIAAA,CAAA,mBAAgB;kCAAE;;;;;;;;;;;;;;;;;IAI3B;IAEA,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAI1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,YAAY;sDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS1F;IAEA,qBAAO,8OAAC,yJAAA,CAAA,qBAAkB;QAAC,UAAU;;;;;;AACvC", "debugId": null}}]}
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { User, Mail, Shield, AlertCircle } from 'lucide-react';
import type { PrivacyRegion } from '@/lib/utils';

const accountFormSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  dataResidencyPreference: z.enum(['SAME_REGION', 'GLOBAL', 'CUSTOM']),
  marketingEmails: z.boolean().default(false),
  analyticsOptIn: z.boolean().default(false),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions'
  }),
  acceptPrivacyPolicy: z.boolean().refine(val => val === true, {
    message: 'You must accept the privacy policy'
  }),
  ageVerification: z.boolean().refine(val => val === true, {
    message: 'You must confirm you are of legal age'
  }),
});

type AccountFormData = z.infer<typeof accountFormSchema>;

interface AccountFormProps {
  selectedRegion: PrivacyRegion;
  onSubmit: (data: AccountFormData) => void;
  onBack: () => void;
  isLoading?: boolean;
}

export function AccountForm({ selectedRegion, onSubmit, onBack, isLoading = false }: AccountFormProps) {
  const [showDataResidencyInfo, setShowDataResidencyInfo] = useState(false);

  const form = useForm<AccountFormData>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      dataResidencyPreference: 'SAME_REGION',
      marketingEmails: false,
      analyticsOptIn: false,
      acceptTerms: false,
      acceptPrivacyPolicy: false,
      ageVerification: false,
    },
  });

  const handleSubmit = (data: AccountFormData) => {
    onSubmit(data);
  };

  const dataResidencyOptions = [
    {
      value: 'SAME_REGION',
      label: 'Same Region Only',
      description: `Store all data in ${selectedRegion} region only`
    },
    {
      value: 'GLOBAL',
      label: 'Global (Optimized)',
      description: 'Allow global storage for better performance (with privacy protections)'
    },
    {
      value: 'CUSTOM',
      label: 'Custom Configuration',
      description: 'Configure specific data storage preferences'
    }
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-purple-100 rounded-full">
            <User className="h-8 w-8 text-purple-600" />
          </div>
        </div>
        <CardTitle className="text-2xl">Create Your Account</CardTitle>
        <CardDescription className="text-lg">
          Complete your privacy-first account setup
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Personal Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input 
                          type="email" 
                          placeholder="Enter your email address" 
                          className="pl-10"
                          {...field} 
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Data Residency Preferences */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-medium">Data Storage Preferences</h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDataResidencyInfo(!showDataResidencyInfo)}
                >
                  <Shield className="h-4 w-4" />
                </Button>
              </div>

              {showDataResidencyInfo && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Data residency determines where your personal information is physically stored. 
                    This affects which privacy laws apply and data transfer restrictions.
                  </AlertDescription>
                </Alert>
              )}

              <FormField
                control={form.control}
                name="dataResidencyPreference"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data Storage Preference</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select data storage preference" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {dataResidencyOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-sm text-gray-500">{option.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Communication Preferences */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Communication Preferences</h3>
              
              <FormField
                control={form.control}
                name="marketingEmails"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        Marketing Communications
                      </FormLabel>
                      <FormDescription>
                        Receive promotional emails, product updates, and special offers
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="analyticsOptIn"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        Usage Analytics
                      </FormLabel>
                      <FormDescription>
                        Help us improve our service by sharing anonymous usage data
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* Legal Agreements */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Legal Agreements</h3>
              
              <FormField
                control={form.control}
                name="acceptTerms"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        I accept the <a href="/terms" className="text-blue-600 hover:underline">Terms and Conditions</a>
                      </FormLabel>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="acceptPrivacyPolicy"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        I accept the <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a>
                      </FormLabel>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="ageVerification"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        I confirm that I am at least 16 years old (or the minimum age in my jurisdiction)
                      </FormLabel>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* Navigation */}
            <div className="flex gap-4 pt-4">
              <Button type="button" variant="outline" onClick={onBack} className="flex-1">
                Back
              </Button>
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

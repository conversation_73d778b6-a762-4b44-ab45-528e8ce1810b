// API Route for Document Retrieval
import { NextRequest, NextResponse } from 'next/server';
import { DocumentGeneratorService } from '@/lib/document-generator-service';

// GET /api/document-generator/documents/[documentId] - Get a generated document
export async function GET(
  request: NextRequest,
  { params }: { params: { documentId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentGeneratorService(tenantId);
    const response = await documentService.getDocument(params.documentId);

    return NextResponse.json(response, {
      status: response.success ? 200 : 404
    });
  } catch (error) {
    console.error('Error in GET /api/document-generator/documents/[documentId]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/document-generator/documents/[documentId] - Delete a generated document
export async function DELETE(
  request: NextRequest,
  { params }: { params: { documentId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentGeneratorService(tenantId);
    const response = await documentService.deleteDocument(params.documentId);

    return NextResponse.json(response, {
      status: response.success ? 200 : 404
    });
  } catch (error) {
    console.error('Error in DELETE /api/document-generator/documents/[documentId]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import { useState, useEffect } from 'react';

interface User {
  id: string;
  email?: string;
  name?: string;
  tenantId?: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for demo user ID in localStorage
    const demoUserId = localStorage.getItem('demoUserId');
    if (demoUserId) {
      setUser({
        id: demoUserId,
        email: `${demoUserId}@demo.com`,
        name: 'Demo User',
        tenantId: '550e8400-e29b-41d4-a716-446655440000' // Default tenant ID
      });
    }
    setIsLoading(false);
  }, []);

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
  };
}

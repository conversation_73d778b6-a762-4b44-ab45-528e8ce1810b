'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Save, Eye, Wand2, FileText } from 'lucide-react';
import { DocumentTemplate, VersionType, DocumentGenerationRequest } from '@/lib/document-types';
import { DocumentManagementService } from '@/lib/document-service';

interface DocumentGeneratorProps {
  template: DocumentTemplate;
  tenantId: string;
  onSave: () => void;
  onCancel: () => void;
}

interface TemplateVariable {
  key: string;
  label: string;
  type: 'text' | 'email' | 'url' | 'number' | 'date';
  required: boolean;
  placeholder?: string;
  description?: string;
}

const COMMON_VARIABLES: TemplateVariable[] = [
  {
    key: 'company_name',
    label: 'Company Name',
    type: 'text',
    required: true,
    placeholder: 'Your Company Inc.',
    description: 'The legal name of your company'
  },
  {
    key: 'contact_email',
    label: 'Contact Email',
    type: 'email',
    required: true,
    placeholder: '<EMAIL>',
    description: 'Email address for privacy-related inquiries'
  },
  {
    key: 'website_url',
    label: 'Website URL',
    type: 'url',
    required: false,
    placeholder: 'https://yourcompany.com',
    description: 'Your company website URL'
  },
  {
    key: 'service_name',
    label: 'Service Name',
    type: 'text',
    required: false,
    placeholder: 'Your Service',
    description: 'Name of your service or product'
  },
  {
    key: 'service_description',
    label: 'Service Description',
    type: 'text',
    required: false,
    placeholder: 'Brief description of your service',
    description: 'What your service does'
  },
  {
    key: 'retention_period',
    label: 'Data Retention Period',
    type: 'text',
    required: false,
    placeholder: '2 years',
    description: 'How long you retain personal data'
  },
  {
    key: 'legal_basis',
    label: 'Legal Basis for Processing',
    type: 'text',
    required: false,
    placeholder: 'Legitimate interest and consent',
    description: 'GDPR legal basis for data processing'
  },
  {
    key: 'governing_law',
    label: 'Governing Law',
    type: 'text',
    required: false,
    placeholder: 'State of California',
    description: 'Jurisdiction governing your terms'
  }
];

export function DocumentGenerator({ template, tenantId, onSave, onCancel }: DocumentGeneratorProps) {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [versionType, setVersionType] = useState<VersionType>('minor');
  const [changelog, setChangelog] = useState('');
  const [autoPublish, setAutoPublish] = useState(false);
  const [notifyUsers, setNotifyUsers] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const [activeTab, setActiveTab] = useState('variables');

  const documentService = new DocumentManagementService(tenantId);

  // Extract variables from template
  const templateVariables = React.useMemo(() => {
    const variableRegex = /\{\{(\w+)\}\}/g;
    const matches = template.markdown_template.match(variableRegex) || [];
    const uniqueVariables = [...new Set(matches.map(match => match.replace(/[{}]/g, '')))];
    
    return uniqueVariables.map(varName => {
      const commonVar = COMMON_VARIABLES.find(v => v.key === varName);
      return commonVar || {
        key: varName,
        label: varName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        type: 'text' as const,
        required: false,
        placeholder: `Enter ${varName.replace(/_/g, ' ')}`
      };
    });
  }, [template.markdown_template]);

  useEffect(() => {
    // Initialize form data with empty values
    const initialData: Record<string, string> = {};
    templateVariables.forEach(variable => {
      initialData[variable.key] = '';
    });
    setFormData(initialData);
  }, [templateVariables]);

  useEffect(() => {
    generatePreview();
  }, [formData, template.markdown_template]);

  const generatePreview = () => {
    let preview = template.markdown_template;
    
    // Replace template variables with form data
    Object.entries(formData).forEach(([key, value]) => {
      preview = preview.replace(new RegExp(`{{${key}}}`, 'g'), value || `[${key}]`);
    });

    // Replace system variables
    preview = preview.replace(/\{\{current_date\}\}/g, new Date().toLocaleDateString());
    preview = preview.replace(/\{\{current_year\}\}/g, new Date().getFullYear().toString());

    setPreviewContent(preview);
  };

  const handleGenerate = async () => {
    setGenerating(true);
    try {
      const request: DocumentGenerationRequest = {
        template_id: template.id,
        template_data: {
          ...formData,
          current_date: new Date().toLocaleDateString(),
          current_year: new Date().getFullYear().toString()
        },
        version_type: versionType,
        changelog: changelog.trim() || undefined,
        auto_publish: autoPublish,
        notify_users: notifyUsers
      };

      const response = await documentService.generateDocument(request);
      
      if (response.success) {
        onSave();
      } else {
        console.error('Error generating document:', response.error);
      }
    } catch (error) {
      console.error('Error generating document:', error);
    } finally {
      setGenerating(false);
    }
  };

  const isFormValid = () => {
    const requiredVariables = templateVariables.filter(v => v.required);
    return requiredVariables.every(variable => formData[variable.key]?.trim());
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Generate Document</h1>
            <p className="text-muted-foreground">
              Create a new version of {template.name}
            </p>
          </div>
        </div>
        <Button 
          onClick={handleGenerate} 
          disabled={generating || !isFormValid()}
          className="flex items-center gap-2"
        >
          <Wand2 className="h-4 w-4" />
          {generating ? 'Generating...' : 'Generate Document'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Form */}
        <div className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="variables">Variables</TabsTrigger>
              <TabsTrigger value="version">Version</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="variables" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Template Variables</CardTitle>
                  <CardDescription>
                    Fill in the variables to customize your document
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {templateVariables.map((variable) => (
                    <div key={variable.key} className="space-y-2">
                      <Label htmlFor={variable.key}>
                        {variable.label}
                        {variable.required && <span className="text-red-500 ml-1">*</span>}
                      </Label>
                      <Input
                        id={variable.key}
                        type={variable.type}
                        value={formData[variable.key] || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          [variable.key]: e.target.value
                        }))}
                        placeholder={variable.placeholder}
                        required={variable.required}
                      />
                      {variable.description && (
                        <p className="text-sm text-muted-foreground">
                          {variable.description}
                        </p>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="version" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Version Information</CardTitle>
                  <CardDescription>
                    Configure the new version details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="version-type">Version Type</Label>
                    <Select value={versionType} onValueChange={(value: VersionType) => setVersionType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="patch">
                          <div>
                            <div className="font-medium">Patch (x.x.X)</div>
                            <div className="text-sm text-muted-foreground">Bug fixes and minor changes</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="minor">
                          <div>
                            <div className="font-medium">Minor (x.X.0)</div>
                            <div className="text-sm text-muted-foreground">New features and improvements</div>
                          </div>
                        </SelectItem>
                        <SelectItem value="major">
                          <div>
                            <div className="font-medium">Major (X.0.0)</div>
                            <div className="text-sm text-muted-foreground">Breaking changes and major updates</div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="changelog">Changelog</Label>
                    <Textarea
                      id="changelog"
                      value={changelog}
                      onChange={(e) => setChangelog(e.target.value)}
                      placeholder="Describe what changed in this version..."
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Publishing Settings</CardTitle>
                  <CardDescription>
                    Configure how this version should be handled
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="auto-publish"
                      checked={autoPublish}
                      onCheckedChange={(checked) => setAutoPublish(checked as boolean)}
                    />
                    <Label htmlFor="auto-publish">Auto-publish this version</Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    If enabled, this version will be automatically published and made live.
                  </p>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="notify-users"
                      checked={notifyUsers}
                      onCheckedChange={(checked) => setNotifyUsers(checked as boolean)}
                      disabled={!autoPublish}
                    />
                    <Label htmlFor="notify-users">Notify existing users</Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Send notifications to users who have previously consented to this document type.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Live Preview
              </CardTitle>
              <CardDescription>
                Preview of your generated document
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap text-xs bg-muted p-4 rounded max-h-96 overflow-y-auto">
                  {previewContent}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

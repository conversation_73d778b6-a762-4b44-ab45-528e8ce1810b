// API Routes for Document Generator Sessions
import { NextRequest, NextResponse } from 'next/server';
import { DocumentGeneratorService } from '@/lib/document-generator-service';
import { CreateSessionRequest } from '@/lib/document-generator-types';

// GET /api/document-generator/sessions - Get all sessions for a tenant
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenant_id');
    const userId = searchParams.get('user_id');

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentGeneratorService(tenantId);
    const response = await documentService.getSessions(userId || undefined);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in GET /api/document-generator/sessions:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/document-generator/sessions - Create a new session
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tenant_id, user_id, ...sessionData } = body as CreateSessionRequest & { 
      tenant_id: string; 
      user_id?: string; 
    };

    if (!tenant_id) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    if (!sessionData.document_type || !sessionData.session_name) {
      return NextResponse.json(
        { success: false, error: 'Document type and session name are required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentGeneratorService(tenant_id);
    const response = await documentService.createSession(sessionData, user_id);

    return NextResponse.json(response, { 
      status: response.success ? 201 : 400 
    });
  } catch (error) {
    console.error('Error in POST /api/document-generator/sessions:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

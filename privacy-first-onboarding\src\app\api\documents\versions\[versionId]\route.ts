// Individual Document Version API Routes
// Get specific document version details

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';

interface RouteParams {
  params: {
    versionId: string;
  };
}

// GET /api/documents/versions/[versionId] - Get specific version
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.getVersion(params.versionId);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: response.error?.includes('not found') ? 404 : 500 }
      );
    }
  } catch (error) {
    console.error('Error in GET /api/documents/versions/[versionId]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Document Templates API Route
// Server-side endpoint to safely fetch document templates

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';

// GET /api/documents/templates - Get document templates safely on server-side
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    try {
      const documentService = new DocumentManagementService(tenantId);
      const result = await documentService.getTemplates();
      
      return NextResponse.json(result);
    } catch (error) {
      console.error('Error fetching templates:', error);
      
      // Return mock data as fallback
      return NextResponse.json({
        success: true,
        data: [
          {
            id: 'mock-template-1',
            tenant_id: tenantId,
            template_type: 'privacy_policy',
            name: 'Standard Privacy Policy',
            description: 'GDPR and CCPA compliant privacy policy template',
            template_content: {
              company_name: '{{company_name}}',
              contact_email: '{{contact_email}}'
            },
            markdown_template: `# Privacy Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Information We Collect
We collect personal identifiers, contact information, and usage data.

## 2. How We Use Your Information
We use your information to provide services and ensure compliance.

## 3. Your Rights
You have the right to access, correct, and delete your data.

## 4. Contact Information
For privacy questions, contact us at: {{contact_email}}`,
            compliance_frameworks: ['GDPR', 'CCPA'],
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ]
      });
    }
  } catch (error) {
    console.error('Error in templates API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

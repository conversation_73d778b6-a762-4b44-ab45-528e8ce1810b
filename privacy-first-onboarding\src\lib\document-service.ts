// Document Management Service
// Core service for managing document templates, versions, and consent tracking

import { supabaseAdmin } from './supabase';
import {
  DocumentTemplate,
  DocumentVersion,
  EndUserConsent,
  DocumentGenerationRequest,
  DocumentGenerationResponse,
  TemplateCreationRequest,
  ConsentTrackingRequest,
  VersionPublishingRequest,
  DocumentAnalytics,
  GDPRDataExport,
  ApiResponse
} from './document-types';

export class DocumentManagementService {
  private tenantId: string;

  constructor(tenantId: string) {
    if (!tenantId) {
      throw new Error('Tenant ID is required for DocumentManagementService');
    }
    this.tenantId = tenantId;
  }

  // Set tenant context for RLS
  private async setTenantContext() {
    try {
      if (!supabaseAdmin) throw new Error('Supabase admin client not configured');

      const { error } = await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.tenant_id',
        new_value: this.tenantId,
        is_local: true
      });

      if (error) {
        console.warn('Failed to set tenant context:', error.message);
        // Don't throw error, just log warning as this might not be critical
      }
    } catch (error) {
      console.warn('setTenantContext failed:', error instanceof Error ? error.message : 'Unknown error');
      // Don't throw error to prevent breaking the entire service
    }
  }

  // Template Management
  async createTemplate(request: TemplateCreationRequest): Promise<ApiResponse<DocumentTemplate>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin
        .from('document_templates')
        .insert([{
          tenant_id: this.tenantId,
          template_type: request.template_type,
          name: request.name,
          description: request.description,
          markdown_template: request.markdown_template,
          template_content: request.template_content,
          compliance_frameworks: request.compliance_frameworks,
          is_active: true
        }])
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error creating template:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getTemplates(): Promise<ApiResponse<DocumentTemplate[]>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin
        .from('document_templates')
        .select('*')
        .eq('tenant_id', this.tenantId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('Error fetching templates:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getTemplate(templateId: string): Promise<ApiResponse<DocumentTemplate>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin
        .from('document_templates')
        .select('*')
        .eq('id', templateId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching template:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async updateTemplate(templateId: string, updates: Partial<DocumentTemplate>): Promise<ApiResponse<DocumentTemplate>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin
        .from('document_templates')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', templateId)
        .eq('tenant_id', this.tenantId)
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error updating template:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Document Generation
  async generateDocument(request: DocumentGenerationRequest): Promise<ApiResponse<DocumentGenerationResponse>> {
    try {
      await this.setTenantContext();

      // Call the database function to create a new version
      const { data, error } = await supabaseAdmin.rpc('create_document_version', {
        p_tenant_id: this.tenantId,
        p_template_id: request.template_id,
        p_changes: request.template_data,
        p_version_type: request.version_type || 'minor',
        p_changelog: request.changelog || null,
        p_created_by: null // TODO: Get from auth context
      });

      if (error) throw error;

      const versionId = data;
      
      // Get the created version details
      const { data: version, error: versionError } = await supabaseAdmin
        .from('document_versions')
        .select('version_number, markdown_content')
        .eq('id', versionId)
        .single();

      if (versionError) throw versionError;

      // Auto-publish if requested
      if (request.auto_publish) {
        await this.publishVersion({
          version_id: versionId,
          notify_existing_users: request.notify_users || false
        });
      }

      return {
        success: true,
        data: {
          success: true,
          version_id: versionId,
          version_number: version.version_number,
          preview_url: `/api/documents/${versionId}/preview`,
          pdf_url: undefined // TODO: Generate PDF
        }
      };
    } catch (error) {
      console.error('Error generating document:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Version Management
  async getVersions(templateId: string): Promise<ApiResponse<DocumentVersion[]>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin
        .from('document_versions')
        .select(`
          *,
          template:document_templates(name, template_type)
        `)
        .eq('template_id', templateId)
        .eq('tenant_id', this.tenantId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('Error fetching versions:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async getVersion(versionId: string): Promise<ApiResponse<DocumentVersion>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin
        .from('document_versions')
        .select(`
          *,
          template:document_templates(name, template_type, compliance_frameworks)
        `)
        .eq('id', versionId)
        .eq('tenant_id', this.tenantId)
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error fetching version:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async publishVersion(request: VersionPublishingRequest): Promise<ApiResponse<any>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin.rpc('publish_document_version', {
        p_tenant_id: this.tenantId,
        p_version_id: request.version_id,
        p_notify_existing_users: request.notify_existing_users || false,
        p_published_by: null // TODO: Get from auth context
      });

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error publishing version:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Consent Tracking
  async trackConsent(request: ConsentTrackingRequest): Promise<ApiResponse<EndUserConsent>> {
    try {
      await this.setTenantContext();

      // Get document version to determine consent type
      const { data: version } = await supabaseAdmin
        .from('document_versions')
        .select('template:document_templates(template_type)')
        .eq('id', request.document_version_id)
        .single();

      const { data, error } = await supabaseAdmin
        .from('end_user_consents')
        .insert([{
          tenant_id: this.tenantId,
          document_version_id: request.document_version_id,
          end_user_identifier: request.end_user_identifier,
          end_user_email: request.end_user_email,
          consent_type: version?.template?.template_type || 'privacy_policy',
          consent_given: request.consent_given,
          ip_address: request.ip_address,
          user_agent: request.user_agent,
          consent_method: request.consent_method,
          consent_timestamp: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;

      // TODO: Trigger notification if consent given
      if (request.consent_given) {
        // Queue notification
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error tracking consent:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // GDPR Compliance
  async exportUserData(userEmail: string): Promise<ApiResponse<GDPRDataExport>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin.rpc('export_user_consent_data', {
        p_tenant_id: this.tenantId,
        p_user_email: userEmail
      });

      if (error) throw error;

      return {
        success: true,
        data: {
          user_email: userEmail,
          export_date: new Date().toISOString(),
          consents: data || [],
          notifications: [] // TODO: Add notification history
        }
      };
    } catch (error) {
      console.error('Error exporting user data:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async deleteUserData(userEmail: string): Promise<ApiResponse<any>> {
    try {
      await this.setTenantContext();

      const { data, error } = await supabaseAdmin.rpc('delete_user_consent_data', {
        p_tenant_id: this.tenantId,
        p_user_email: userEmail,
        p_deleted_by: null // TODO: Get from auth context
      });

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('Error deleting user data:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

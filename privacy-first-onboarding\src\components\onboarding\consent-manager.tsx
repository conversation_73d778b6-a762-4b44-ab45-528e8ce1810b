'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Info, CheckCircle, XCircle } from 'lucide-react';
import { DATA_CATEGORIES, PRIVACY_REGIONS } from '@/lib/utils';
import type { PrivacyRegion, DataCategory } from '@/lib/utils';

interface ConsentItem {
  dataCategory: DataCategory;
  purpose: string;
  description: string;
  required: boolean;
  legalBasis: string;
}

interface ConsentManagerProps {
  selectedRegion: PrivacyRegion;
  consents: Record<string, boolean>;
  onConsentChange: (consentKey: string, granted: boolean) => void;
  onNext: () => void;
  onBack: () => void;
}

export function ConsentManager({ 
  selectedRegion, 
  consents, 
  onConsentChange, 
  onNext, 
  onBack 
}: ConsentManagerProps) {
  const [showDetails, setShowDetails] = useState<Record<string, boolean>>({});

  // Define consent items based on region
  const consentItems: ConsentItem[] = [
    {
      dataCategory: 'PERSONAL_IDENTIFIERS',
      purpose: 'Account Creation and Management',
      description: 'We need your name and email to create and manage your account, provide customer support, and ensure account security.',
      required: true,
      legalBasis: 'Contract Performance'
    },
    {
      dataCategory: 'CONTACT_INFO',
      purpose: 'Service Communications',
      description: 'We use your contact information to send important service updates, security notifications, and account-related communications.',
      required: true,
      legalBasis: 'Contract Performance'
    },
    {
      dataCategory: 'BEHAVIORAL_DATA',
      purpose: 'Service Improvement and Analytics',
      description: 'We analyze how you use our service to improve functionality, fix bugs, and enhance user experience.',
      required: false,
      legalBasis: 'Legitimate Interest'
    },
    {
      dataCategory: 'CONTACT_INFO',
      purpose: 'Marketing Communications',
      description: 'We would like to send you promotional emails, product updates, and personalized offers.',
      required: false,
      legalBasis: 'Consent'
    },
    {
      dataCategory: 'BEHAVIORAL_DATA',
      purpose: 'Personalization',
      description: 'We use your usage patterns to personalize your experience and show relevant content.',
      required: false,
      legalBasis: 'Consent'
    },
    {
      dataCategory: 'LOCATION_DATA',
      purpose: 'Location-based Services',
      description: 'We may use your approximate location to provide region-specific features and comply with local laws.',
      required: false,
      legalBasis: 'Consent'
    }
  ];

  const toggleDetails = (key: string) => {
    setShowDetails(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const getConsentKey = (item: ConsentItem) => {
    return `${item.dataCategory}_${item.purpose.replace(/\s+/g, '_').toLowerCase()}`;
  };

  const requiredConsentsGranted = consentItems
    .filter(item => item.required)
    .every(item => consents[getConsentKey(item)] === true);

  const canProceed = requiredConsentsGranted;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-green-100 rounded-full">
            <Shield className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <CardTitle className="text-2xl">Privacy Preferences</CardTitle>
        <CardDescription className="text-lg">
          Choose how we can use your data. You can change these preferences anytime.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Region Info */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 mb-2">
            <Info className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-900">
              Data Processing in {PRIVACY_REGIONS[selectedRegion].name}
            </span>
          </div>
          <p className="text-sm text-blue-700">
            Your data will be processed according to{' '}
            {PRIVACY_REGIONS[selectedRegion].laws.join(', ')} regulations.
          </p>
        </div>

        {/* Consent Items */}
        <div className="space-y-4">
          {consentItems.map((item) => {
            const consentKey = getConsentKey(item);
            const isGranted = consents[consentKey] || false;
            const isDetailVisible = showDetails[consentKey];

            return (
              <div key={consentKey} className="border rounded-lg p-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{item.purpose}</h4>
                      {item.required && (
                        <Badge variant="destructive" className="text-xs">
                          Required
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        {DATA_CATEGORIES[item.dataCategory]}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-gray-600">{item.description}</p>
                    
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Legal Basis: {item.legalBasis}</span>
                      <Button
                        variant="link"
                        size="sm"
                        className="h-auto p-0 text-xs"
                        onClick={() => toggleDetails(consentKey)}
                      >
                        {isDetailVisible ? 'Hide Details' : 'Show Details'}
                      </Button>
                    </div>

                    {isDetailVisible && (
                      <div className="mt-3 p-3 bg-gray-50 rounded text-xs space-y-2">
                        <div><strong>Data Category:</strong> {DATA_CATEGORIES[item.dataCategory]}</div>
                        <div><strong>Retention Period:</strong> As per {PRIVACY_REGIONS[selectedRegion].name} regulations</div>
                        <div><strong>Your Rights:</strong> Access, correction, deletion, portability (where applicable)</div>
                        <div><strong>Third Parties:</strong> We do not sell your data to third parties</div>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {item.required ? (
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-sm font-medium">Required</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Switch
                          id={consentKey}
                          checked={isGranted}
                          onCheckedChange={(checked) => onConsentChange(consentKey, checked)}
                        />
                        <Label htmlFor={consentKey} className="sr-only">
                          {item.purpose}
                        </Label>
                        {isGranted ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <XCircle className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Required Consents Warning */}
        {!requiredConsentsGranted && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Some data processing is required for basic service functionality. 
              These cannot be disabled but you can delete your account at any time.
            </AlertDescription>
          </Alert>
        )}

        <Separator />

        {/* Your Rights */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2">Your Privacy Rights</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
            <div>• Right to access your data</div>
            <div>• Right to correct inaccurate data</div>
            <div>• Right to delete your data</div>
            <div>• Right to data portability</div>
            <div>• Right to withdraw consent</div>
            <div>• Right to object to processing</div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex gap-4">
          <Button variant="outline" onClick={onBack} className="flex-1">
            Back
          </Button>
          <Button 
            onClick={onNext} 
            disabled={!canProceed}
            className="flex-1"
          >
            Continue to Account Details
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

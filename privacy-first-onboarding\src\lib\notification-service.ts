// Multi-Channel Notification Service
// Handle email, SMS, and webhook notifications for document updates

import { supabaseAdmin } from './supabase';
import { NotificationPreferences, NotificationHistory, DocumentVersion } from './document-types';

export interface EmailNotification {
  to: string;
  subject: string;
  html: string;
  attachments?: {
    filename: string;
    path: string;
  }[];
}

export interface SMSNotification {
  to: string;
  message: string;
}

export interface WebhookNotification {
  url: string;
  payload: Record<string, any>;
  headers?: Record<string, string>;
}

export class NotificationService {
  private tenantId: string;

  constructor(tenantId: string) {
    this.tenantId = tenantId;
  }

  // Set tenant context for RLS
  private async setTenantContext() {
    if (!supabaseAdmin) throw new Error('Supabase admin client not configured');
    await supabaseAdmin.rpc('set_config', {
      setting_name: 'app.tenant_id',
      new_value: this.tenantId,
      is_local: true
    });
  }

  // Get notification preferences for tenant
  async getNotificationPreferences(): Promise<NotificationPreferences[]> {
    await this.setTenantContext();

    const { data, error } = await supabaseAdmin
      .from('notification_preferences')
      .select('*')
      .eq('tenant_id', this.tenantId)
      .eq('is_enabled', true);

    if (error) throw error;
    return data || [];
  }

  // Send consent confirmation notification
  async sendConsentConfirmation(
    userEmail: string, 
    documentVersionId: string, 
    consentGiven: boolean
  ): Promise<void> {
    try {
      const preferences = await this.getNotificationPreferences();
      const documentData = await this.getDocumentVersion(documentVersionId);

      for (const preference of preferences) {
        switch (preference.notification_type) {
          case 'email':
            await this.sendEmailNotification(
              userEmail, 
              documentData, 
              preference.configuration,
              consentGiven ? 'consent_confirmation' : 'consent_declined'
            );
            break;
            
          case 'sms':
            await this.sendSMSNotification(
              userEmail, 
              documentData, 
              preference.configuration,
              consentGiven ? 'consent_confirmation' : 'consent_declined'
            );
            break;
            
          case 'webhook':
            await this.sendWebhookNotification(
              userEmail, 
              documentData, 
              preference.configuration,
              consentGiven ? 'consent_confirmation' : 'consent_declined'
            );
            break;
        }
      }
    } catch (error) {
      console.error('Error sending consent confirmation:', error);
      throw error;
    }
  }

  // Send document update notification
  async sendDocumentUpdateNotification(
    userEmail: string,
    documentVersionId: string,
    customMessage?: string
  ): Promise<void> {
    try {
      const preferences = await this.getNotificationPreferences();
      const documentData = await this.getDocumentVersion(documentVersionId);

      for (const preference of preferences) {
        switch (preference.notification_type) {
          case 'email':
            await this.sendEmailNotification(
              userEmail, 
              documentData, 
              preference.configuration,
              'version_update',
              customMessage
            );
            break;
            
          case 'sms':
            await this.sendSMSNotification(
              userEmail, 
              documentData, 
              preference.configuration,
              'version_update',
              customMessage
            );
            break;
            
          case 'webhook':
            await this.sendWebhookNotification(
              userEmail, 
              documentData, 
              preference.configuration,
              'version_update',
              customMessage
            );
            break;
        }
      }
    } catch (error) {
      console.error('Error sending document update notification:', error);
      throw error;
    }
  }

  // Email notification implementation
  private async sendEmailNotification(
    email: string,
    documentData: DocumentVersion,
    emailConfig: any,
    notificationType: string,
    customMessage?: string
  ): Promise<void> {
    try {
      const subject = this.processTemplate(
        emailConfig.subject_template || 'Document Update - {{document_name}}',
        {
          document_name: documentData.template?.name || 'Document',
          document_type: this.getDocumentTypeName(documentData.template?.template_type || 'document'),
          version: documentData.version_number,
          company_name: 'Privacy First Application'
        }
      );

      const emailTemplate = this.getEmailTemplate(notificationType);
      const html = this.processTemplate(emailTemplate, {
        document_name: documentData.template?.name || 'Document',
        document_type: this.getDocumentTypeName(documentData.template?.template_type || 'document'),
        version: documentData.version_number,
        document_url: `${process.env.NEXT_PUBLIC_BASE_URL}/documents/view/${documentData.id}`,
        custom_message: customMessage || '',
        company_name: 'Privacy First Application',
        current_date: new Date().toLocaleDateString()
      });

      const emailNotification: EmailNotification = {
        to: email,
        subject,
        html,
        attachments: []
      };

      // Add PDF attachment if requested and available
      if (emailConfig.include_pdf && documentData.pdf_url) {
        emailNotification.attachments?.push({
          filename: `${documentData.template?.name}_v${documentData.version_number}.pdf`,
          path: documentData.pdf_url
        });
      }

      // In a real implementation, you would use a service like SendGrid, Mailgun, etc.
      await this.sendEmail(emailNotification);

      // Log the notification
      await this.logNotification(email, 'email', documentData.id, 'sent', emailNotification);

    } catch (error) {
      console.error('Error sending email notification:', error);
      await this.logNotification(email, 'email', documentData.id, 'failed', null, error.message);
      throw error;
    }
  }

  // SMS notification implementation
  private async sendSMSNotification(
    phoneNumber: string,
    documentData: DocumentVersion,
    smsConfig: any,
    notificationType: string,
    customMessage?: string
  ): Promise<void> {
    try {
      const message = this.processTemplate(
        smsConfig.message_template || 'Your {{document_type}} has been updated. View: {{document_url}}',
        {
          document_name: documentData.template?.name || 'Document',
          document_type: this.getDocumentTypeName(documentData.template?.template_type || 'document'),
          version: documentData.version_number,
          document_url: `${process.env.NEXT_PUBLIC_BASE_URL}/documents/view/${documentData.id}`,
          custom_message: customMessage || ''
        }
      );

      const smsNotification: SMSNotification = {
        to: phoneNumber,
        message
      };

      // In a real implementation, you would use a service like Twilio, AWS SNS, etc.
      await this.sendSMS(smsNotification);

      // Log the notification
      await this.logNotification(phoneNumber, 'sms', documentData.id, 'sent', smsNotification);

    } catch (error) {
      console.error('Error sending SMS notification:', error);
      await this.logNotification(phoneNumber, 'sms', documentData.id, 'failed', null, error.message);
      throw error;
    }
  }

  // Webhook notification implementation
  private async sendWebhookNotification(
    userIdentifier: string,
    documentData: DocumentVersion,
    webhookConfig: any,
    notificationType: string,
    customMessage?: string
  ): Promise<void> {
    try {
      const payload = {
        event: notificationType,
        user_identifier: userIdentifier,
        document: {
          id: documentData.id,
          name: documentData.template?.name,
          type: documentData.template?.template_type,
          version: documentData.version_number,
          url: `${process.env.NEXT_PUBLIC_BASE_URL}/documents/view/${documentData.id}`
        },
        custom_message: customMessage,
        timestamp: new Date().toISOString(),
        tenant_id: this.tenantId
      };

      const webhookNotification: WebhookNotification = {
        url: webhookConfig.webhook_url,
        payload,
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Secret': webhookConfig.secret_key || '',
          'User-Agent': 'PrivacyFirst-Webhook/1.0'
        }
      };

      // In a real implementation, you would make HTTP requests with retry logic
      await this.sendWebhook(webhookNotification);

      // Log the notification
      await this.logNotification(userIdentifier, 'webhook', documentData.id, 'sent', webhookNotification);

    } catch (error) {
      console.error('Error sending webhook notification:', error);
      await this.logNotification(userIdentifier, 'webhook', documentData.id, 'failed', null, error.message);
      throw error;
    }
  }

  // Helper methods
  private async getDocumentVersion(versionId: string): Promise<DocumentVersion> {
    await this.setTenantContext();

    const { data, error } = await supabaseAdmin
      .from('document_versions')
      .select(`
        *,
        template:document_templates(name, template_type)
      `)
      .eq('id', versionId)
      .single();

    if (error) throw error;
    return data;
  }

  private processTemplate(template: string, variables: Record<string, string>): string {
    let processed = template;
    Object.entries(variables).forEach(([key, value]) => {
      processed = processed.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    return processed;
  }

  private getDocumentTypeName(type: string): string {
    switch (type) {
      case 'privacy_policy':
        return 'Privacy Policy';
      case 'terms_of_service':
        return 'Terms of Service';
      case 'cookie_policy':
        return 'Cookie Policy';
      default:
        return 'Document';
    }
  }

  private getEmailTemplate(notificationType: string): string {
    switch (notificationType) {
      case 'consent_confirmation':
        return `
          <h2>Consent Confirmation</h2>
          <p>Thank you for accepting our {{document_type}}.</p>
          <p>You can view the document at: <a href="{{document_url}}">{{document_url}}</a></p>
          {{#if custom_message}}<p>{{custom_message}}</p>{{/if}}
          <p>Best regards,<br>{{company_name}}</p>
        `;
      case 'version_update':
        return `
          <h2>{{document_type}} Updated</h2>
          <p>We have updated our {{document_type}} to version {{version}}.</p>
          <p>Please review the changes at: <a href="{{document_url}}">{{document_url}}</a></p>
          {{#if custom_message}}<p>{{custom_message}}</p>{{/if}}
          <p>Best regards,<br>{{company_name}}</p>
        `;
      default:
        return `
          <h2>Document Notification</h2>
          <p>This is a notification about {{document_name}}.</p>
          <p>View document: <a href="{{document_url}}">{{document_url}}</a></p>
        `;
    }
  }

  // Mock implementations - replace with real services
  private async sendEmail(notification: EmailNotification): Promise<void> {
    console.log('📧 Sending email:', notification);
    // TODO: Implement with SendGrid, Mailgun, etc.
  }

  private async sendSMS(notification: SMSNotification): Promise<void> {
    console.log('📱 Sending SMS:', notification);
    // TODO: Implement with Twilio, AWS SNS, etc.
  }

  private async sendWebhook(notification: WebhookNotification): Promise<void> {
    console.log('🔗 Sending webhook:', notification);
    // TODO: Implement HTTP request with retry logic
  }

  // Log notification to database
  private async logNotification(
    userIdentifier: string,
    notificationType: string,
    documentVersionId: string,
    status: string,
    notificationData: any,
    errorMessage?: string
  ): Promise<void> {
    await this.setTenantContext();

    await supabaseAdmin
      .from('notification_history')
      .insert([{
        tenant_id: this.tenantId,
        end_user_identifier: userIdentifier,
        notification_type: notificationType,
        document_version_id: documentVersionId,
        status,
        notification_data: notificationData,
        error_message: errorMessage,
        sent_at: new Date().toISOString()
      }]);
  }
}

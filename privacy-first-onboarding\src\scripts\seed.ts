#!/usr/bin/env tsx

import { seedDatabase, prisma } from '../lib/database';

async function main() {
  console.log('🌱 Starting database seeding...');
  
  try {
    await seedDatabase();
    console.log('✅ Database seeding completed successfully');
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();

// Document Management System Types
// TypeScript interfaces for document templates, versions, and consent tracking

export type DocumentType = 'privacy_policy' | 'terms_of_service' | 'cookie_policy' | 'data_processing_agreement';
export type VersionType = 'major' | 'minor' | 'patch';
export type DocumentStatus = 'draft' | 'published' | 'archived';
export type ConsentMethod = 'website_signup' | 'email_confirmation' | 'api_integration' | 'embedded_widget';
export type NotificationType = 'email' | 'sms' | 'webhook';
export type NotificationStatus = 'sent' | 'delivered' | 'failed' | 'pending';

// Document Template Interface
export interface DocumentTemplate {
  id: string;
  tenant_id: string;
  template_type: DocumentType;
  name: string;
  description?: string;
  template_content: Record<string, any>; // JSONB data
  markdown_template: string;
  compliance_frameworks: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Document Version Interface
export interface DocumentVersion {
  id: string;
  tenant_id: string;
  template_id: string;
  version_number: string;
  version_type: VersionType;
  markdown_content: string;
  generated_data: Record<string, any>;
  pdf_url?: string;
  status: DocumentStatus;
  changelog?: string;
  published_at?: string;
  created_by?: string;
  created_at: string;
  
  // Joined data
  template?: DocumentTemplate;
}

// End User Consent Interface
export interface EndUserConsent {
  id: string;
  tenant_id: string;
  document_version_id: string;
  end_user_identifier: string;
  end_user_email?: string;
  consent_type: DocumentType;
  consent_given: boolean;
  consent_timestamp: string;
  ip_address?: string;
  user_agent?: string;
  consent_method: ConsentMethod;
  notification_sent: boolean;
  notification_sent_at?: string;
  created_at: string;
  
  // Joined data
  document_version?: DocumentVersion;
}

// Notification Preferences Interface
export interface NotificationPreferences {
  id: string;
  tenant_id: string;
  notification_type: NotificationType;
  is_enabled: boolean;
  configuration: {
    // Email configuration
    smtp_host?: string;
    smtp_port?: number;
    from_email?: string;
    from_name?: string;
    subject_template?: string;
    email_template?: string;
    include_pdf?: boolean;
    
    // SMS configuration
    provider?: string;
    api_key?: string;
    from_number?: string;
    message_template?: string;
    
    // Webhook configuration
    webhook_url?: string;
    secret_key?: string;
    retry_attempts?: number;
    timeout_seconds?: number;
  };
  created_at: string;
  updated_at: string;
}

// Notification History Interface
export interface NotificationHistory {
  id: string;
  tenant_id: string;
  end_user_identifier: string;
  notification_type: NotificationType;
  document_version_id?: string;
  status: NotificationStatus;
  notification_data?: Record<string, any>;
  sent_at: string;
  delivered_at?: string;
  error_message?: string;
  retry_count: number;
}

// Document Generation Request
export interface DocumentGenerationRequest {
  template_id: string;
  template_data: Record<string, any>;
  version_type?: VersionType;
  changelog?: string;
  auto_publish?: boolean;
  notify_users?: boolean;
}

// Document Generation Response
export interface DocumentGenerationResponse {
  success: boolean;
  version_id: string;
  version_number: string;
  preview_url: string;
  pdf_url?: string;
  errors?: string[];
}

// Template Creation Request
export interface TemplateCreationRequest {
  template_type: DocumentType;
  name: string;
  description?: string;
  markdown_template: string;
  template_content: Record<string, any>;
  compliance_frameworks: string[];
}

// Consent Tracking Request
export interface ConsentTrackingRequest {
  document_version_id: string;
  end_user_identifier: string;
  end_user_email?: string;
  consent_given: boolean;
  consent_method: ConsentMethod;
  ip_address?: string;
  user_agent?: string;
}

// Version Publishing Request
export interface VersionPublishingRequest {
  version_id: string;
  notify_existing_users?: boolean;
  notification_message?: string;
}

// Document Analytics Interface
export interface DocumentAnalytics {
  template_id: string;
  template_name: string;
  template_type: DocumentType;
  total_versions: number;
  current_version: string;
  total_consents: number;
  consent_rate: number;
  recent_activity: {
    date: string;
    consents: number;
    notifications: number;
  }[];
}

// Embed Widget Configuration
export interface EmbedWidgetConfig {
  document_version_id: string;
  widget_type: 'modal' | 'inline' | 'banner';
  styling: {
    width?: string;
    height?: string;
    background_color?: string;
    text_color?: string;
    button_color?: string;
    border_radius?: string;
  };
  behavior: {
    auto_show?: boolean;
    show_delay?: number;
    require_scroll?: boolean;
    remember_choice?: boolean;
  };
  content: {
    title?: string;
    description?: string;
    accept_text?: string;
    decline_text?: string;
  };
}

// GDPR Data Export Response
export interface GDPRDataExport {
  user_email: string;
  export_date: string;
  consents: {
    consent_id: string;
    document_type: DocumentType;
    document_name: string;
    consent_given: boolean;
    timestamp: string;
    ip_address?: string;
    user_agent?: string;
    consent_method: ConsentMethod;
    document_version: string;
  }[];
  notifications: {
    notification_id: string;
    notification_type: NotificationType;
    status: NotificationStatus;
    sent_at: string;
    delivered_at?: string;
  }[];
}

// Utility type for API responses
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

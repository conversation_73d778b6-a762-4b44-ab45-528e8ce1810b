// Privacy-First Multitenant Database Schema
// Designed for global privacy law compliance (GDPR, CCPA, DPDP, etc.)

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Tenant Management
model Tenant {
  id            String   @id @default(cuid())
  name          String
  slug          String   @unique
  regions       String[] // Supported regions for this tenant
  defaultRegion String // Default data region
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Privacy Configuration
  privacyConfig TenantPrivacyConfig?

  // Related data
  users                    User[]
  dataProcessingActivities DataProcessingActivity[]
  userRoles                UserRole[]

  @@map("tenants")
}

model TenantPrivacyConfig {
  id       String @id @default(cuid())
  tenantId String @unique
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Compliance Settings
  requireExplicitConsent   Boolean @default(true)
  enableRightToBeForgotten Boolean @default(true)
  enableDataPortability    Boolean @default(true)
  enableOptOut             Boolean @default(true)
  cookieConsentRequired    Boolean @default(true)
  minorProtection          Boolean @default(true)

  // Data Retention Policies (in days)
  personalIdentifiersRetention Int @default(2555) // 7 years
  contactInfoRetention         Int @default(1095) // 3 years
  financialInfoRetention       Int @default(2555) // 7 years
  biometricDataRetention       Int @default(365) // 1 year
  locationDataRetention        Int @default(365) // 1 year
  behavioralDataRetention      Int @default(730) // 2 years
  sensitivePersonalRetention   Int @default(365) // 1 year
  healthDataRetention          Int @default(2555) // 7 years
  employmentDataRetention      Int @default(2555) // 7 years
  educationDataRetention       Int @default(2555) // 7 years

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("tenant_privacy_configs")
}

// User Management with Privacy Controls
model User {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Basic Information (encrypted at rest)
  email          String
  firstName      String
  lastName       String
  hashedPassword String? // Optional for demo purposes

  // Privacy Preferences
  preferredRegion         String // Data storage region
  dataResidencyPreference String // SAME_REGION, GLOBAL, CUSTOM

  // Communication Preferences
  marketingConsent       Boolean @default(false)
  analyticsConsent       Boolean @default(false)
  personalizationConsent Boolean @default(false)
  thirdPartySharing      Boolean @default(false)

  // Account Status
  isActive      Boolean @default(true)
  emailVerified Boolean @default(false)

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastLoginAt DateTime?

  // Privacy-related data
  consents        Consent[]
  privacyRequests PrivacyRequest[]
  auditLogs       AuditLog[]

  // Authentication-related data
  accounts  Account[]
  sessions  Session[]
  userRoles UserRole[]
  apiKeys   ApiKey[]

  @@unique([tenantId, email])
  @@map("users")
}

// Consent Management
model Consent {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Consent Details
  dataCategory String // PERSONAL_IDENTIFIERS, CONTACT_INFO, etc.
  purpose      String // Specific purpose for data processing
  legalBasis   String // CONSENT, CONTRACT, LEGITIMATE_INTEREST, etc.
  granted      Boolean
  version      String  @default("1.0")

  // Metadata
  grantedAt   DateTime?
  withdrawnAt DateTime?
  expiresAt   DateTime?
  region      String // Region where consent was given

  // Audit Trail
  ipAddress     String?
  userAgent     String?
  consentMethod String // WEB_FORM, API, etc.

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("consents")
}

// Data Processing Activities (GDPR Article 30)
model DataProcessingActivity {
  id       String @id @default(cuid())
  tenantId String
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Activity Details
  name            String
  description     String
  dataCategories  String[] // Array of data categories
  purposes        String[] // Array of processing purposes
  legalBasis      String
  retentionPeriod Int // Days

  // Geographic Scope
  regions      String[] // Regions where this activity applies
  thirdParties String[] // Third parties involved

  // Compliance
  isActive     Boolean   @default(true)
  lastReviewed DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("data_processing_activities")
}

// Privacy Rights Requests
model PrivacyRequest {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Request Details
  requestType String // ACCESS, CORRECTION, DELETION, PORTABILITY, OPT_OUT, RESTRICT_PROCESSING
  status      String // PENDING, IN_PROGRESS, COMPLETED, REJECTED
  description String?

  // Processing
  requestedAt     DateTime  @default(now())
  acknowledgedAt  DateTime?
  completedAt     DateTime?
  rejectedAt      DateTime?
  rejectionReason String?

  // Response Data
  responseData Json? // For data export requests

  // Compliance Tracking
  region        String // Region where request was made
  legalDeadline DateTime // Legal deadline for response

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("privacy_requests")
}

// Audit Logging for Compliance
model AuditLog {
  id     String  @id @default(cuid())
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Event Details
  eventType  String // DATA_ACCESS, CONSENT_CHANGE, PRIVACY_REQUEST, etc.
  action     String // CREATE, READ, UPDATE, DELETE
  resource   String // Table/resource affected
  resourceId String? // ID of affected resource

  // Context
  ipAddress String?
  userAgent String?
  region    String?

  // Data
  oldValues Json? // Previous values (for updates)
  newValues Json? // New values
  metadata  Json? // Additional context

  timestamp DateTime @default(now())

  @@map("audit_logs")
}

// Data Breach Incident Management
model DataBreach {
  id String @id @default(cuid())

  // Incident Details
  title       String
  description String
  severity    String // LOW, MEDIUM, HIGH, CRITICAL
  status      String // DETECTED, INVESTIGATING, CONTAINED, RESOLVED

  // Affected Data
  affectedDataTypes String[] // Types of data involved
  affectedUserCount Int? // Number of users affected
  affectedRegions   String[] // Regions affected

  // Timeline
  detectedAt  DateTime
  containedAt DateTime?
  resolvedAt  DateTime?

  // Compliance
  requiresNotification Boolean   @default(false)
  notificationSent     Boolean   @default(false)
  notificationSentAt   DateTime?

  // Regulatory Reporting
  reportedToAuthorities Boolean   @default(false)
  reportedAt            DateTime?
  authorityReference    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("data_breaches")
}

// Regional Compliance Tracking
model ComplianceStatus {
  id String @id @default(cuid())

  // Scope
  tenantId String? // Null for global compliance
  region   String
  law      String // GDPR, CCPA, DPDP, etc.

  // Status
  isCompliant    Boolean  @default(false)
  lastAssessment DateTime
  nextAssessment DateTime

  // Details
  complianceScore Int? // 0-100
  findings        Json? // Assessment findings
  remediationPlan Json? // Action items

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([tenantId, region, law])
  @@map("compliance_status")
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Role-Based Access Control
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  permissions String[] // Array of permission strings
  isSystem    Boolean  @default(false) // System roles cannot be deleted

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userRoles UserRole[]

  @@map("roles")
}

model UserRole {
  id       String @id @default(cuid())
  userId   String
  roleId   String
  tenantId String // Scope role to tenant

  grantedAt DateTime  @default(now())
  grantedBy String? // User ID who granted this role
  expiresAt DateTime? // Optional role expiration

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  role   Role   @relation(fields: [roleId], references: [id], onDelete: Cascade)
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId, tenantId])
  @@map("user_roles")
}

// API Keys for programmatic access
model ApiKey {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  name        String // Human-readable name
  keyHash     String   @unique // Hashed API key
  permissions String[] // Scoped permissions

  isActive   Boolean   @default(true)
  lastUsedAt DateTime?
  expiresAt  DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("api_keys")
}

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Code, 
  Copy, 
  Eye, 
  Settings, 
  Palette,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react';
import { DocumentVersion, EmbedWidgetConfig } from '@/lib/document-types';

interface EmbedGeneratorProps {
  documentVersion: DocumentVersion;
  tenantId: string;
  onClose: () => void;
}

export function EmbedGenerator({ documentVersion, tenantId, onClose }: EmbedGeneratorProps) {
  const [config, setConfig] = useState<EmbedWidgetConfig>({
    document_version_id: documentVersion.id,
    widget_type: 'inline',
    styling: {
      width: '100%',
      height: 'auto',
      background_color: '#ffffff',
      text_color: '#374151',
      button_color: '#3b82f6',
      border_radius: '8px'
    },
    behavior: {
      auto_show: true,
      show_delay: 0,
      require_scroll: false,
      remember_choice: true
    },
    content: {
      title: 'Privacy Policy Consent',
      description: 'Please review and accept our privacy policy to continue.',
      accept_text: 'Accept',
      decline_text: 'Decline'
    }
  });

  const [activeTab, setActiveTab] = useState('config');
  const [previewDevice, setPreviewDevice] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  const generateEmbedCode = () => {
    const baseUrl = window.location.origin;
    const embedUrl = new URL(`${baseUrl}/embed/consent/${documentVersion.id}`);
    
    // Add configuration parameters
    embedUrl.searchParams.set('tenantId', tenantId);
    embedUrl.searchParams.set('type', config.widget_type);
    embedUrl.searchParams.set('width', config.styling.width || '100%');
    embedUrl.searchParams.set('height', config.styling.height || 'auto');
    embedUrl.searchParams.set('bg', config.styling.background_color || '#ffffff');
    embedUrl.searchParams.set('color', config.styling.text_color || '#374151');
    embedUrl.searchParams.set('buttonColor', config.styling.button_color || '#3b82f6');
    embedUrl.searchParams.set('borderRadius', config.styling.border_radius || '8px');
    embedUrl.searchParams.set('autoShow', config.behavior.auto_show ? 'true' : 'false');
    embedUrl.searchParams.set('delay', config.behavior.show_delay?.toString() || '0');
    embedUrl.searchParams.set('requireScroll', config.behavior.require_scroll ? 'true' : 'false');
    embedUrl.searchParams.set('remember', config.behavior.remember_choice ? 'true' : 'false');
    
    if (config.content.title) embedUrl.searchParams.set('title', config.content.title);
    if (config.content.description) embedUrl.searchParams.set('description', config.content.description);
    if (config.content.accept_text) embedUrl.searchParams.set('acceptText', config.content.accept_text);
    if (config.content.decline_text) embedUrl.searchParams.set('declineText', config.content.decline_text);

    return `<!-- Privacy Consent Widget -->
<div id="privacy-consent-${documentVersion.id}"></div>
<script>
(function() {
    const widget = document.createElement('iframe');
    widget.src = '${embedUrl.toString()}';
    widget.style.width = '${config.styling.width}';
    widget.style.height = '${config.styling.height === 'auto' ? '600px' : config.styling.height}';
    widget.style.border = 'none';
    widget.style.borderRadius = '${config.styling.border_radius}';
    
    // Consent tracking
    widget.onload = function() {
        widget.contentWindow.postMessage({
            type: 'init',
            tenantId: '${tenantId}',
            documentId: '${documentVersion.id}'
        }, '*');
    };
    
    // Listen for consent events
    window.addEventListener('message', function(event) {
        if (event.data.type === 'consent-changed') {
            console.log('Consent changed:', event.data.consent);
            // You can add custom logic here to handle consent changes
        }
    });
    
    document.getElementById('privacy-consent-${documentVersion.id}')
        .appendChild(widget);
})();
</script>`;
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generateEmbedCode());
  };

  const getPreviewWidth = () => {
    switch (previewDevice) {
      case 'mobile':
        return '375px';
      case 'tablet':
        return '768px';
      default:
        return '100%';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Embed Code Generator</h1>
          <p className="text-muted-foreground">
            Generate embeddable consent widget for {documentVersion.template?.name} v{documentVersion.version_number}
          </p>
        </div>
        <Button variant="outline" onClick={onClose}>
          Close
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration */}
        <div className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="config">Configuration</TabsTrigger>
              <TabsTrigger value="styling">Styling</TabsTrigger>
              <TabsTrigger value="code">Code</TabsTrigger>
            </TabsList>

            <TabsContent value="config" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Widget Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="widget-type">Widget Type</Label>
                    <Select
                      value={config.widget_type}
                      onValueChange={(value: any) => 
                        setConfig(prev => ({ ...prev, widget_type: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inline">Inline</SelectItem>
                        <SelectItem value="modal">Modal</SelectItem>
                        <SelectItem value="banner">Banner</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={config.content.title || ''}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        content: { ...prev.content, title: e.target.value }
                      }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={config.content.description || ''}
                      onChange={(e) => setConfig(prev => ({
                        ...prev,
                        content: { ...prev.content, description: e.target.value }
                      }))}
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="accept-text">Accept Button Text</Label>
                      <Input
                        id="accept-text"
                        value={config.content.accept_text || ''}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          content: { ...prev.content, accept_text: e.target.value }
                        }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="decline-text">Decline Button Text</Label>
                      <Input
                        id="decline-text"
                        value={config.content.decline_text || ''}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          content: { ...prev.content, decline_text: e.target.value }
                        }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="auto-show"
                        checked={config.behavior.auto_show}
                        onCheckedChange={(checked) => setConfig(prev => ({
                          ...prev,
                          behavior: { ...prev.behavior, auto_show: checked as boolean }
                        }))}
                      />
                      <Label htmlFor="auto-show">Auto-show widget</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="remember-choice"
                        checked={config.behavior.remember_choice}
                        onCheckedChange={(checked) => setConfig(prev => ({
                          ...prev,
                          behavior: { ...prev.behavior, remember_choice: checked as boolean }
                        }))}
                      />
                      <Label htmlFor="remember-choice">Remember user choice</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="styling" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Visual Styling
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="width">Width</Label>
                      <Input
                        id="width"
                        value={config.styling.width || ''}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          styling: { ...prev.styling, width: e.target.value }
                        }))}
                        placeholder="100%"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="height">Height</Label>
                      <Input
                        id="height"
                        value={config.styling.height || ''}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          styling: { ...prev.styling, height: e.target.value }
                        }))}
                        placeholder="auto"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="bg-color">Background Color</Label>
                      <Input
                        id="bg-color"
                        type="color"
                        value={config.styling.background_color || '#ffffff'}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          styling: { ...prev.styling, background_color: e.target.value }
                        }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="text-color">Text Color</Label>
                      <Input
                        id="text-color"
                        type="color"
                        value={config.styling.text_color || '#374151'}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          styling: { ...prev.styling, text_color: e.target.value }
                        }))}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="button-color">Button Color</Label>
                      <Input
                        id="button-color"
                        type="color"
                        value={config.styling.button_color || '#3b82f6'}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          styling: { ...prev.styling, button_color: e.target.value }
                        }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="border-radius">Border Radius</Label>
                      <Input
                        id="border-radius"
                        value={config.styling.border_radius || ''}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          styling: { ...prev.styling, border_radius: e.target.value }
                        }))}
                        placeholder="8px"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="code" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Embed Code
                  </CardTitle>
                  <CardDescription>
                    Copy this code and paste it into your website
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Textarea
                      value={generateEmbedCode()}
                      readOnly
                      rows={15}
                      className="font-mono text-sm"
                    />
                    <Button onClick={copyToClipboard} className="w-full">
                      <Copy className="h-4 w-4 mr-2" />
                      Copy to Clipboard
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Preview
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={previewDevice === 'desktop' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('desktop')}
                  >
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === 'tablet' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('tablet')}
                  >
                    <Tablet className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={previewDevice === 'mobile' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewDevice('mobile')}
                  >
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-gray-50">
                <div 
                  className="mx-auto transition-all duration-300"
                  style={{ width: getPreviewWidth() }}
                >
                  <iframe
                    src={`/embed/consent/${documentVersion.id}?${new URLSearchParams({
                      tenantId,
                      type: config.widget_type,
                      width: config.styling.width || '100%',
                      height: config.styling.height || 'auto',
                      bg: config.styling.background_color || '#ffffff',
                      color: config.styling.text_color || '#374151',
                      buttonColor: config.styling.button_color || '#3b82f6',
                      borderRadius: config.styling.border_radius || '8px',
                      title: config.content.title || '',
                      description: config.content.description || '',
                      acceptText: config.content.accept_text || 'Accept',
                      declineText: config.content.decline_text || 'Decline'
                    }).toString()}`}
                    width="100%"
                    height="400"
                    style={{ border: 'none', borderRadius: config.styling.border_radius }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Publish, 
  Users, 
  Mail, 
  MessageSquare, 
  Webhook, 
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { DocumentVersion, VersionPublishingRequest } from '@/lib/document-types';
import { DocumentManagementService } from '@/lib/document-service';

interface VersionPublisherProps {
  version: DocumentVersion;
  tenantId: string;
  onPublish: () => void;
  onCancel: () => void;
}

export function VersionPublisher({ version, tenantId, onPublish, onCancel }: VersionPublisherProps) {
  const [notifyUsers, setNotifyUsers] = useState(true);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [publishing, setPublishing] = useState(false);
  const [estimatedUsers, setEstimatedUsers] = useState(0);

  const documentService = new DocumentManagementService(tenantId);

  React.useEffect(() => {
    // TODO: Fetch estimated number of users who will be notified
    setEstimatedUsers(42); // Mock data
  }, []);

  const handlePublish = async () => {
    setPublishing(true);
    try {
      const request: VersionPublishingRequest = {
        version_id: version.id,
        notify_existing_users: notifyUsers,
        notification_message: notificationMessage.trim() || undefined
      };

      const response = await documentService.publishVersion(request);
      
      if (response.success) {
        onPublish();
      } else {
        console.error('Error publishing version:', response.error);
      }
    } catch (error) {
      console.error('Error publishing version:', error);
    } finally {
      setPublishing(false);
    }
  };

  const getVersionTypeColor = (type: string) => {
    switch (type) {
      case 'major':
        return 'bg-red-100 text-red-800';
      case 'minor':
        return 'bg-blue-100 text-blue-800';
      case 'patch':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactLevel = (versionType: string) => {
    switch (versionType) {
      case 'major':
        return {
          level: 'High Impact',
          description: 'Major changes that may affect user rights or data processing',
          color: 'text-red-600',
          icon: <AlertTriangle className="h-4 w-4" />
        };
      case 'minor':
        return {
          level: 'Medium Impact',
          description: 'New features or improvements to existing policies',
          color: 'text-yellow-600',
          icon: <Clock className="h-4 w-4" />
        };
      case 'patch':
        return {
          level: 'Low Impact',
          description: 'Minor fixes and clarifications',
          color: 'text-green-600',
          icon: <CheckCircle className="h-4 w-4" />
        };
      default:
        return {
          level: 'Unknown Impact',
          description: 'Impact level not determined',
          color: 'text-gray-600',
          icon: <AlertTriangle className="h-4 w-4" />
        };
    }
  };

  const impact = getImpactLevel(version.version_type);

  return (
    <Dialog open={true} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Publish className="h-5 w-5" />
            Publish Version {version.version_number}
          </DialogTitle>
          <DialogDescription>
            Review and configure the publishing settings for this document version
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Version Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Version Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Badge className={getVersionTypeColor(version.version_type)}>
                  {version.version_type} v{version.version_number}
                </Badge>
                <div className={`flex items-center gap-2 ${impact.color}`}>
                  {impact.icon}
                  <span className="font-medium">{impact.level}</span>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Changelog</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {version.changelog || 'No changelog provided'}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium">Impact Description</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  {impact.description}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Publishing Impact */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Publishing Impact:</strong> This action will make version {version.version_number} the 
              live version of this document. Previous published versions will be archived.
            </AlertDescription>
          </Alert>

          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Notifications
              </CardTitle>
              <CardDescription>
                Configure how existing users will be notified about this update
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="notify-users"
                  checked={notifyUsers}
                  onCheckedChange={(checked) => setNotifyUsers(checked as boolean)}
                />
                <Label htmlFor="notify-users" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Notify existing users about this update
                </Label>
              </div>

              {notifyUsers && (
                <div className="space-y-4 pl-6 border-l-2 border-muted">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>Estimated recipients: {estimatedUsers} users</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notification-message">
                      Custom notification message (optional)
                    </Label>
                    <Textarea
                      id="notification-message"
                      value={notificationMessage}
                      onChange={(e) => setNotificationMessage(e.target.value)}
                      placeholder="Add a custom message to explain the changes to your users..."
                      rows={3}
                    />
                    <p className="text-xs text-muted-foreground">
                      This message will be included in the notification email along with the document changes.
                    </p>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Mail className="h-4 w-4" />
                      <span>Email</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MessageSquare className="h-4 w-4" />
                      <span>SMS (if enabled)</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Webhook className="h-4 w-4" />
                      <span>Webhooks</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Compliance Notice */}
          {version.version_type === 'major' && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Compliance Notice:</strong> Major version changes may require explicit user consent 
                under GDPR and other privacy regulations. Ensure you have the legal basis for these changes.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onCancel} disabled={publishing}>
              Cancel
            </Button>
            <Button 
              onClick={handlePublish} 
              disabled={publishing}
              className="flex items-center gap-2"
            >
              <Publish className="h-4 w-4" />
              {publishing ? 'Publishing...' : `Publish Version ${version.version_number}`}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

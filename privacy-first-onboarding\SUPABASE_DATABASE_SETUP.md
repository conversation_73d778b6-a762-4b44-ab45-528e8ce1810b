# 🚀 Supabase Database Setup Guide

This guide will help you set up your Supabase database with all the necessary tables and data for the Privacy-First Application.

## 📋 Prerequisites

1. **Supabase Project**: You should have a Supabase project created
2. **Database Password**: You'll need your Supabase database password

## 🔧 Step 1: Get Your Database Password

1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to **Settings** → **Database**
3. Find your database password or reset it if needed
4. Copy the password for the next steps

## 🗄️ Step 2: Run Database Migrations

### Option A: Using Supabase SQL Editor (Recommended)

1. **Open Supabase SQL Editor**:
   - Go to your Supabase project dashboard
   - Click on **SQL Editor** in the left sidebar

2. **Run the Schema Migration**:
   - Copy the entire content from `supabase/migrations/001_initial_schema.sql`
   - Paste it into the SQL Editor
   - Click **Run** to execute the migration

3. **Run the Seed Data**:
   - Copy the entire content from `supabase/corrected_seed.sql`
   - Paste it into the SQL Editor
   - Click **Run** to execute the seed data

### Option B: Using Prisma (Alternative)

1. **Update Environment Variables**:
   ```bash
   # In your .env.local file, replace [YOUR-PASSWORD] with your actual password:
   DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
   DIRECT_URL="postgresql://postgres:<EMAIL>:5432/postgres"
   ```

2. **Push Schema to Database**:
   ```bash
   npx prisma db push
   ```

3. **Run Seed Data**:
   - Still need to run the corrected_seed.sql in Supabase SQL Editor

## 🔐 Step 3: Set Up Row Level Security (RLS)

1. **Run RLS Policies**:
   - Copy the content from `supabase/rls_policies.sql`
   - Paste it into the Supabase SQL Editor
   - Click **Run** to execute the RLS policies

## ✅ Step 4: Verify Database Setup

After running the migrations, you should have these tables:
- `tenants`
- `tenant_privacy_configs`
- `roles`
- `users`
- `user_roles`
- `consents`
- `privacy_requests`
- `data_processing_activities`
- `compliance_status`
- `audit_logs`
- `data_breaches`

## 🧪 Step 5: Test Database Connection

1. **Update your .env.local** with the correct password
2. **Restart your development server**:
   ```bash
   npm run dev
   ```
3. **Check the application** - it should now connect to Supabase

## 🚨 Troubleshooting

### Common Issues:

1. **"Environment variable not found: DIRECT_URL"**
   - Make sure you added `DIRECT_URL` to your `.env.local` file

2. **"Connection refused"**
   - Verify your database password is correct
   - Check that your Supabase project is active

3. **"Table doesn't exist"**
   - Make sure you ran the schema migration first
   - Check the Supabase dashboard to see if tables were created

### Getting Help:

- Check Supabase logs in the dashboard
- Verify your connection string format
- Ensure your IP is allowed (Supabase allows all IPs by default)

## 📝 Next Steps

Once the database is set up:
1. Update the service selection logic to use Supabase
2. Test real data persistence
3. Verify all privacy features work with real data

---

**Need help?** Check the Supabase documentation or ask for assistance!

{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/database.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\n// Global Prisma instance for development\ndeclare global {\n  var prisma: PrismaClient | undefined;\n}\n\n// Create Prisma client with connection pooling and logging\nexport const prisma = globalThis.prisma || new PrismaClient({\n  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],\n  datasources: {\n    db: {\n      url: process.env.DATABASE_URL,\n    },\n  },\n});\n\nif (process.env.NODE_ENV !== 'production') {\n  globalThis.prisma = prisma;\n}\n\n// Database connection health check\nexport async function checkDatabaseConnection(): Promise<boolean> {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n\n// Graceful shutdown\nexport async function disconnectDatabase(): Promise<void> {\n  await prisma.$disconnect();\n}\n\n// Regional database routing (for multitenant architecture)\nexport class RegionalDatabaseManager {\n  private static instance: RegionalDatabaseManager;\n  private regionalClients: Map<string, PrismaClient> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): RegionalDatabaseManager {\n    if (!RegionalDatabaseManager.instance) {\n      RegionalDatabaseManager.instance = new RegionalDatabaseManager();\n    }\n    return RegionalDatabaseManager.instance;\n  }\n\n  // Get database client for specific region\n  getRegionalClient(region: string): PrismaClient {\n    if (!this.regionalClients.has(region)) {\n      // In production, this would connect to region-specific database URLs\n      const regionalUrl = this.getRegionalDatabaseUrl(region);\n      \n      const client = new PrismaClient({\n        datasources: {\n          db: {\n            url: regionalUrl,\n          },\n        },\n        log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],\n      });\n\n      this.regionalClients.set(region, client);\n    }\n\n    return this.regionalClients.get(region)!;\n  }\n\n  // Get region-specific database URL\n  private getRegionalDatabaseUrl(region: string): string {\n    // In production, you would have different database URLs for each region\n    const regionalUrls: Record<string, string> = {\n      'US': process.env.DATABASE_URL_US || process.env.DATABASE_URL || '',\n      'EU': process.env.DATABASE_URL_EU || process.env.DATABASE_URL || '',\n      'UK': process.env.DATABASE_URL_UK || process.env.DATABASE_URL || '',\n      'CA': process.env.DATABASE_URL_CA || process.env.DATABASE_URL || '',\n      'IN': process.env.DATABASE_URL_IN || process.env.DATABASE_URL || '',\n      'SG': process.env.DATABASE_URL_SG || process.env.DATABASE_URL || '',\n      'JP': process.env.DATABASE_URL_JP || process.env.DATABASE_URL || '',\n      'AU': process.env.DATABASE_URL_AU || process.env.DATABASE_URL || '',\n      'BR': process.env.DATABASE_URL_BR || process.env.DATABASE_URL || '',\n      'CN': process.env.DATABASE_URL_CN || process.env.DATABASE_URL || '',\n    };\n\n    return regionalUrls[region] || process.env.DATABASE_URL || '';\n  }\n\n  // Disconnect all regional clients\n  async disconnectAll(): Promise<void> {\n    const disconnectPromises = Array.from(this.regionalClients.values()).map(\n      client => client.$disconnect()\n    );\n    await Promise.all(disconnectPromises);\n    this.regionalClients.clear();\n  }\n}\n\n// Audit logging utility\nexport async function createAuditLog(\n  eventType: string,\n  action: string,\n  resource: string,\n  resourceId?: string,\n  userId?: string,\n  oldValues?: any,\n  newValues?: any,\n  metadata?: any,\n  region?: string,\n  ipAddress?: string,\n  userAgent?: string\n): Promise<void> {\n  try {\n    const client = region ? \n      RegionalDatabaseManager.getInstance().getRegionalClient(region) : \n      prisma;\n\n    await client.auditLog.create({\n      data: {\n        eventType,\n        action,\n        resource,\n        resourceId,\n        userId,\n        oldValues: oldValues ? JSON.parse(JSON.stringify(oldValues)) : null,\n        newValues: newValues ? JSON.parse(JSON.stringify(newValues)) : null,\n        metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,\n        region,\n        ipAddress,\n        userAgent,\n      },\n    });\n  } catch (error) {\n    console.error('Failed to create audit log:', error);\n    // Don't throw error to avoid breaking main functionality\n  }\n}\n\n// Data retention cleanup utility\nexport async function cleanupExpiredData(): Promise<void> {\n  try {\n    console.log('Starting data retention cleanup...');\n\n    // Get all tenants and their retention policies\n    const tenants = await prisma.tenant.findMany({\n      include: {\n        privacyConfig: true,\n      },\n    });\n\n    for (const tenant of tenants) {\n      if (!tenant.privacyConfig) continue;\n\n      const config = tenant.privacyConfig;\n      const now = new Date();\n\n      // Clean up expired consents\n      const expiredConsentsDate = new Date(now.getTime() - (config.contactInfoRetention * 24 * 60 * 60 * 1000));\n      \n      await prisma.consent.deleteMany({\n        where: {\n          user: {\n            tenantId: tenant.id,\n          },\n          createdAt: {\n            lt: expiredConsentsDate,\n          },\n          granted: false, // Only delete withdrawn consents after retention period\n        },\n      });\n\n      // Clean up old audit logs (keep for compliance period)\n      const auditRetentionDate = new Date(now.getTime() - (2555 * 24 * 60 * 60 * 1000)); // 7 years\n      \n      await prisma.auditLog.deleteMany({\n        where: {\n          user: {\n            tenantId: tenant.id,\n          },\n          timestamp: {\n            lt: auditRetentionDate,\n          },\n        },\n      });\n\n      console.log(`Cleaned up expired data for tenant: ${tenant.name}`);\n    }\n\n    console.log('Data retention cleanup completed');\n  } catch (error) {\n    console.error('Data retention cleanup failed:', error);\n  }\n}\n\n// Database seeding for development\nexport async function seedDatabase(): Promise<void> {\n  try {\n    console.log('Seeding database...');\n\n    // Create default tenant\n    const defaultTenant = await prisma.tenant.upsert({\n      where: { slug: 'default' },\n      update: {},\n      create: {\n        name: 'Privacy First Application',\n        slug: 'default',\n        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n        defaultRegion: 'US',\n        privacyConfig: {\n          create: {\n            requireExplicitConsent: true,\n            enableRightToBeForgotten: true,\n            enableDataPortability: true,\n            enableOptOut: true,\n            cookieConsentRequired: true,\n            minorProtection: true,\n          },\n        },\n      },\n    });\n\n    // Create default data processing activities\n    const activities = [\n      {\n        name: 'User Account Management',\n        description: 'Creating and managing user accounts',\n        dataCategories: ['PERSONAL_IDENTIFIERS', 'CONTACT_INFO'],\n        purposes: ['Account Creation', 'Authentication', 'User Support'],\n        legalBasis: 'Contract Performance',\n        retentionPeriod: 2555,\n        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n        thirdParties: [],\n      },\n      {\n        name: 'Marketing Communications',\n        description: 'Sending promotional emails and personalized offers',\n        dataCategories: ['CONTACT_INFO', 'BEHAVIORAL_DATA'],\n        purposes: ['Marketing', 'Personalization'],\n        legalBasis: 'Consent',\n        retentionPeriod: 1095,\n        regions: ['US', 'EU', 'UK', 'CA'],\n        thirdParties: [],\n      },\n      {\n        name: 'Usage Analytics',\n        description: 'Analyzing user behavior to improve our services',\n        dataCategories: ['BEHAVIORAL_DATA', 'LOCATION_DATA'],\n        purposes: ['Analytics', 'Service Improvement'],\n        legalBasis: 'Legitimate Interest',\n        retentionPeriod: 730,\n        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],\n        thirdParties: [],\n      },\n    ];\n\n    for (const activity of activities) {\n      await prisma.dataProcessingActivity.upsert({\n        where: {\n          tenantId_name: {\n            tenantId: defaultTenant.id,\n            name: activity.name,\n          },\n        },\n        update: activity,\n        create: {\n          ...activity,\n          tenantId: defaultTenant.id,\n        },\n      });\n    }\n\n    // Create compliance status records\n    const regions = ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'];\n    const laws = {\n      'US': ['CCPA', 'CPRA'],\n      'EU': ['GDPR'],\n      'UK': ['UK GDPR', 'DPA 2018'],\n      'CA': ['PIPEDA', 'CPPA'],\n      'IN': ['DPDP'],\n      'SG': ['PDPA'],\n      'JP': ['APPI'],\n      'AU': ['Privacy Act'],\n      'BR': ['LGPD'],\n    };\n\n    for (const region of regions) {\n      const regionLaws = laws[region as keyof typeof laws] || [];\n      \n      for (const law of regionLaws) {\n        await prisma.complianceStatus.upsert({\n          where: {\n            tenantId_region_law: {\n              tenantId: defaultTenant.id,\n              region,\n              law,\n            },\n          },\n          update: {},\n          create: {\n            tenantId: defaultTenant.id,\n            region,\n            law,\n            isCompliant: true,\n            lastAssessment: new Date(),\n            nextAssessment: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year\n            complianceScore: 95,\n            findings: {\n              strengths: ['Strong consent management', 'Comprehensive audit logging'],\n              improvements: ['Enhanced data minimization', 'Automated compliance monitoring'],\n            },\n          },\n        });\n      }\n    }\n\n    // Create default roles\n    const roles = [\n      {\n        name: 'super_admin',\n        description: 'Super administrator with all permissions',\n        permissions: ['*'],\n        isSystem: true,\n      },\n      {\n        name: 'admin',\n        description: 'Administrator with most permissions',\n        permissions: [\n          'users:read', 'users:create', 'users:update',\n          'privacy:read', 'privacy:manage',\n          'consents:read', 'consents:manage',\n          'audit:read',\n          'api:read', 'api:write'\n        ],\n        isSystem: true,\n      },\n      {\n        name: 'privacy_officer',\n        description: 'Privacy officer with privacy management permissions',\n        permissions: [\n          'users:read',\n          'privacy:read', 'privacy:manage', 'privacy:export', 'privacy:delete',\n          'consents:read', 'consents:manage',\n          'audit:read'\n        ],\n        isSystem: true,\n      },\n      {\n        name: 'user',\n        description: 'Standard user with basic permissions',\n        permissions: [\n          'privacy:read',\n          'consents:read'\n        ],\n        isSystem: true,\n      },\n    ];\n\n    for (const role of roles) {\n      await prisma.role.upsert({\n        where: { name: role.name },\n        update: role,\n        create: role,\n      });\n    }\n\n    console.log('Database seeded successfully');\n  } catch (error) {\n    console.error('Database seeding failed:', error);\n    throw error;\n  }\n}\n\n// Export regional database manager instance\nexport const regionalDb = RegionalDatabaseManager.getInstance();\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAQO,MAAM,SAAS,WAAW,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY,CAAC;IAC1D,KAAK,uCAAyC;QAAC;QAAS;QAAS;KAAO;IACxE,aAAa;QACX,IAAI;YACF,KAAK,QAAQ,GAAG,CAAC,YAAY;QAC/B;IACF;AACF;AAEA,wCAA2C;IACzC,WAAW,MAAM,GAAG;AACtB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,MAAM;IACX,OAAe,SAAkC;IACzC,kBAA6C,IAAI,MAAM;IAE/D,aAAsB,CAAC;IAEvB,OAAO,cAAuC;QAC5C,IAAI,CAAC,wBAAwB,QAAQ,EAAE;YACrC,wBAAwB,QAAQ,GAAG,IAAI;QACzC;QACA,OAAO,wBAAwB,QAAQ;IACzC;IAEA,0CAA0C;IAC1C,kBAAkB,MAAc,EAAgB;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS;YACrC,qEAAqE;YACrE,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC;YAEhD,MAAM,SAAS,IAAI,6HAAA,CAAA,eAAY,CAAC;gBAC9B,aAAa;oBACX,IAAI;wBACF,KAAK;oBACP;gBACF;gBACA,KAAK,uCAAyC;oBAAC;iBAAQ;YACzD;YAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ;QACnC;QAEA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;IAEA,mCAAmC;IAC3B,uBAAuB,MAAc,EAAU;QACrD,wEAAwE;QACxE,MAAM,eAAuC;YAC3C,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;YACjE,MAAM,QAAQ,GAAG,CAAC,eAAe,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;QACnE;QAEA,OAAO,YAAY,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,YAAY,IAAI;IAC7D;IAEA,kCAAkC;IAClC,MAAM,gBAA+B;QACnC,MAAM,qBAAqB,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,GAAG,CACtE,CAAA,SAAU,OAAO,WAAW;QAE9B,MAAM,QAAQ,GAAG,CAAC;QAClB,IAAI,CAAC,eAAe,CAAC,KAAK;IAC5B;AACF;AAGO,eAAe,eACpB,SAAiB,EACjB,MAAc,EACd,QAAgB,EAChB,UAAmB,EACnB,MAAe,EACf,SAAe,EACf,SAAe,EACf,QAAc,EACd,MAAe,EACf,SAAkB,EAClB,SAAkB;IAElB,IAAI;QACF,MAAM,SAAS,SACb,wBAAwB,WAAW,GAAG,iBAAiB,CAAC,UACxD;QAEF,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC;YAC3B,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA,WAAW,YAAY,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,cAAc;gBAC/D,WAAW,YAAY,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,cAAc;gBAC/D,UAAU,WAAW,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,aAAa;gBAC5D;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC7C,yDAAyD;IAC3D;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,+CAA+C;QAC/C,MAAM,UAAU,MAAM,OAAO,MAAM,CAAC,QAAQ,CAAC;YAC3C,SAAS;gBACP,eAAe;YACjB;QACF;QAEA,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI,CAAC,OAAO,aAAa,EAAE;YAE3B,MAAM,SAAS,OAAO,aAAa;YACnC,MAAM,MAAM,IAAI;YAEhB,4BAA4B;YAC5B,MAAM,sBAAsB,IAAI,KAAK,IAAI,OAAO,KAAM,OAAO,oBAAoB,GAAG,KAAK,KAAK,KAAK;YAEnG,MAAM,OAAO,OAAO,CAAC,UAAU,CAAC;gBAC9B,OAAO;oBACL,MAAM;wBACJ,UAAU,OAAO,EAAE;oBACrB;oBACA,WAAW;wBACT,IAAI;oBACN;oBACA,SAAS;gBACX;YACF;YAEA,uDAAuD;YACvD,MAAM,qBAAqB,IAAI,KAAK,IAAI,OAAO,KAAM,OAAO,KAAK,KAAK,KAAK,OAAQ,UAAU;YAE7F,MAAM,OAAO,QAAQ,CAAC,UAAU,CAAC;gBAC/B,OAAO;oBACL,MAAM;wBACJ,UAAU,OAAO,EAAE;oBACrB;oBACA,WAAW;wBACT,IAAI;oBACN;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,OAAO,IAAI,EAAE;QAClE;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,wBAAwB;QACxB,MAAM,gBAAgB,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC;YAC/C,OAAO;gBAAE,MAAM;YAAU;YACzB,QAAQ,CAAC;YACT,QAAQ;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;gBAC/D,eAAe;gBACf,eAAe;oBACb,QAAQ;wBACN,wBAAwB;wBACxB,0BAA0B;wBAC1B,uBAAuB;wBACvB,cAAc;wBACd,uBAAuB;wBACvB,iBAAiB;oBACnB;gBACF;YACF;QACF;QAEA,4CAA4C;QAC5C,MAAM,aAAa;YACjB;gBACE,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAwB;iBAAe;gBACxD,UAAU;oBAAC;oBAAoB;oBAAkB;iBAAe;gBAChE,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;gBAC/D,cAAc,EAAE;YAClB;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAgB;iBAAkB;gBACnD,UAAU;oBAAC;oBAAa;iBAAkB;gBAC1C,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;iBAAK;gBACjC,cAAc,EAAE;YAClB;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,gBAAgB;oBAAC;oBAAmB;iBAAgB;gBACpD,UAAU;oBAAC;oBAAa;iBAAsB;gBAC9C,YAAY;gBACZ,iBAAiB;gBACjB,SAAS;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;gBAC/D,cAAc,EAAE;YAClB;SACD;QAED,KAAK,MAAM,YAAY,WAAY;YACjC,MAAM,OAAO,sBAAsB,CAAC,MAAM,CAAC;gBACzC,OAAO;oBACL,eAAe;wBACb,UAAU,cAAc,EAAE;wBAC1B,MAAM,SAAS,IAAI;oBACrB;gBACF;gBACA,QAAQ;gBACR,QAAQ;oBACN,GAAG,QAAQ;oBACX,UAAU,cAAc,EAAE;gBAC5B;YACF;QACF;QAEA,mCAAmC;QACnC,MAAM,UAAU;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACtE,MAAM,OAAO;YACX,MAAM;gBAAC;gBAAQ;aAAO;YACtB,MAAM;gBAAC;aAAO;YACd,MAAM;gBAAC;gBAAW;aAAW;YAC7B,MAAM;gBAAC;gBAAU;aAAO;YACxB,MAAM;gBAAC;aAAO;YACd,MAAM;gBAAC;aAAO;YACd,MAAM;gBAAC;aAAO;YACd,MAAM;gBAAC;aAAc;YACrB,MAAM;gBAAC;aAAO;QAChB;QAEA,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,aAAa,IAAI,CAAC,OAA4B,IAAI,EAAE;YAE1D,KAAK,MAAM,OAAO,WAAY;gBAC5B,MAAM,OAAO,gBAAgB,CAAC,MAAM,CAAC;oBACnC,OAAO;wBACL,qBAAqB;4BACnB,UAAU,cAAc,EAAE;4BAC1B;4BACA;wBACF;oBACF;oBACA,QAAQ,CAAC;oBACT,QAAQ;wBACN,UAAU,cAAc,EAAE;wBAC1B;wBACA;wBACA,aAAa;wBACb,gBAAgB,IAAI;wBACpB,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAK,MAAM,KAAK,KAAK,KAAK;wBAC3D,iBAAiB;wBACjB,UAAU;4BACR,WAAW;gCAAC;gCAA6B;6BAA8B;4BACvE,cAAc;gCAAC;gCAA8B;6BAAkC;wBACjF;oBACF;gBACF;YACF;QACF;QAEA,uBAAuB;QACvB,MAAM,QAAQ;YACZ;gBACE,MAAM;gBACN,aAAa;gBACb,aAAa;oBAAC;iBAAI;gBAClB,UAAU;YACZ;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,aAAa;oBACX;oBAAc;oBAAgB;oBAC9B;oBAAgB;oBAChB;oBAAiB;oBACjB;oBACA;oBAAY;iBACb;gBACD,UAAU;YACZ;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,aAAa;oBACX;oBACA;oBAAgB;oBAAkB;oBAAkB;oBACpD;oBAAiB;oBACjB;iBACD;gBACD,UAAU;YACZ;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,aAAa;oBACX;oBACA;iBACD;gBACD,UAAU;YACZ;SACD;QAED,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;gBACvB,OAAO;oBAAE,MAAM,KAAK,IAAI;gBAAC;gBACzB,QAAQ;gBACR,QAAQ;YACV;QACF;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,MAAM,aAAa,wBAAwB,WAAW", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport { PrismaAdapter } from '@auth/prisma-adapter';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport bcrypt from 'bcryptjs';\nimport { prisma } from './database';\nimport { createAuditLog } from './database';\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n        tenantSlug: { label: 'Organization', type: 'text' },\n      },\n      async authorize(credentials, req) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // Find tenant\n          const tenant = await prisma.tenant.findUnique({\n            where: { slug: credentials.tenantSlug || 'default' },\n          });\n\n          if (!tenant) {\n            return null;\n          }\n\n          // Find user\n          const user = await prisma.user.findUnique({\n            where: {\n              tenantId_email: {\n                tenantId: tenant.id,\n                email: credentials.email,\n              },\n            },\n            include: {\n              userRoles: {\n                include: {\n                  role: true,\n                },\n              },\n            },\n          });\n\n          if (!user || !user.hashedPassword) {\n            return null;\n          }\n\n          // Verify password\n          const isPasswordValid = await bcrypt.compare(\n            credentials.password,\n            user.hashedPassword\n          );\n\n          if (!isPasswordValid) {\n            return null;\n          }\n\n          // Update last login\n          await prisma.user.update({\n            where: { id: user.id },\n            data: { lastLoginAt: new Date() },\n          });\n\n          // Create audit log\n          await createAuditLog(\n            'USER_LOGIN',\n            'READ',\n            'user',\n            user.id,\n            user.id,\n            null,\n            { loginMethod: 'credentials' },\n            { \n              userAgent: req.headers?.['user-agent'],\n              ip: req.headers?.['x-forwarded-for'] || req.headers?.['x-real-ip']\n            },\n            user.preferredRegion,\n            req.headers?.['x-forwarded-for'] as string || req.headers?.['x-real-ip'] as string,\n            req.headers?.['user-agent'] as string\n          );\n\n          return {\n            id: user.id,\n            email: user.email,\n            name: `${user.firstName} ${user.lastName}`,\n            image: null,\n            tenantId: user.tenantId,\n            preferredRegion: user.preferredRegion,\n            roles: user.userRoles.map(ur => ur.role.name),\n          };\n        } catch (error) {\n          console.error('Authentication error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.tenantId = user.tenantId;\n        token.preferredRegion = user.preferredRegion;\n        token.roles = user.roles;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.tenantId = token.tenantId as string;\n        session.user.preferredRegion = token.preferredRegion as string;\n        session.user.roles = token.roles as string[];\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n  },\n  events: {\n    async signOut({ token }) {\n      if (token?.sub) {\n        await createAuditLog(\n          'USER_LOGOUT',\n          'READ',\n          'user',\n          token.sub,\n          token.sub,\n          null,\n          { logoutMethod: 'manual' },\n          null,\n          token.preferredRegion as string\n        );\n      }\n    },\n  },\n};\n\n// Role-based access control utilities\nexport async function hasPermission(\n  userId: string,\n  permission: string,\n  tenantId?: string\n): Promise<boolean> {\n  try {\n    const userRoles = await prisma.userRole.findMany({\n      where: {\n        userId,\n        ...(tenantId && { tenantId }),\n        OR: [\n          { expiresAt: null },\n          { expiresAt: { gt: new Date() } },\n        ],\n      },\n      include: {\n        role: true,\n      },\n    });\n\n    return userRoles.some(userRole =>\n      userRole.role.permissions.includes(permission) ||\n      userRole.role.permissions.includes('*') // Super admin\n    );\n  } catch (error) {\n    console.error('Permission check failed:', error);\n    return false;\n  }\n}\n\nexport async function getUserRoles(userId: string, tenantId?: string): Promise<string[]> {\n  try {\n    const userRoles = await prisma.userRole.findMany({\n      where: {\n        userId,\n        ...(tenantId && { tenantId }),\n        OR: [\n          { expiresAt: null },\n          { expiresAt: { gt: new Date() } },\n        ],\n      },\n      include: {\n        role: true,\n      },\n    });\n\n    return userRoles.map(ur => ur.role.name);\n  } catch (error) {\n    console.error('Failed to get user roles:', error);\n    return [];\n  }\n}\n\nexport async function assignRole(\n  userId: string,\n  roleName: string,\n  tenantId: string,\n  grantedBy?: string,\n  expiresAt?: Date\n): Promise<boolean> {\n  try {\n    const role = await prisma.role.findUnique({\n      where: { name: roleName },\n    });\n\n    if (!role) {\n      return false;\n    }\n\n    await prisma.userRole.create({\n      data: {\n        userId,\n        roleId: role.id,\n        tenantId,\n        grantedBy,\n        expiresAt,\n      },\n    });\n\n    // Create audit log\n    await createAuditLog(\n      'ROLE_ASSIGNMENT',\n      'CREATE',\n      'user_role',\n      undefined,\n      grantedBy || userId,\n      null,\n      { userId, roleName, tenantId },\n      { grantedBy, expiresAt }\n    );\n\n    return true;\n  } catch (error) {\n    console.error('Failed to assign role:', error);\n    return false;\n  }\n}\n\nexport async function revokeRole(\n  userId: string,\n  roleName: string,\n  tenantId: string,\n  revokedBy?: string\n): Promise<boolean> {\n  try {\n    const role = await prisma.role.findUnique({\n      where: { name: roleName },\n    });\n\n    if (!role) {\n      return false;\n    }\n\n    const deletedRole = await prisma.userRole.deleteMany({\n      where: {\n        userId,\n        roleId: role.id,\n        tenantId,\n      },\n    });\n\n    if (deletedRole.count > 0) {\n      // Create audit log\n      await createAuditLog(\n        'ROLE_REVOCATION',\n        'DELETE',\n        'user_role',\n        undefined,\n        revokedBy || userId,\n        { userId, roleName, tenantId },\n        null,\n        { revokedBy }\n      );\n    }\n\n    return deletedRole.count > 0;\n  } catch (error) {\n    console.error('Failed to revoke role:', error);\n    return false;\n  }\n}\n\n// Password utilities\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12);\n}\n\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  return bcrypt.compare(password, hash);\n}\n\n// Default permissions\nexport const PERMISSIONS = {\n  // User management\n  'users:read': 'View users',\n  'users:create': 'Create users',\n  'users:update': 'Update users',\n  'users:delete': 'Delete users',\n  \n  // Privacy management\n  'privacy:read': 'View privacy data',\n  'privacy:manage': 'Manage privacy settings',\n  'privacy:export': 'Export user data',\n  'privacy:delete': 'Delete user data',\n  \n  // Consent management\n  'consents:read': 'View consents',\n  'consents:manage': 'Manage consents',\n  \n  // Audit logs\n  'audit:read': 'View audit logs',\n  \n  // System administration\n  'system:admin': 'System administration',\n  'tenants:manage': 'Manage tenants',\n  'roles:manage': 'Manage roles',\n  \n  // API access\n  'api:read': 'API read access',\n  'api:write': 'API write access',\n  \n  // Super admin (all permissions)\n  '*': 'All permissions',\n} as const;\n\nexport type Permission = keyof typeof PERMISSIONS;\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;;;;;;AAGO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,wHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;gBAChD,YAAY;oBAAE,OAAO;oBAAgB,MAAM;gBAAO;YACpD;YACA,MAAM,WAAU,WAAW,EAAE,GAAG;gBAC9B,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,cAAc;oBACd,MAAM,SAAS,MAAM,wHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;wBAC5C,OAAO;4BAAE,MAAM,YAAY,UAAU,IAAI;wBAAU;oBACrD;oBAEA,IAAI,CAAC,QAAQ;wBACX,OAAO;oBACT;oBAEA,YAAY;oBACZ,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,gBAAgB;gCACd,UAAU,OAAO,EAAE;gCACnB,OAAO,YAAY,KAAK;4BAC1B;wBACF;wBACA,SAAS;4BACP,WAAW;gCACT,SAAS;oCACP,MAAM;gCACR;4BACF;wBACF;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,kBAAkB;oBAClB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,iBAAiB;wBACpB,OAAO;oBACT;oBAEA,oBAAoB;oBACpB,MAAM,wHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;wBACvB,OAAO;4BAAE,IAAI,KAAK,EAAE;wBAAC;wBACrB,MAAM;4BAAE,aAAa,IAAI;wBAAO;oBAClC;oBAEA,mBAAmB;oBACnB,MAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EACjB,cACA,QACA,QACA,KAAK,EAAE,EACP,KAAK,EAAE,EACP,MACA;wBAAE,aAAa;oBAAc,GAC7B;wBACE,WAAW,IAAI,OAAO,EAAE,CAAC,aAAa;wBACtC,IAAI,IAAI,OAAO,EAAE,CAAC,kBAAkB,IAAI,IAAI,OAAO,EAAE,CAAC,YAAY;oBACpE,GACA,KAAK,eAAe,EACpB,IAAI,OAAO,EAAE,CAAC,kBAAkB,IAAc,IAAI,OAAO,EAAE,CAAC,YAAY,EACxE,IAAI,OAAO,EAAE,CAAC,aAAa;oBAG7B,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,OAAO;wBACP,UAAU,KAAK,QAAQ;wBACvB,iBAAiB,KAAK,eAAe;wBACrC,OAAO,KAAK,SAAS,CAAC,GAAG,CAAC,CAAA,KAAM,GAAG,IAAI,CAAC,IAAI;oBAC9C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,eAAe,GAAG,KAAK,eAAe;gBAC5C,MAAM,KAAK,GAAG,KAAK,KAAK;YAC1B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,eAAe,GAAG,MAAM,eAAe;gBACpD,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;YAClC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM,SAAQ,EAAE,KAAK,EAAE;YACrB,IAAI,OAAO,KAAK;gBACd,MAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EACjB,eACA,QACA,QACA,MAAM,GAAG,EACT,MAAM,GAAG,EACT,MACA;oBAAE,cAAc;gBAAS,GACzB,MACA,MAAM,eAAe;YAEzB;QACF;IACF;AACF;AAGO,eAAe,cACpB,MAAc,EACd,UAAkB,EAClB,QAAiB;IAEjB,IAAI;QACF,MAAM,YAAY,MAAM,wHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBACL;gBACA,GAAI,YAAY;oBAAE;gBAAS,CAAC;gBAC5B,IAAI;oBACF;wBAAE,WAAW;oBAAK;oBAClB;wBAAE,WAAW;4BAAE,IAAI,IAAI;wBAAO;oBAAE;iBACjC;YACH;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,OAAO,UAAU,IAAI,CAAC,CAAA,WACpB,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,eACnC,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,cAAc;;IAE1D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAEO,eAAe,aAAa,MAAc,EAAE,QAAiB;IAClE,IAAI;QACF,MAAM,YAAY,MAAM,wHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBACL;gBACA,GAAI,YAAY;oBAAE;gBAAS,CAAC;gBAC5B,IAAI;oBACF;wBAAE,WAAW;oBAAK;oBAClB;wBAAE,WAAW;4BAAE,IAAI,IAAI;wBAAO;oBAAE;iBACjC;YACH;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,OAAO,UAAU,GAAG,CAAC,CAAA,KAAM,GAAG,IAAI,CAAC,IAAI;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,WACpB,MAAc,EACd,QAAgB,EAChB,QAAgB,EAChB,SAAkB,EAClB,SAAgB;IAEhB,IAAI;QACF,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,MAAM;YAAS;QAC1B;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,MAAM,wHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,MAAM;gBACJ;gBACA,QAAQ,KAAK,EAAE;gBACf;gBACA;gBACA;YACF;QACF;QAEA,mBAAmB;QACnB,MAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EACjB,mBACA,UACA,aACA,WACA,aAAa,QACb,MACA;YAAE;YAAQ;YAAU;QAAS,GAC7B;YAAE;YAAW;QAAU;QAGzB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAEO,eAAe,WACpB,MAAc,EACd,QAAgB,EAChB,QAAgB,EAChB,SAAkB;IAElB,IAAI;QACF,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,MAAM;YAAS;QAC1B;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,MAAM,cAAc,MAAM,wHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YACnD,OAAO;gBACL;gBACA,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,IAAI,YAAY,KAAK,GAAG,GAAG;YACzB,mBAAmB;YACnB,MAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EACjB,mBACA,UACA,aACA,WACA,aAAa,QACb;gBAAE;gBAAQ;gBAAU;YAAS,GAC7B,MACA;gBAAE;YAAU;QAEhB;QAEA,OAAO,YAAY,KAAK,GAAG;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAGO,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAEO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,MAAM,cAAc;IACzB,kBAAkB;IAClB,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAEhB,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAClB,kBAAkB;IAClB,kBAAkB;IAElB,qBAAqB;IACrB,iBAAiB;IACjB,mBAAmB;IAEnB,aAAa;IACb,cAAc;IAEd,wBAAwB;IACxB,gBAAgB;IAChB,kBAAkB;IAClB,gBAAgB;IAEhB,aAAa;IACb,YAAY;IACZ,aAAa;IAEb,gCAAgC;IAChC,KAAK;AACP", "debugId": null}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,cAAW", "debugId": null}}]}
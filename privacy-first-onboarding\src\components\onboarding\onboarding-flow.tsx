'use client';

import { useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle } from 'lucide-react';
import { RegionSelector } from './region-selector';
import { ConsentManager } from './consent-manager';
import { AccountForm } from './account-form';
import { OnboardingSuccess } from './onboarding-success';
import { privacyService } from '@/lib/privacy-service';
import type { PrivacyRegion } from '@/lib/utils';

type OnboardingStep = 'region' | 'consent' | 'account' | 'success';

interface OnboardingState {
  currentStep: OnboardingStep;
  selectedRegion: PrivacyRegion | null;
  consents: Record<string, boolean>;
  isLoading: boolean;
  userId: string | null;
}

export function OnboardingFlow() {
  const [state, setState] = useState<OnboardingState>({
    currentStep: 'region',
    selectedRegion: null,
    consents: {
      // Set required consents to true by default
      'PERSONAL_IDENTIFIERS_account_creation_and_management': true,
      'CONTACT_INFO_service_communications': true,
    },
    isLoading: false,
    userId: null,
  });

  const steps = [
    { key: 'region', label: 'Data Region', description: 'Choose your data location' },
    { key: 'consent', label: 'Privacy Preferences', description: 'Manage your data consent' },
    { key: 'account', label: 'Account Details', description: 'Complete your profile' },
    { key: 'success', label: 'Complete', description: 'Account created successfully' },
  ];

  const currentStepIndex = steps.findIndex(step => step.key === state.currentStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  const handleRegionSelect = (region: PrivacyRegion) => {
    setState(prev => ({ ...prev, selectedRegion: region }));
  };

  const handleRegionNext = () => {
    setState(prev => ({ ...prev, currentStep: 'consent' }));
  };

  const handleConsentChange = (consentKey: string, granted: boolean) => {
    setState(prev => ({
      ...prev,
      consents: { ...prev.consents, [consentKey]: granted }
    }));
  };

  const handleConsentNext = () => {
    setState(prev => ({ ...prev, currentStep: 'account' }));
  };

  const handleConsentBack = () => {
    setState(prev => ({ ...prev, currentStep: 'region' }));
  };

  const handleAccountSubmit = async (accountData: any) => {
    if (!state.selectedRegion) return;

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Prepare consent data for registration
      const consentData = Object.entries(state.consents)
        .filter(([_, granted]) => granted)
        .map(([key, _]) => {
          // Parse consent key to extract data category and purpose
          const [dataCategory, ...purposeParts] = key.split('_');
          const purpose = purposeParts.join(' ').replace(/_/g, ' ');
          
          return {
            dataCategory: dataCategory as any,
            purpose: purpose,
            granted: true,
            region: state.selectedRegion!,
          };
        });

      // Register user with privacy service
      const registrationData = {
        firstName: accountData.firstName,
        lastName: accountData.lastName,
        email: accountData.email,
        preferredRegion: state.selectedRegion,
        dataResidencyPreference: accountData.dataResidencyPreference,
        consents: consentData,
        communicationPreferences: {
          marketing: accountData.marketingEmails,
          analytics: accountData.analyticsOptIn,
          personalization: false,
          thirdPartySharing: false,
        },
        acceptTerms: accountData.acceptTerms,
        acceptPrivacyPolicy: accountData.acceptPrivacyPolicy,
        ageVerification: accountData.ageVerification,
      };

      const result = await privacyService.registerUser(registrationData);
      
      if (result.success) {
        setState(prev => ({ 
          ...prev, 
          userId: result.userId,
          currentStep: 'success',
          isLoading: false 
        }));
      } else {
        throw new Error('Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      setState(prev => ({ ...prev, isLoading: false }));
      // In a real app, show error message to user
    }
  };

  const handleAccountBack = () => {
    setState(prev => ({ ...prev, currentStep: 'consent' }));
  };

  const renderCurrentStep = () => {
    switch (state.currentStep) {
      case 'region':
        return (
          <RegionSelector
            selectedRegion={state.selectedRegion}
            onRegionSelect={handleRegionSelect}
            onNext={handleRegionNext}
          />
        );
      
      case 'consent':
        return state.selectedRegion ? (
          <ConsentManager
            selectedRegion={state.selectedRegion}
            consents={state.consents}
            onConsentChange={handleConsentChange}
            onNext={handleConsentNext}
            onBack={handleConsentBack}
          />
        ) : null;
      
      case 'account':
        return state.selectedRegion ? (
          <AccountForm
            selectedRegion={state.selectedRegion}
            onSubmit={handleAccountSubmit}
            onBack={handleAccountBack}
            isLoading={state.isLoading}
          />
        ) : null;
      
      case 'success':
        return state.userId ? (
          <OnboardingSuccess userId={state.userId} />
        ) : null;
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Privacy-First Account Setup
          </h1>
          <p className="text-lg text-gray-600">
            Create your account with complete control over your data
          </p>
        </div>

        {/* Progress Indicator */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">
                  Step {currentStepIndex + 1} of {steps.length}
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round(progress)}% Complete
                </span>
              </div>
              
              <Progress value={progress} className="h-2" />
              
              <div className="flex justify-between">
                {steps.map((step, index) => {
                  const isCompleted = index < currentStepIndex;
                  const isCurrent = index === currentStepIndex;
                  
                  return (
                    <div key={step.key} className="flex flex-col items-center text-center">
                      <div className={`
                        flex items-center justify-center w-8 h-8 rounded-full border-2 mb-2
                        ${isCompleted 
                          ? 'bg-green-500 border-green-500 text-white' 
                          : isCurrent 
                            ? 'border-blue-500 text-blue-500' 
                            : 'border-gray-300 text-gray-300'
                        }
                      `}>
                        {isCompleted ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <span className="text-sm font-medium">{index + 1}</span>
                        )}
                      </div>
                      <div className="space-y-1">
                        <div className={`text-sm font-medium ${
                          isCurrent ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                        }`}>
                          {step.label}
                        </div>
                        <div className="text-xs text-gray-500 max-w-20">
                          {step.description}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current Step Content */}
        <div className="mb-8">
          {renderCurrentStep()}
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>
            Your privacy is our priority. All data is encrypted and processed according to 
            applicable privacy laws.
          </p>
          <div className="flex justify-center gap-4 mt-2">
            <a href="/privacy" className="hover:text-gray-700">Privacy Policy</a>
            <a href="/terms" className="hover:text-gray-700">Terms of Service</a>
            <a href="/security" className="hover:text-gray-700">Security</a>
          </div>
        </div>
      </div>
    </div>
  );
}

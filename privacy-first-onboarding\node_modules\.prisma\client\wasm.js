
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.TenantScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  regions: 'regions',
  defaultRegion: 'defaultRegion',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TenantPrivacyConfigScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  requireExplicitConsent: 'requireExplicitConsent',
  enableRightToBeForgotten: 'enableRightToBeForgotten',
  enableDataPortability: 'enableDataPortability',
  enableOptOut: 'enableOptOut',
  cookieConsentRequired: 'cookieConsentRequired',
  minorProtection: 'minorProtection',
  personalIdentifiersRetention: 'personalIdentifiersRetention',
  contactInfoRetention: 'contactInfoRetention',
  financialInfoRetention: 'financialInfoRetention',
  biometricDataRetention: 'biometricDataRetention',
  locationDataRetention: 'locationDataRetention',
  behavioralDataRetention: 'behavioralDataRetention',
  sensitivePersonalRetention: 'sensitivePersonalRetention',
  healthDataRetention: 'healthDataRetention',
  employmentDataRetention: 'employmentDataRetention',
  educationDataRetention: 'educationDataRetention',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  hashedPassword: 'hashedPassword',
  preferredRegion: 'preferredRegion',
  dataResidencyPreference: 'dataResidencyPreference',
  marketingConsent: 'marketingConsent',
  analyticsConsent: 'analyticsConsent',
  personalizationConsent: 'personalizationConsent',
  thirdPartySharing: 'thirdPartySharing',
  isActive: 'isActive',
  emailVerified: 'emailVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLoginAt: 'lastLoginAt'
};

exports.Prisma.ConsentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  dataCategory: 'dataCategory',
  purpose: 'purpose',
  legalBasis: 'legalBasis',
  granted: 'granted',
  version: 'version',
  grantedAt: 'grantedAt',
  withdrawnAt: 'withdrawnAt',
  expiresAt: 'expiresAt',
  region: 'region',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  consentMethod: 'consentMethod',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DataProcessingActivityScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  name: 'name',
  description: 'description',
  dataCategories: 'dataCategories',
  purposes: 'purposes',
  legalBasis: 'legalBasis',
  retentionPeriod: 'retentionPeriod',
  regions: 'regions',
  thirdParties: 'thirdParties',
  isActive: 'isActive',
  lastReviewed: 'lastReviewed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PrivacyRequestScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  requestType: 'requestType',
  status: 'status',
  description: 'description',
  requestedAt: 'requestedAt',
  acknowledgedAt: 'acknowledgedAt',
  completedAt: 'completedAt',
  rejectedAt: 'rejectedAt',
  rejectionReason: 'rejectionReason',
  responseData: 'responseData',
  region: 'region',
  legalDeadline: 'legalDeadline',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  eventType: 'eventType',
  action: 'action',
  resource: 'resource',
  resourceId: 'resourceId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  region: 'region',
  oldValues: 'oldValues',
  newValues: 'newValues',
  metadata: 'metadata',
  timestamp: 'timestamp'
};

exports.Prisma.DataBreachScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  severity: 'severity',
  status: 'status',
  affectedDataTypes: 'affectedDataTypes',
  affectedUserCount: 'affectedUserCount',
  affectedRegions: 'affectedRegions',
  detectedAt: 'detectedAt',
  containedAt: 'containedAt',
  resolvedAt: 'resolvedAt',
  requiresNotification: 'requiresNotification',
  notificationSent: 'notificationSent',
  notificationSentAt: 'notificationSentAt',
  reportedToAuthorities: 'reportedToAuthorities',
  reportedAt: 'reportedAt',
  authorityReference: 'authorityReference',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplianceStatusScalarFieldEnum = {
  id: 'id',
  tenantId: 'tenantId',
  region: 'region',
  law: 'law',
  isCompliant: 'isCompliant',
  lastAssessment: 'lastAssessment',
  nextAssessment: 'nextAssessment',
  complianceScore: 'complianceScore',
  findings: 'findings',
  remediationPlan: 'remediationPlan',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  permissions: 'permissions',
  isSystem: 'isSystem',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  roleId: 'roleId',
  tenantId: 'tenantId',
  grantedAt: 'grantedAt',
  grantedBy: 'grantedBy',
  expiresAt: 'expiresAt'
};

exports.Prisma.ApiKeyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  keyHash: 'keyHash',
  permissions: 'permissions',
  isActive: 'isActive',
  lastUsedAt: 'lastUsedAt',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  Tenant: 'Tenant',
  TenantPrivacyConfig: 'TenantPrivacyConfig',
  User: 'User',
  Consent: 'Consent',
  DataProcessingActivity: 'DataProcessingActivity',
  PrivacyRequest: 'PrivacyRequest',
  AuditLog: 'AuditLog',
  DataBreach: 'DataBreach',
  ComplianceStatus: 'ComplianceStatus',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  Role: 'Role',
  UserRole: 'UserRole',
  ApiKey: 'ApiKey'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin permissions
    if (!session.user.roles.includes('admin') && !session.user.roles.includes('super_admin')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    // Get tenant ID from session
    const tenantId = session.user.tenantId;

    // Fetch statistics
    const [
      totalUsers,
      activeConsents,
      pendingRequests,
      recentAuditLogs,
      tenant
    ] = await Promise.all([
      prisma.user.count({
        where: { tenantId, isActive: true }
      }),
      prisma.consent.count({
        where: {
          user: { tenantId },
          granted: true
        }
      }),
      prisma.privacyRequest.count({
        where: {
          user: { tenantId },
          status: { in: ['PENDING', 'IN_PROGRESS'] }
        }
      }),
      prisma.auditLog.count({
        where: {
          user: { tenantId },
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      }),
      prisma.tenant.findUnique({
        where: { id: tenantId },
        select: { regions: true }
      })
    ]);

    // Additional statistics
    const usersByRegion = await prisma.user.groupBy({
      by: ['preferredRegion'],
      where: { tenantId, isActive: true },
      _count: { id: true }
    });

    const consentsByCategory = await prisma.consent.groupBy({
      by: ['dataCategory'],
      where: {
        user: { tenantId },
        granted: true
      },
      _count: { id: true }
    });

    const requestsByType = await prisma.privacyRequest.groupBy({
      by: ['requestType'],
      where: {
        user: { tenantId },
        requestedAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      _count: { id: true }
    });

    const stats = {
      totalUsers,
      activeConsents,
      pendingRequests,
      recentAuditLogs,
      activeRegions: tenant?.regions.length || 0,
      usersByRegion: usersByRegion.map(item => ({
        region: item.preferredRegion,
        count: item._count.id
      })),
      consentsByCategory: consentsByCategory.map(item => ({
        category: item.dataCategory,
        count: item._count.id
      })),
      requestsByType: requestsByType.map(item => ({
        type: item.requestType,
        count: item._count.id
      })),
      generatedAt: new Date().toISOString()
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Admin stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

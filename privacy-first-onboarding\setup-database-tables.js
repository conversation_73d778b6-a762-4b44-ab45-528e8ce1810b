const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function setupDatabaseTables() {
  console.log('🚀 Setting up Document Management Database Tables...\n');
  
  try {
    // Create document_templates table
    console.log('📄 Creating document_templates table...');
    const { error: templatesError } = await supabase.rpc('sql', {
      query: `
        CREATE TABLE IF NOT EXISTS document_templates (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id UUID NOT NULL,
          template_type TEXT NOT NULL CHECK (template_type IN ('privacy_policy', 'terms_of_service', 'cookie_policy', 'data_processing_agreement')),
          name TEXT NOT NULL,
          description TEXT,
          template_content JSONB NOT NULL DEFAULT '{}',
          markdown_template TEXT NOT NULL,
          compliance_frameworks TEXT[] NOT NULL DEFAULT '{}',
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          UNIQUE(tenant_id, name, template_type)
        );
      `
    });
    
    if (templatesError) {
      console.log('⚠️ Templates table might already exist or need manual creation');
    } else {
      console.log('✅ document_templates table ready');
    }

    // Create document_versions table
    console.log('📄 Creating document_versions table...');
    const { error: versionsError } = await supabase.rpc('sql', {
      query: `
        CREATE TABLE IF NOT EXISTS document_versions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id UUID NOT NULL,
          template_id UUID NOT NULL,
          version_number TEXT NOT NULL,
          version_type TEXT NOT NULL CHECK (version_type IN ('major', 'minor', 'patch')),
          markdown_content TEXT NOT NULL,
          generated_data JSONB NOT NULL DEFAULT '{}',
          pdf_url TEXT,
          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
          changelog TEXT,
          published_at TIMESTAMP WITH TIME ZONE,
          created_by UUID,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          UNIQUE(tenant_id, template_id, version_number)
        );
      `
    });
    
    if (versionsError) {
      console.log('⚠️ Versions table might already exist or need manual creation');
    } else {
      console.log('✅ document_versions table ready');
    }

    // Insert sample template
    console.log('📄 Creating sample template...');
    const tenantId = '550e8400-e29b-41d4-a716-************';
    
    const { data: template, error: insertError } = await supabase
      .from('document_templates')
      .insert([{
        tenant_id: tenantId,
        template_type: 'privacy_policy',
        name: 'Standard Privacy Policy',
        description: 'GDPR and CCPA compliant privacy policy template',
        template_content: {
          company_name: '{{company_name}}',
          contact_email: '{{contact_email}}'
        },
        markdown_template: `# Privacy Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Information We Collect
We collect personal identifiers, contact information, and usage data to provide our services.

## 2. How We Use Your Information
We use your information to:
- Provide and improve our services
- Communicate with you about your account
- Ensure compliance with legal obligations

## 3. Your Rights
Under applicable privacy laws, you have the right to:
- Access your personal data
- Correct inaccurate information
- Delete your data
- Object to processing

## 4. Contact Information
For privacy questions, contact us at: {{contact_email}}

## 5. Updates to This Policy
We may update this policy from time to time. We will notify you of any material changes.`,
        compliance_frameworks: ['GDPR', 'CCPA']
      }])
      .select()
      .single();
    
    if (insertError) {
      if (insertError.code === '23505') {
        console.log('✅ Sample template already exists');
      } else {
        console.log('⚠️ Could not create sample template:', insertError.message);
      }
    } else {
      console.log('✅ Sample template created:', template.id);
    }

    console.log('\n🎉 Database setup complete!');
    console.log('\n📋 Next steps:');
    console.log('   1. Run: node test-document-management-system.js');
    console.log('   2. Visit: http://localhost:3000/documents');
    console.log('   3. Start creating documents!');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('\n📋 Manual setup required:');
    console.log('   1. Go to your Supabase Dashboard');
    console.log('   2. Navigate to SQL Editor');
    console.log('   3. Run the SQL migrations manually');
    console.log('   4. Check the migration files in supabase/migrations/');
  }
}

setupDatabaseTables().catch(console.error);

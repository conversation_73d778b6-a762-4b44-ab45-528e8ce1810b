'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Save, Eye, FileText, Code, Settings } from 'lucide-react';
import { DocumentTemplate, DocumentType, TemplateCreationRequest } from '@/lib/document-types';
import { DocumentManagementService } from '@/lib/document-service';

interface TemplateEditorProps {
  template?: DocumentTemplate | null;
  tenantId: string;
  onSave: () => void;
  onCancel: () => void;
}

const DOCUMENT_TYPES: { value: DocumentType; label: string; description: string }[] = [
  {
    value: 'privacy_policy',
    label: 'Privacy Policy',
    description: 'GDPR and CCPA compliant privacy policy'
  },
  {
    value: 'terms_of_service',
    label: 'Terms of Service',
    description: 'Standard terms and conditions'
  },
  {
    value: 'cookie_policy',
    label: 'Cookie Policy',
    description: 'Cookie usage and consent policy'
  },
  {
    value: 'data_processing_agreement',
    label: 'Data Processing Agreement',
    description: 'GDPR Article 28 DPA template'
  }
];

const COMPLIANCE_FRAMEWORKS = [
  'GDPR',
  'CCPA',
  'CPRA',
  'PIPEDA',
  'LGPD',
  'ePrivacy',
  'HIPAA',
  'SOX',
  'PCI DSS'
];

const DEFAULT_TEMPLATES = {
  privacy_policy: `# Privacy Policy for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Information We Collect

We collect the following types of personal information:
- Personal identifiers (name, email address)
- Contact information
- Behavioral data and usage analytics

## 2. How We Use Your Information

We use your personal information for:
- Providing and improving our services
- Communication and customer support
- Legal compliance and fraud prevention

## 3. Data Retention

We retain your personal information for {{retention_period}} or as required by law.

## 4. Your Rights

Under applicable privacy laws, you have the right to:
- Access your personal data
- Correct inaccurate data
- Delete your data
- Data portability
- Opt-out of data processing

## 5. Contact Information

For privacy-related questions, contact us at: {{contact_email}}`,

  terms_of_service: `# Terms of Service for {{company_name}}

**Last Updated:** {{current_date}}

## 1. Acceptance of Terms

By using {{service_name}}, you agree to these Terms of Service.

## 2. Description of Service

{{service_name}} provides {{service_description}}.

## 3. User Responsibilities

You are responsible for:
- Providing accurate information
- Maintaining account security
- Complying with applicable laws

## 4. Prohibited Uses

You may not use our service for illegal activities or unauthorized access.

## 5. Termination

We may terminate your account for violation of these terms.

## 6. Contact Information

Questions about these terms? Contact: {{contact_email}}`,

  cookie_policy: `# Cookie Policy for {{company_name}}

**Last Updated:** {{current_date}}

## What Are Cookies

Cookies are small text files stored on your device when you visit {{website_url}}.

## Types of Cookies We Use

### Essential Cookies
Required for basic website functionality.

### Analytics Cookies
Help us understand how visitors use our website.

### Marketing Cookies
Used to deliver relevant advertisements.

## Managing Cookies

You can control cookies through your browser settings.

## Contact Us

Questions about our cookie policy? Contact: {{contact_email}}`
};

export function TemplateEditor({ template, tenantId, onSave, onCancel }: TemplateEditorProps) {
  // Early return if tenantId is not provided
  if (!tenantId || tenantId.trim() === '') {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Invalid Configuration</h2>
          <p className="text-muted-foreground mb-4">Tenant ID is required to use the template editor.</p>
          <Button onClick={onCancel}>Go Back</Button>
        </div>
      </div>
    );
  }

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    template_type: 'privacy_policy' as DocumentType,
    markdown_template: '',
    template_content: {},
    compliance_frameworks: [] as string[]
  });
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [previewContent, setPreviewContent] = useState('');

  const documentService = new DocumentManagementService(tenantId);

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        description: template.description || '',
        template_type: template.template_type,
        markdown_template: template.markdown_template,
        template_content: template.template_content,
        compliance_frameworks: template.compliance_frameworks
      });
    } else {
      // Set default template for new documents
      setFormData(prev => ({
        ...prev,
        markdown_template: DEFAULT_TEMPLATES[prev.template_type] || ''
      }));
    }
  }, [template]);

  useEffect(() => {
    // Update default template when type changes
    if (!template && formData.template_type) {
      setFormData(prev => ({
        ...prev,
        markdown_template: DEFAULT_TEMPLATES[prev.template_type] || ''
      }));
    }
  }, [formData.template_type, template]);

  const handleSave = async () => {
    setSaving(true);
    try {
      const request: TemplateCreationRequest = {
        template_type: formData.template_type,
        name: formData.name,
        description: formData.description,
        markdown_template: formData.markdown_template,
        template_content: formData.template_content,
        compliance_frameworks: formData.compliance_frameworks
      };

      let response;
      if (template) {
        response = await documentService.updateTemplate(template.id, request);
      } else {
        response = await documentService.createTemplate(request);
      }

      if (response.success) {
        onSave();
      } else {
        console.error('Error saving template:', response.error);
      }
    } catch (error) {
      console.error('Error saving template:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleComplianceFrameworkToggle = (framework: string) => {
    setFormData(prev => ({
      ...prev,
      compliance_frameworks: prev.compliance_frameworks.includes(framework)
        ? prev.compliance_frameworks.filter(f => f !== framework)
        : [...prev.compliance_frameworks, framework]
    }));
  };

  const generatePreview = () => {
    let preview = formData.markdown_template;
    
    // Replace common template variables with sample data
    const sampleData = {
      company_name: 'Sample Company Inc.',
      contact_email: '<EMAIL>',
      website_url: 'https://samplecompany.com',
      service_name: 'Sample Service',
      service_description: 'privacy-first user onboarding and document management',
      retention_period: '2 years',
      current_date: new Date().toLocaleDateString(),
      current_year: new Date().getFullYear().toString()
    };

    Object.entries(sampleData).forEach(([key, value]) => {
      preview = preview.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });

    setPreviewContent(preview);
  };

  useEffect(() => {
    generatePreview();
  }, [formData.markdown_template]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {template ? 'Edit Template' : 'Create Template'}
            </h1>
            <p className="text-muted-foreground">
              {template ? 'Modify existing template' : 'Create a new document template'}
            </p>
          </div>
        </div>
        <Button onClick={handleSave} disabled={saving || !formData.name.trim()}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Template'}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Information</CardTitle>
              <CardDescription>
                Basic information about your document template
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Template Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Standard Privacy Policy"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Document Type</Label>
                  <Select
                    value={formData.template_type}
                    onValueChange={(value: DocumentType) => 
                      setFormData(prev => ({ ...prev, template_type: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {DOCUMENT_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div>
                            <div className="font-medium">{type.label}</div>
                            <div className="text-sm text-muted-foreground">{type.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what this template is used for..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Content</CardTitle>
              <CardDescription>
                Write your document template using Markdown. Use {{variable_name}} for dynamic content.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="markdown">Markdown Template</Label>
                <Textarea
                  id="markdown"
                  value={formData.markdown_template}
                  onChange={(e) => setFormData(prev => ({ ...prev, markdown_template: e.target.value }))}
                  placeholder="Write your template content here..."
                  rows={20}
                  className="font-mono text-sm"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Preview</CardTitle>
              <CardDescription>
                Preview how your template will look with sample data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <pre className="whitespace-pre-wrap text-sm">{previewContent}</pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Frameworks</CardTitle>
              <CardDescription>
                Select the compliance frameworks this template addresses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {COMPLIANCE_FRAMEWORKS.map((framework) => (
                  <div key={framework} className="flex items-center space-x-2">
                    <Checkbox
                      id={framework}
                      checked={formData.compliance_frameworks.includes(framework)}
                      onCheckedChange={() => handleComplianceFrameworkToggle(framework)}
                    />
                    <Label htmlFor={framework}>{framework}</Label>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Label>Selected Frameworks:</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.compliance_frameworks.map((framework) => (
                    <Badge key={framework} variant="secondary">
                      {framework}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

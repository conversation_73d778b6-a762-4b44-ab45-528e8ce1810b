// Consent Tracking API
// Track user consent for document versions

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';
import { ConsentTrackingRequest } from '@/lib/document-types';

// POST /api/consent/track - Track user consent
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // Get IP address from request headers
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
    
    const consentRequest: ConsentTrackingRequest = {
      document_version_id: body.document_version_id,
      end_user_identifier: body.end_user_identifier,
      end_user_email: body.end_user_email,
      consent_given: body.consent_given,
      consent_method: body.consent_method || 'api_integration',
      ip_address: ip,
      user_agent: request.headers.get('user-agent') || 'unknown'
    };

    // Validate required fields
    if (!consentRequest.document_version_id || !consentRequest.end_user_identifier) {
      return NextResponse.json(
        { error: 'document_version_id and end_user_identifier are required' },
        { status: 400 }
      );
    }

    if (typeof consentRequest.consent_given !== 'boolean') {
      return NextResponse.json(
        { error: 'consent_given must be a boolean value' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.trackConsent(consentRequest);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: 'Consent tracked successfully'
      }, { status: 201 });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in POST /api/consent/track:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/consent/track - Get consent history for user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const userEmail = searchParams.get('userEmail');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'userEmail is required' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.exportUserData(userEmail);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in GET /api/consent/track:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

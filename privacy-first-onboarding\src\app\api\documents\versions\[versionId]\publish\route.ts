// Version Publishing API
// Publish document versions and trigger notifications

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';
import { VersionPublishingRequest } from '@/lib/document-types';

interface RouteParams {
  params: {
    versionId: string;
  };
}

// POST /api/documents/versions/[versionId]/publish - Publish a version
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const publishRequest: VersionPublishingRequest = {
      version_id: params.versionId,
      notify_existing_users: body.notify_existing_users || false,
      notification_message: body.notification_message
    };

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.publishVersion(publishRequest);

    if (response.success) {
      return NextResponse.json({
        success: true,
        data: response.data,
        message: 'Version published successfully'
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in POST /api/documents/versions/[versionId]/publish:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

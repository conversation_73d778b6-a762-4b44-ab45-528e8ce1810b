// GDPR Data Deletion API
// Delete user data for GDPR compliance (Right to be Forgotten)

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';

// DELETE /api/gdpr/delete - Delete user data
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const userEmail = searchParams.get('userEmail');
    const confirmDeletion = searchParams.get('confirm');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'userEmail is required' },
        { status: 400 }
      );
    }

    if (confirmDeletion !== 'true') {
      return NextResponse.json(
        { error: 'Deletion must be confirmed with confirm=true parameter' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.deleteUserData(userEmail);

    if (response.success) {
      return NextResponse.json({
        success: true,
        message: 'User data deleted successfully',
        data: response.data
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in DELETE /api/gdpr/delete:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/gdpr/delete - Request data deletion (with verification)
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { userEmail, reason, verificationCode } = body;

    if (!userEmail) {
      return NextResponse.json(
        { error: 'userEmail is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // In a real implementation, you would:
    // 1. Verify the verification code sent to the user's email
    // 2. Check if the user has the right to request deletion
    // 3. Queue the deletion for processing after a grace period

    if (!verificationCode) {
      // Send verification code to user's email
      return NextResponse.json({
        success: true,
        message: 'Verification code sent to email',
        requiresVerification: true,
        requestId: `delete-${Date.now()}`
      });
    }

    // For demo purposes, accept any verification code
    // In production, verify the code against what was sent
    if (verificationCode !== 'DEMO123') {
      return NextResponse.json(
        { error: 'Invalid verification code' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.deleteUserData(userEmail);

    if (response.success) {
      return NextResponse.json({
        success: true,
        message: 'Data deletion request processed successfully',
        data: response.data,
        deletionReason: reason || 'User requested deletion'
      });
    } else {
      return NextResponse.json(
        { error: response.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in POST /api/gdpr/delete:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

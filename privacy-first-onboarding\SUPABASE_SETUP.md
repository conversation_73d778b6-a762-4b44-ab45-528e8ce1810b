# 🗄️ Supabase Database Setup Guide

This guide will help you set up Supabase as the real database for your Privacy-First User Onboarding Application.

## 📋 Prerequisites

- Supabase account (free tier available)
- Node.js 18+ installed
- Git repository access

## 🚀 Step-by-Step Setup

### 1. Create Supabase Project

1. **Sign up/Login** to [Supabase](https://supabase.com)
2. **Create a new project**:
   - Choose a project name: `privacy-first-app`
   - Set a strong database password
   - Select a region close to your users (important for privacy compliance)
   - Wait for the project to be created (2-3 minutes)

### 2. Get Your Credentials

1. Go to **Project Settings** → **API**
2. Copy the following values:
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **anon public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **service_role key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

3. Go to **Project Settings** → **Database**
4. Copy the **Connection string** (URI format)

### 3. Update Environment Variables

Update your `.env.local` file:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key-here"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key-here"

# Database URL for Prisma
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
DIRECT_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
```

**Important**: Replace the placeholders with your actual values!

### 4. Run Database Migrations

#### Option A: Using Supabase SQL Editor (Recommended)

1. Go to **SQL Editor** in your Supabase dashboard
2. Create a new query
3. Copy and paste the content from `supabase/migrations/001_initial_schema.sql`
4. Click **Run** to execute the migration
5. Create another query with content from `supabase/seed.sql`
6. Click **Run** to seed the database
7. Create a final query with content from `supabase/rls_policies.sql`
8. Click **Run** to set up Row Level Security

#### Option B: Using Prisma (Alternative)

```bash
# Generate Prisma client
npm run db:generate

# Push schema to Supabase
npm run db:push

# Seed the database
npm run db:seed
```

### 5. Set Up Row Level Security (RLS)

1. Go to **Authentication** → **Policies** in Supabase dashboard
2. Verify that RLS policies are enabled for all tables
3. Check that the policies from `supabase/rls_policies.sql` are applied

### 6. Configure Authentication (Optional)

If you want to use Supabase Auth instead of NextAuth:

1. Go to **Authentication** → **Settings**
2. Configure your site URL: `http://localhost:3000`
3. Add redirect URLs for production
4. Enable email confirmations if desired

### 7. Test the Setup

1. **Start your application**:
   ```bash
   npm run dev
   ```

2. **Check the console** for "Using service: Supabase" message

3. **Test the onboarding flow**:
   - Go to `http://localhost:3000`
   - Complete the onboarding process
   - Verify data is saved in Supabase

4. **Test the dashboard**:
   - Go to `http://localhost:3000/dashboard`
   - Use the generated User ID or try the demo account
   - Verify data loads from Supabase

### 8. Verify Database Tables

In Supabase dashboard, go to **Table Editor** and verify these tables exist:

- ✅ `tenants`
- ✅ `tenant_privacy_configs`
- ✅ `users`
- ✅ `roles`
- ✅ `user_roles`
- ✅ `consents`
- ✅ `data_processing_activities`
- ✅ `privacy_requests`
- ✅ `audit_logs`
- ✅ `data_breaches`
- ✅ `compliance_status`
- ✅ `api_keys`

## 🌍 Regional Compliance Setup

For production deployments requiring data residency compliance:

### Multi-Region Setup

1. **Create separate Supabase projects** for each region:
   - US: `privacy-first-app-us`
   - EU: `privacy-first-app-eu`
   - UK: `privacy-first-app-uk`
   - etc.

2. **Add regional environment variables**:
   ```bash
   # US Region
   NEXT_PUBLIC_SUPABASE_URL_US="https://us-project.supabase.co"
   NEXT_PUBLIC_SUPABASE_ANON_KEY_US="us-anon-key"
   
   # EU Region
   NEXT_PUBLIC_SUPABASE_URL_EU="https://eu-project.supabase.co"
   NEXT_PUBLIC_SUPABASE_ANON_KEY_EU="eu-anon-key"
   ```

3. **Deploy the same schema** to each regional database

4. **Configure routing** based on user's preferred region

## 🔧 Troubleshooting

### Common Issues

1. **"Unable to load privacy profile"**
   - Check if Supabase credentials are correct
   - Verify database tables exist
   - Check browser console for errors

2. **RLS Policy Errors**
   - Ensure RLS policies are properly applied
   - Check if user has proper permissions
   - Verify authentication is working

3. **Connection Issues**
   - Verify DATABASE_URL format is correct
   - Check if Supabase project is active
   - Ensure network connectivity

### Debug Mode

Add this to your `.env.local` for debugging:

```bash
# Enable debug logging
LOG_LEVEL="debug"
ENABLE_DATABASE_LOGGING="true"
```

## 📊 Monitoring & Analytics

### Supabase Dashboard

Monitor your application through:

1. **Database** → View tables and data
2. **Auth** → Monitor user registrations
3. **Logs** → Check for errors and performance
4. **API** → Monitor API usage

### Privacy Compliance Monitoring

The application includes built-in compliance monitoring:

1. **Compliance Dashboard** at `/admin`
2. **Audit Logs** for all data operations
3. **Privacy Request Tracking**
4. **Data Retention Monitoring**

## 🔒 Security Best Practices

1. **Never expose service_role key** in client-side code
2. **Use RLS policies** to protect user data
3. **Enable MFA** on your Supabase account
4. **Regularly rotate** API keys
5. **Monitor access logs** for suspicious activity
6. **Use HTTPS** in production
7. **Implement rate limiting** for API endpoints

## 🚀 Production Deployment

### Environment Setup

1. **Set production environment variables**
2. **Configure custom domain** in Supabase
3. **Set up SSL certificates**
4. **Configure backup policies**
5. **Set up monitoring alerts**

### Scaling Considerations

1. **Database connection pooling**
2. **Read replicas** for high-traffic regions
3. **CDN setup** for static assets
4. **Load balancing** for multiple regions

## 📞 Support

If you encounter issues:

1. **Check Supabase documentation**: [docs.supabase.com](https://docs.supabase.com)
2. **Review application logs** in browser console
3. **Check Supabase project logs** in dashboard
4. **Verify environment variables** are set correctly

---

**🎉 Congratulations!** Your Privacy-First Application is now powered by Supabase with full regional compliance support!

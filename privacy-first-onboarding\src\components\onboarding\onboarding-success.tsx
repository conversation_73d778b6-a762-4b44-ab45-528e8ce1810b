'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Shield, Download, Settings, ArrowRight } from 'lucide-react';
import { privacyService } from '@/lib/privacy-service';
import type { UserPrivacyProfile, PrivacyConsent } from '@/lib/privacy-types';

interface OnboardingSuccessProps {
  userId: string;
}

export function OnboardingSuccess({ userId }: OnboardingSuccessProps) {
  const [userProfile, setUserProfile] = useState<UserPrivacyProfile | null>(null);
  const [consents, setConsents] = useState<PrivacyConsent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        const dashboardData = await privacyService.getPrivacyDashboardData(userId);
        setUserProfile(dashboardData.profile);
        setConsents(dashboardData.consents);
      } catch (error) {
        console.error('Failed to load user data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, [userId]);

  const handleDownloadData = async () => {
    try {
      const userData = await privacyService.exportUserData(userId);
      const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `privacy-data-${userId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download data:', error);
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your privacy profile...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-4 bg-green-100 rounded-full">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
        </div>
        <CardTitle className="text-3xl text-green-600">Welcome!</CardTitle>
        <CardDescription className="text-lg">
          Your privacy-first account has been created successfully
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-8">
        {/* Account Summary */}
        {userProfile && (
          <div className="bg-green-50 p-6 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="h-5 w-5 text-green-600" />
              <h3 className="text-lg font-semibold text-green-900">Your Privacy Profile</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-green-800">User ID:</span>
                <span className="ml-2 text-green-700 font-mono">{userId}</span>
              </div>
              <div>
                <span className="font-medium text-green-800">Data Region:</span>
                <span className="ml-2 text-green-700">{userProfile.preferredRegion}</span>
              </div>
              <div>
                <span className="font-medium text-green-800">Data Residency:</span>
                <span className="ml-2 text-green-700">
                  {userProfile.dataResidencyPreference.replace('_', ' ').toLowerCase()}
                </span>
              </div>
              <div>
                <span className="font-medium text-green-800">Active Consents:</span>
                <span className="ml-2 text-green-700">
                  {consents.filter(c => c.granted).length} granted
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Consent Summary */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Your Privacy Choices</h3>
          <div className="grid gap-3">
            {consents.map((consent) => (
              <div key={consent.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="font-medium">{consent.purpose}</div>
                  <div className="text-sm text-gray-600">{consent.dataCategory.replace('_', ' ')}</div>
                </div>
                <Badge variant={consent.granted ? "default" : "secondary"}>
                  {consent.granted ? 'Granted' : 'Declined'}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Next Steps */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">What's Next?</h3>
          <div className="grid gap-4">
            <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50">
              <Settings className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium">Manage Your Privacy Settings</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Update your consent preferences, data retention settings, and privacy controls anytime.
                </p>
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400" />
            </div>

            <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50">
              <Download className="h-5 w-5 text-purple-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium">Download Your Data</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Exercise your right to data portability by downloading all your personal data.
                </p>
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400" />
            </div>

            <div className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50">
              <Shield className="h-5 w-5 text-green-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium">Privacy Dashboard</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Monitor how your data is being used and exercise your privacy rights.
                </p>
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Your Rights Reminder */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">Remember Your Rights</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-blue-700">
            <div>✓ Access your personal data</div>
            <div>✓ Correct inaccurate information</div>
            <div>✓ Delete your account and data</div>
            <div>✓ Export your data</div>
            <div>✓ Withdraw consent anytime</div>
            <div>✓ Object to data processing</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Button onClick={handleDownloadData} variant="outline" className="flex-1">
            <Download className="h-4 w-4 mr-2" />
            Download My Data
          </Button>
          <Button 
            onClick={() => window.location.href = '/dashboard'} 
            className="flex-1"
          >
            <Settings className="h-4 w-4 mr-2" />
            Go to Privacy Dashboard
          </Button>
        </div>

        {/* Support Information */}
        <div className="text-center text-sm text-gray-500 pt-4 border-t">
          <p>
            Questions about your privacy? Contact our Data Protection Officer at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to switch from Mock Data to Supabase
 * Run this after setting up your Supabase database
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Switching from Mock Data to Supabase...\n');

// Files to update
const filesToUpdate = [
  {
    path: 'src/components/dashboard/privacy-dashboard.tsx',
    description: 'Privacy Dashboard - Service Selection Logic'
  },
  {
    path: 'src/components/supabase-status.tsx',
    description: 'Supabase Status Component'
  }
];

// Backup original files
function backupFile(filePath) {
  const backupPath = filePath + '.backup';
  if (fs.existsSync(filePath) && !fs.existsSync(backupPath)) {
    fs.copyFileSync(filePath, backupPath);
    console.log(`✅ Backed up: ${filePath}`);
  }
}

// Update privacy dashboard to use Supabase
function updatePrivacyDashboard() {
  const filePath = 'src/components/dashboard/privacy-dashboard.tsx';
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace the mock service logic with Supabase logic
    const oldLogic = `      // Always use mock service for now until Supabase database is properly set up
      console.log('Loading dashboard data using mock service for userId:', userId);
      let data = await privacyService.getPrivacyDashboardData(userId);
      console.log('Mock data loaded:', data);

      // If no profile was found, the service should have created a demo profile
      if (!data.profile) {
        console.log('No profile found, this should not happen with the updated mock service');
      }`;

    const newLogic = `      // Try Supabase first if configured, fallback to mock service
      let data;
      const isSupabaseConfigured = supabaseUtils.isConfigured();
      console.log('Using service:', isSupabaseConfigured ? 'Supabase' : 'Mock');

      if (isSupabaseConfigured) {
        try {
          data = await supabasePrivacyService.getPrivacyDashboardData(userId);
          console.log('Supabase data loaded:', data);
        } catch (supabaseError) {
          console.log('Supabase failed, falling back to mock service:', supabaseError);
          data = await privacyService.getPrivacyDashboardData(userId);
          console.log('Mock data loaded:', data);
        }
      } else {
        data = await privacyService.getPrivacyDashboardData(userId);
        console.log('Mock data loaded:', data);
      }`;

    if (content.includes(oldLogic)) {
      content = content.replace(oldLogic, newLogic);
      fs.writeFileSync(filePath, content);
      console.log('✅ Updated: Privacy Dashboard service logic');
    } else {
      console.log('⚠️  Privacy Dashboard already updated or pattern not found');
    }
  } catch (error) {
    console.error('❌ Error updating Privacy Dashboard:', error.message);
  }
}

// Update Supabase status component
function updateSupabaseStatus() {
  const filePath = 'src/components/supabase-status.tsx';
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Remove the forced mock data mode
    content = content.replace(
      'const usingMockData = true; // For now, always using mock data',
      'const usingMockData = false; // Now using real Supabase data'
    );
    
    content = content.replace(
      'if (isConfigured && !usingMockData) {',
      'if (isConfigured) {'
    );

    // Update the status badge
    const oldBadgeLogic = `export function SupabaseStatusBadge() {
  const isConfigured = supabaseUtils.isConfigured();
  const usingMockData = true; // For now, always using mock data

  return (
    <Badge 
      variant="outline" 
      className="bg-amber-100 text-amber-800 border-amber-300"
    >
      <XCircle className="h-3 w-3 mr-1" />
      Mock Data
    </Badge>
  );
}`;

    const newBadgeLogic = `export function SupabaseStatusBadge() {
  const isConfigured = supabaseUtils.isConfigured();

  return (
    <Badge 
      variant="outline" 
      className={
        isConfigured 
          ? "bg-green-100 text-green-800 border-green-300" 
          : "bg-amber-100 text-amber-800 border-amber-300"
      }
    >
      {isConfigured ? (
        <>
          <CheckCircle className="h-3 w-3 mr-1" />
          Supabase
        </>
      ) : (
        <>
          <XCircle className="h-3 w-3 mr-1" />
          Mock Data
        </>
      )}
    </Badge>
  );
}`;

    if (content.includes('const usingMockData = true')) {
      content = content.replace(oldBadgeLogic, newBadgeLogic);
      fs.writeFileSync(filePath, content);
      console.log('✅ Updated: Supabase Status component');
    } else {
      console.log('⚠️  Supabase Status already updated or pattern not found');
    }
  } catch (error) {
    console.error('❌ Error updating Supabase Status:', error.message);
  }
}

// Main execution
function main() {
  console.log('📋 Files to update:');
  filesToUpdate.forEach((file, index) => {
    console.log(`${index + 1}. ${file.path} - ${file.description}`);
  });
  console.log('');

  // Create backups
  console.log('💾 Creating backups...');
  filesToUpdate.forEach(file => backupFile(file.path));
  console.log('');

  // Update files
  console.log('🔧 Updating files...');
  updatePrivacyDashboard();
  updateSupabaseStatus();
  console.log('');

  console.log('✅ Switch to Supabase completed!');
  console.log('');
  console.log('📝 Next steps:');
  console.log('1. Make sure your Supabase database is set up');
  console.log('2. Update your .env.local with the correct DATABASE_URL');
  console.log('3. Restart your development server: npm run dev');
  console.log('4. Test the application with real data persistence');
  console.log('');
  console.log('🔄 To revert changes, restore from .backup files');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };

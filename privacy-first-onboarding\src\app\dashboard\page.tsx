'use client';

import { useState, useEffect } from 'react';
import { PrivacyDashboard } from '@/components/dashboard/privacy-dashboard';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle } from 'lucide-react';

export default function DashboardPage() {
  const [userId, setUserId] = useState<string>('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState<string>('');

  // In a real application, you would get the userId from authentication context
  // For demo purposes, we'll allow manual entry
  useEffect(() => {
    // Check if userId is in URL params or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const userIdFromUrl = urlParams.get('userId');
    const userIdFromStorage = localStorage.getItem('demoUserId');
    
    if (userIdFromUrl) {
      setUserId(userIdFromUrl);
      setIsAuthenticated(true);
      localStorage.setItem('demoUserId', userIdFromUrl);
    } else if (userIdFromStorage) {
      setUserId(userIdFromStorage);
      setIsAuthenticated(true);
    }
  }, []);

  const handleLogin = () => {
    const currentUserId = userId.trim();
    if (!currentUserId) {
      setError('Please enter a valid User ID');
      return;
    }

    // Basic validation - in real app, this would be proper authentication
    if (currentUserId.startsWith('user_') || currentUserId === '550e8400-e29b-41d4-a716-************') {
      setIsAuthenticated(true);
      setError('');
      localStorage.setItem('demoUserId', currentUserId);
    } else {
      setError('Invalid User ID format. User ID should start with "user_" or use the demo UUID');
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setUserId('');
    localStorage.removeItem('demoUserId');
    window.location.href = '/';
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-8">
        <Card className="w-full max-w-md">
          <CardContent className="p-8">
            <div className="text-center mb-6">
              <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-4">
                <Shield className="h-8 w-8 text-blue-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Privacy Dashboard Access
              </h1>
              <p className="text-gray-600">
                Enter your User ID to access your privacy dashboard
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="userId">User ID</Label>
                <Input
                  id="userId"
                  type="text"
                  placeholder="user_1234567890_abcdef123"
                  value={userId}
                  onChange={(e) => setUserId(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
                />
              </div>

              {error && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button onClick={handleLogin} className="w-full">
                Access Dashboard
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  setUserId('550e8400-e29b-41d4-a716-************');
                  handleLogin();
                }}
                className="w-full"
              >
                Try Demo Account
              </Button>

              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">
                  Don't have an account?
                </p>
                <Button 
                  variant="outline" 
                  onClick={() => window.location.href = '/'}
                  className="w-full"
                >
                  Create Privacy-First Account
                </Button>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">Demo Instructions</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p>1. Complete the onboarding flow to get a User ID</p>
                <p>2. Use that User ID to access this dashboard</p>
                <p>3. Or use any ID starting with "user_" for demo purposes</p>
                <p>4. Try: <code className="bg-blue-100 px-1 rounded">550e8400-e29b-41d4-a716-************</code></p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div>
      {/* Header with logout */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-8 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-blue-600" />
            <span className="font-semibold text-gray-900">Privacy Dashboard</span>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              User: <code className="bg-gray-100 px-2 py-1 rounded text-xs">{userId}</code>
            </span>
            <Button variant="outline" size="sm" onClick={handleLogout}>
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Dashboard */}
      <PrivacyDashboard userId={userId} />
    </div>
  );
}

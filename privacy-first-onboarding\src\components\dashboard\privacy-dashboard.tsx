'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Shield, 
  Download, 
  Trash2, 
  Edit, 
  Eye, 
  Settings, 
  Globe, 
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { privacyService } from '@/lib/privacy-service';
import { supabasePrivacyService } from '@/lib/supabase-privacy-service';
import { supabaseUtils } from '@/lib/supabase';
import { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from '@/lib/utils';
import type { UserPrivacyProfile, PrivacyConsent } from '@/lib/privacy-types';

interface PrivacyDashboardProps {
  userId: string;
}

export function PrivacyDashboard({ userId }: PrivacyDashboardProps) {
  const [userProfile, setUserProfile] = useState<UserPrivacyProfile | null>(null);
  const [consents, setConsents] = useState<PrivacyConsent[]>([]);
  const [applicableLaws, setApplicableLaws] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();
  }, [userId]);

  const loadDashboardData = async () => {
    try {
      console.log('Loading dashboard data for user:', userId);

      // Try Supabase first if configured, fallback to mock service
      const service = supabaseUtils.isConfigured() ? supabasePrivacyService : privacyService;
      console.log('Using service:', supabaseUtils.isConfigured() ? 'Supabase' : 'Mock');

      // For demo purposes, if user doesn't exist, create a demo profile
      let data = await service.getPrivacyDashboardData(userId);
      console.log('Dashboard data loaded:', data);

      if (!data.profile) {
        // Create a demo user profile for testing
        const demoFormData = {
          firstName: 'Demo',
          lastName: 'User',
          email: '<EMAIL>',
          preferredRegion: 'US' as const,
          dataResidencyPreference: 'SAME_REGION' as const,
          consents: [
            {
              dataCategory: 'PERSONAL_IDENTIFIERS' as const,
              purpose: 'Account Creation and Management',
              granted: true,
              region: 'US' as const,
            },
            {
              dataCategory: 'CONTACT_INFO' as const,
              purpose: 'Service Communications',
              granted: true,
              region: 'US' as const,
            },
            {
              dataCategory: 'BEHAVIORAL_DATA' as const,
              purpose: 'Service Improvement and Analytics',
              granted: true,
              region: 'US' as const,
            },
            {
              dataCategory: 'CONTACT_INFO' as const,
              purpose: 'Marketing Communications',
              granted: false,
              region: 'US' as const,
            },
          ],
          communicationPreferences: {
            marketing: false,
            analytics: true,
            personalization: false,
            thirdPartySharing: false,
          },
          acceptTerms: true,
          acceptPrivacyPolicy: true,
          ageVerification: true,
        };

        // Register the demo user
        await privacyService.registerUser(demoFormData);

        // Load the data again
        data = await privacyService.getPrivacyDashboardData(userId);
      }

      setUserProfile(data.profile);
      setConsents(data.consents);
      setApplicableLaws(data.applicableLaws);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);

      // Create a fallback demo profile if all else fails
      const fallbackProfile = {
        userId: userId,
        preferredRegion: 'US' as const,
        dataResidencyPreference: 'SAME_REGION' as const,
        consentPreferences: [
          {
            id: 'demo-consent-1',
            userId: userId,
            dataCategory: 'PERSONAL_IDENTIFIERS' as const,
            purpose: 'Account Creation and Management',
            granted: true,
            timestamp: new Date(),
            region: 'US' as const,
            version: '1.0',
          },
          {
            id: 'demo-consent-2',
            userId: userId,
            dataCategory: 'CONTACT_INFO' as const,
            purpose: 'Marketing Communications',
            granted: false,
            timestamp: new Date(),
            region: 'US' as const,
            version: '1.0',
          },
        ],
        communicationPreferences: {
          marketing: false,
          analytics: true,
          personalization: false,
          thirdPartySharing: false,
        },
        rightsExercised: [],
      };

      setUserProfile(fallbackProfile);
      setConsents(fallbackProfile.consentPreferences);
      setApplicableLaws(['CCPA', 'CPRA']);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConsentToggle = async (consentId: string, granted: boolean) => {
    try {
      await privacyService.updateConsent(userId, consentId, granted);
      await loadDashboardData(); // Refresh data
    } catch (error) {
      console.error('Failed to update consent:', error);
    }
  };

  const handleExerciseRight = async (right: keyof typeof USER_RIGHTS) => {
    try {
      const requestId = await privacyService.exerciseUserRight(userId, right);
      alert(`Your ${USER_RIGHTS[right]} request has been submitted. Request ID: ${requestId}`);
      await loadDashboardData(); // Refresh data
    } catch (error) {
      console.error('Failed to exercise right:', error);
    }
  };

  const handleDownloadData = async () => {
    try {
      const userData = await privacyService.exportUserData(userId);
      const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `privacy-data-${userId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download data:', error);
    }
  };

  const handleDeleteAccount = async () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      try {
        await privacyService.deleteUserData(userId);
        alert('Your account and all associated data have been deleted.');
        window.location.href = '/';
      } catch (error) {
        console.error('Failed to delete account:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your privacy dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-6xl mx-auto">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Unable to load your privacy profile. This might be because:
              <ul className="mt-2 ml-4 list-disc text-sm">
                <li>The User ID doesn't exist in our system</li>
                <li>You need to complete the onboarding flow first</li>
                <li>There's a temporary system issue</li>
              </ul>
            </AlertDescription>
          </Alert>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2">Quick Solutions</h4>
            <div className="text-sm text-blue-700 space-y-2">
              <p>1. <strong>Go back to onboarding:</strong> <a href="/" className="underline">Complete the onboarding flow</a> to create a new account</p>
              <p>2. <strong>Try a demo account:</strong> Go back and click "Try Demo Account" button</p>
              <p>3. <strong>Use a valid User ID:</strong> Make sure your User ID starts with "user_"</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const grantedConsents = consents.filter(c => c.granted).length;
  const totalConsents = consents.length;
  const consentPercentage = totalConsents > 0 ? (grantedConsents / totalConsents) * 100 : 0;

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Privacy Dashboard</h1>
          <p className="text-lg text-gray-600">
            Manage your data, privacy preferences, and exercise your rights
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Globe className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Data Region</p>
                  <p className="font-semibold">{PRIVACY_REGIONS[userProfile.preferredRegion].name}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Shield className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Privacy Laws</p>
                  <p className="font-semibold">{applicableLaws.length} applicable</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Active Consents</p>
                  <p className="font-semibold">{grantedConsents} of {totalConsents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Clock className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Rights Exercised</p>
                  <p className="font-semibold">{userProfile.rightsExercised.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Dashboard */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="consents">Consent Management</TabsTrigger>
            <TabsTrigger value="rights">Your Rights</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Privacy Score */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Privacy Health Score
                </CardTitle>
                <CardDescription>
                  Your current privacy configuration and data protection status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Consent Management</span>
                    <span>{Math.round(consentPercentage)}%</span>
                  </div>
                  <Progress value={consentPercentage} className="h-2" />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Data Protection</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span>Data encrypted at rest and in transit</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span>Regional data residency enforced</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span>Regular security audits performed</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">Compliance Status</h4>
                    <div className="space-y-1">
                      {applicableLaws.map((law) => (
                        <Badge key={law} variant="outline" className="mr-1">
                          {law} Compliant
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Privacy Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {userProfile.rightsExercised.slice(0, 5).map((activity, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="p-1 bg-blue-100 rounded">
                          <Settings className="h-3 w-3 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{USER_RIGHTS[activity.right]}</p>
                          <p className="text-sm text-gray-600">
                            {activity.requestedAt.toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <Badge variant={
                        activity.status === 'COMPLETED' ? 'default' :
                        activity.status === 'IN_PROGRESS' ? 'secondary' :
                        activity.status === 'REJECTED' ? 'destructive' : 'outline'
                      }>
                        {activity.status}
                      </Badge>
                    </div>
                  ))}
                  
                  {userProfile.rightsExercised.length === 0 && (
                    <p className="text-center text-gray-500 py-4">
                      No privacy rights have been exercised yet.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="consents" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Manage Your Consent Preferences</CardTitle>
                <CardDescription>
                  Control how your data is used by toggling consent for different purposes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {consents.map((consent) => (
                  <div key={consent.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{consent.purpose}</h4>
                      <p className="text-sm text-gray-600">
                        {DATA_CATEGORIES[consent.dataCategory]} • 
                        Last updated: {consent.timestamp.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-3">
                      <Switch
                        checked={consent.granted}
                        onCheckedChange={(checked) => handleConsentToggle(consent.id, checked)}
                      />
                      {consent.granted ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rights" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Exercise Your Privacy Rights</CardTitle>
                <CardDescription>
                  These rights are guaranteed under {applicableLaws.join(', ')} and other applicable privacy laws
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Eye className="h-5 w-5 text-blue-600" />
                      <div>
                        <h4 className="font-medium">Access Your Data</h4>
                        <p className="text-sm text-gray-600">Download a copy of all your personal data</p>
                      </div>
                    </div>
                    <Button onClick={handleDownloadData} variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Edit className="h-5 w-5 text-green-600" />
                      <div>
                        <h4 className="font-medium">Correct Your Data</h4>
                        <p className="text-sm text-gray-600">Request correction of inaccurate information</p>
                      </div>
                    </div>
                    <Button 
                      onClick={() => handleExerciseRight('CORRECTION')} 
                      variant="outline"
                    >
                      Request Correction
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Trash2 className="h-5 w-5 text-red-600" />
                      <div>
                        <h4 className="font-medium">Delete Your Account</h4>
                        <p className="text-sm text-gray-600">Permanently delete your account and all data</p>
                      </div>
                    </div>
                    <Button onClick={handleDeleteAccount} variant="destructive">
                      Delete Account
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Privacy Settings</CardTitle>
                <CardDescription>
                  Configure your privacy preferences and data handling options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Data Residency</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Your data is currently stored in: <strong>{PRIVACY_REGIONS[userProfile.preferredRegion].name}</strong>
                    </p>
                    <p className="text-sm text-gray-600">
                      Data residency preference: <strong>{userProfile.dataResidencyPreference.replace('_', ' ')}</strong>
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-2">Communication Preferences</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Marketing Communications</span>
                        <Switch checked={userProfile.communicationPreferences.marketing} />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Analytics Data Collection</span>
                        <Switch checked={userProfile.communicationPreferences.analytics} />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Personalization</span>
                        <Switch checked={userProfile.communicationPreferences.personalization} />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

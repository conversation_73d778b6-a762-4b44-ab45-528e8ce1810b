'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mail, 
  MessageSquare, 
  Webhook, 
  Settings, 
  Save, 
  TestTube,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { NotificationPreferences } from '@/lib/document-types';

interface NotificationSettingsProps {
  tenantId: string;
}

export function NotificationSettings({ tenantId }: NotificationSettingsProps) {
  const [preferences, setPreferences] = useState<NotificationPreferences[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testingEmail, setTestingEmail] = useState(false);
  const [testingSMS, setTestingSMS] = useState(false);
  const [testingWebhook, setTestingWebhook] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, [tenantId]);

  const loadPreferences = async () => {
    setLoading(true);
    try {
      // TODO: Fetch from API
      // const response = await fetch(`/api/notifications/preferences?tenantId=${tenantId}`);
      // const data = await response.json();
      
      // Mock data for now
      const mockPreferences: NotificationPreferences[] = [
        {
          id: '1',
          tenant_id: tenantId,
          notification_type: 'email',
          is_enabled: true,
          configuration: {
            smtp_host: 'smtp.example.com',
            smtp_port: 587,
            from_email: '<EMAIL>',
            from_name: 'Privacy First App',
            subject_template: '{{document_type}} Updated - {{company_name}}',
            email_template: 'Your {{document_type}} has been updated. Please review the changes at: {{document_url}}',
            include_pdf: true
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: '2',
          tenant_id: tenantId,
          notification_type: 'webhook',
          is_enabled: false,
          configuration: {
            webhook_url: 'https://api.example.com/webhooks/document-updates',
            secret_key: 'webhook_secret_key',
            retry_attempts: 3,
            timeout_seconds: 30
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: '3',
          tenant_id: tenantId,
          notification_type: 'sms',
          is_enabled: false,
          configuration: {
            provider: 'twilio',
            api_key: '',
            from_number: '',
            message_template: 'Your {{document_type}} has been updated. View: {{document_url}}'
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      
      setPreferences(mockPreferences);
    } catch (error) {
      console.error('Error loading preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const updatePreference = (id: string, updates: Partial<NotificationPreferences>) => {
    setPreferences(prev => prev.map(pref => 
      pref.id === id ? { ...pref, ...updates } : pref
    ));
  };

  const updateConfiguration = (id: string, configUpdates: any) => {
    setPreferences(prev => prev.map(pref => 
      pref.id === id 
        ? { ...pref, configuration: { ...pref.configuration, ...configUpdates } }
        : pref
    ));
  };

  const savePreferences = async () => {
    setSaving(true);
    try {
      // TODO: Save to API
      // await fetch(`/api/notifications/preferences?tenantId=${tenantId}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(preferences)
      // });
      
      console.log('Saving preferences:', preferences);
      
      // Mock success
      setTimeout(() => {
        setSaving(false);
      }, 1000);
    } catch (error) {
      console.error('Error saving preferences:', error);
      setSaving(false);
    }
  };

  const testNotification = async (type: 'email' | 'sms' | 'webhook') => {
    const setTesting = type === 'email' ? setTestingEmail : 
                      type === 'sms' ? setTestingSMS : setTestingWebhook;
    
    setTesting(true);
    try {
      // TODO: Send test notification
      // await fetch(`/api/notifications/test?tenantId=${tenantId}&type=${type}`, {
      //   method: 'POST'
      // });
      
      console.log(`Testing ${type} notification`);
      
      // Mock test
      setTimeout(() => {
        setTesting(false);
      }, 2000);
    } catch (error) {
      console.error(`Error testing ${type} notification:`, error);
      setTesting(false);
    }
  };

  const emailPreference = preferences.find(p => p.notification_type === 'email');
  const smsPreference = preferences.find(p => p.notification_type === 'sms');
  const webhookPreference = preferences.find(p => p.notification_type === 'webhook');

  if (loading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Notification Settings</h2>
          <p className="text-muted-foreground">
            Configure how users are notified about document updates and consent confirmations
          </p>
        </div>
        <Button onClick={savePreferences} disabled={saving}>
          <Save className="h-4 w-4 mr-2" />
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

      <Tabs defaultValue="email" className="space-y-4">
        <TabsList>
          <TabsTrigger value="email" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email
          </TabsTrigger>
          <TabsTrigger value="sms" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            SMS
          </TabsTrigger>
          <TabsTrigger value="webhook" className="flex items-center gap-2">
            <Webhook className="h-4 w-4" />
            Webhooks
          </TabsTrigger>
        </TabsList>

        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    Email Notifications
                  </CardTitle>
                  <CardDescription>
                    Configure email notifications for document updates and consent confirmations
                  </CardDescription>
                </div>
                <div className="flex items-center gap-4">
                  <Switch
                    checked={emailPreference?.is_enabled || false}
                    onCheckedChange={(enabled) => 
                      emailPreference && updatePreference(emailPreference.id, { is_enabled: enabled })
                    }
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => testNotification('email')}
                    disabled={testingEmail || !emailPreference?.is_enabled}
                  >
                    <TestTube className="h-3 w-3 mr-1" />
                    {testingEmail ? 'Testing...' : 'Test'}
                  </Button>
                </div>
              </div>
            </CardHeader>
            {emailPreference?.is_enabled && (
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="smtp-host">SMTP Host</Label>
                    <Input
                      id="smtp-host"
                      value={emailPreference.configuration.smtp_host || ''}
                      onChange={(e) => updateConfiguration(emailPreference.id, { smtp_host: e.target.value })}
                      placeholder="smtp.example.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtp-port">SMTP Port</Label>
                    <Input
                      id="smtp-port"
                      type="number"
                      value={emailPreference.configuration.smtp_port || ''}
                      onChange={(e) => updateConfiguration(emailPreference.id, { smtp_port: parseInt(e.target.value) })}
                      placeholder="587"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="from-email">From Email</Label>
                    <Input
                      id="from-email"
                      type="email"
                      value={emailPreference.configuration.from_email || ''}
                      onChange={(e) => updateConfiguration(emailPreference.id, { from_email: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="from-name">From Name</Label>
                    <Input
                      id="from-name"
                      value={emailPreference.configuration.from_name || ''}
                      onChange={(e) => updateConfiguration(emailPreference.id, { from_name: e.target.value })}
                      placeholder="Privacy First App"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject-template">Subject Template</Label>
                  <Input
                    id="subject-template"
                    value={emailPreference.configuration.subject_template || ''}
                    onChange={(e) => updateConfiguration(emailPreference.id, { subject_template: e.target.value })}
                    placeholder="{{document_type}} Updated - {{company_name}}"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email-template">Email Template</Label>
                  <Textarea
                    id="email-template"
                    value={emailPreference.configuration.email_template || ''}
                    onChange={(e) => updateConfiguration(emailPreference.id, { email_template: e.target.value })}
                    placeholder="Your {{document_type}} has been updated. Please review the changes at: {{document_url}}"
                    rows={4}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="include-pdf"
                    checked={emailPreference.configuration.include_pdf || false}
                    onCheckedChange={(checked) => updateConfiguration(emailPreference.id, { include_pdf: checked })}
                  />
                  <Label htmlFor="include-pdf">Include PDF attachment</Label>
                </div>
              </CardContent>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="sms" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    SMS Notifications
                  </CardTitle>
                  <CardDescription>
                    Configure SMS notifications via Twilio or other providers
                  </CardDescription>
                </div>
                <div className="flex items-center gap-4">
                  <Switch
                    checked={smsPreference?.is_enabled || false}
                    onCheckedChange={(enabled) => 
                      smsPreference && updatePreference(smsPreference.id, { is_enabled: enabled })
                    }
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => testNotification('sms')}
                    disabled={testingSMS || !smsPreference?.is_enabled}
                  >
                    <TestTube className="h-3 w-3 mr-1" />
                    {testingSMS ? 'Testing...' : 'Test'}
                  </Button>
                </div>
              </div>
            </CardHeader>
            {smsPreference?.is_enabled && (
              <CardContent className="space-y-4">
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    SMS notifications require a Twilio account or similar SMS provider.
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sms-provider">Provider</Label>
                    <Input
                      id="sms-provider"
                      value={smsPreference.configuration.provider || ''}
                      onChange={(e) => updateConfiguration(smsPreference.id, { provider: e.target.value })}
                      placeholder="twilio"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sms-api-key">API Key</Label>
                    <Input
                      id="sms-api-key"
                      type="password"
                      value={smsPreference.configuration.api_key || ''}
                      onChange={(e) => updateConfiguration(smsPreference.id, { api_key: e.target.value })}
                      placeholder="Your API key"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="from-number">From Number</Label>
                  <Input
                    id="from-number"
                    value={smsPreference.configuration.from_number || ''}
                    onChange={(e) => updateConfiguration(smsPreference.id, { from_number: e.target.value })}
                    placeholder="+1234567890"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sms-template">Message Template</Label>
                  <Textarea
                    id="sms-template"
                    value={smsPreference.configuration.message_template || ''}
                    onChange={(e) => updateConfiguration(smsPreference.id, { message_template: e.target.value })}
                    placeholder="Your {{document_type}} has been updated. View: {{document_url}}"
                    rows={3}
                  />
                </div>
              </CardContent>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="webhook" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Webhook className="h-5 w-5" />
                    Webhook Notifications
                  </CardTitle>
                  <CardDescription>
                    Configure webhook endpoints for programmatic notifications
                  </CardDescription>
                </div>
                <div className="flex items-center gap-4">
                  <Switch
                    checked={webhookPreference?.is_enabled || false}
                    onCheckedChange={(enabled) => 
                      webhookPreference && updatePreference(webhookPreference.id, { is_enabled: enabled })
                    }
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => testNotification('webhook')}
                    disabled={testingWebhook || !webhookPreference?.is_enabled}
                  >
                    <TestTube className="h-3 w-3 mr-1" />
                    {testingWebhook ? 'Testing...' : 'Test'}
                  </Button>
                </div>
              </div>
            </CardHeader>
            {webhookPreference?.is_enabled && (
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="webhook-url">Webhook URL</Label>
                  <Input
                    id="webhook-url"
                    type="url"
                    value={webhookPreference.configuration.webhook_url || ''}
                    onChange={(e) => updateConfiguration(webhookPreference.id, { webhook_url: e.target.value })}
                    placeholder="https://api.example.com/webhooks/document-updates"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="secret-key">Secret Key</Label>
                  <Input
                    id="secret-key"
                    type="password"
                    value={webhookPreference.configuration.secret_key || ''}
                    onChange={(e) => updateConfiguration(webhookPreference.id, { secret_key: e.target.value })}
                    placeholder="webhook_secret_key"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="retry-attempts">Retry Attempts</Label>
                    <Input
                      id="retry-attempts"
                      type="number"
                      value={webhookPreference.configuration.retry_attempts || ''}
                      onChange={(e) => updateConfiguration(webhookPreference.id, { retry_attempts: parseInt(e.target.value) })}
                      placeholder="3"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timeout">Timeout (seconds)</Label>
                    <Input
                      id="timeout"
                      type="number"
                      value={webhookPreference.configuration.timeout_seconds || ''}
                      onChange={(e) => updateConfiguration(webhookPreference.id, { timeout_seconds: parseInt(e.target.value) })}
                      placeholder="30"
                    />
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

# Document Generator System

A comprehensive document generator system similar to Termly.io that allows users to create legal documents through guided forms. The system integrates with Supabase for data persistence and supports multi-tenant architecture.

## 🎯 Features

### Document Types Supported
- **Privacy Policy Generator** - GDPR/CCPA compliant privacy policies
- **Terms and Conditions Generator** - Service terms and user agreements  
- **Cookie Policy Generator** - Cookie usage and consent policies
- **EULA Generator** - End User License Agreements
- **Acceptable Use Policy Generator** - Platform usage guidelines
- **Return Policy Generator** - E-commerce return policies
- **Disclaimer Generator** - Legal disclaimers and limitations
- **Shipping Policy Generator** - Shipping terms and conditions

### Core Functionality
- **Progressive Form System** - Multi-step guided forms with validation
- **Auto-Save** - Real-time form data persistence to Supabase
- **Draft Management** - Save and resume document creation sessions
- **Document Generation** - Generate HTML and Markdown versions
- **Version Control** - Track document changes and maintain history
- **Multi-Tenant Support** - Isolated data per tenant with RLS policies

## 🏗️ Architecture

### Database Schema

```sql
-- Document Generator Sessions
document_generator_sessions (
  id UUID PRIMARY KEY,
  tenant_id UUID NOT NULL,
  user_id UUID,
  document_type TEXT NOT NULL,
  session_name TEXT NOT NULL,
  form_data JSONB NOT NULL DEFAULT '{}',
  completion_status TEXT DEFAULT 'draft',
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Generated Documents  
generated_documents (
  id UUID PRIMARY KEY,
  session_id UUID REFERENCES document_generator_sessions(id),
  tenant_id UUID NOT NULL,
  document_type TEXT NOT NULL,
  title TEXT NOT NULL,
  generated_content TEXT NOT NULL,
  markdown_content TEXT NOT NULL,
  pdf_url TEXT,
  status TEXT DEFAULT 'draft',
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Document Versions (for change tracking)
document_versions (
  id UUID PRIMARY KEY,
  document_id UUID REFERENCES generated_documents(id),
  version_number INTEGER NOT NULL,
  content TEXT NOT NULL,
  markdown_content TEXT NOT NULL,
  changes_summary TEXT,
  created_by UUID,
  created_at TIMESTAMP
)
```

### Component Structure

```
src/components/document-generator/
├── DocumentGeneratorHub.tsx      # Main landing page with generator cards
├── DocumentGeneratorWizard.tsx   # Multi-step form wizard
└── DocumentViewer.tsx            # Document preview and management

src/lib/
├── document-generator-types.ts   # TypeScript interfaces and types
└── document-generator-service.ts # Business logic and Supabase integration

src/app/
├── dashboard/document-generator/page.tsx  # Main page component
└── api/document-generator/               # API routes
    ├── sessions/route.ts                 # Session CRUD operations
    ├── sessions/[sessionId]/route.ts     # Individual session operations
    └── generate/route.ts                 # Document generation
```

## 🚀 Getting Started

### 1. Database Setup

Run the migration to create the required tables:

```bash
# Apply the migration
supabase db push

# Or manually run the migration file
psql -f supabase/migrations/20241228_document_generator.sql
```

### 2. Environment Configuration

Ensure your Supabase configuration is set up in your environment variables:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Access the Document Generator

Navigate to `/dashboard/document-generator` in your application to access the document generator hub.

## 📝 Usage

### Creating a Document

1. **Select Document Type** - Choose from 8 available document generators
2. **Fill Form Steps** - Complete the multi-step guided form
3. **Auto-Save** - Form data is automatically saved every 30 seconds
4. **Generate Document** - Create HTML and Markdown versions
5. **Preview & Export** - View, edit, and download the generated document

### Form Data Structure

Each document type has its own form data structure. Example for Privacy Policy:

```typescript
interface PrivacyPolicyFormData {
  // Company Information
  company_name: string;
  website_url: string;
  contact_email: string;
  contact_address: string;
  
  // Data Collection
  collects_personal_info: boolean;
  personal_info_types: string[];
  collects_usage_data: boolean;
  uses_cookies: boolean;
  uses_analytics: boolean;
  
  // Data Usage
  data_usage_purposes: string[];
  shares_data_third_party: boolean;
  third_party_services: string[];
  
  // User Rights
  gdpr_applicable: boolean;
  ccpa_applicable: boolean;
  data_retention_period: string;
  
  // Contact Information
  privacy_contact_email: string;
  dpo_contact?: string;
}
```

## 🔧 API Endpoints

### Sessions

- `GET /api/document-generator/sessions?tenant_id=xxx` - Get all sessions
- `POST /api/document-generator/sessions` - Create new session
- `GET /api/document-generator/sessions/[sessionId]?tenant_id=xxx` - Get specific session
- `PUT /api/document-generator/sessions/[sessionId]` - Update session

### Document Generation

- `POST /api/document-generator/generate` - Generate document from session

### Example API Usage

```typescript
// Create a new session
const response = await fetch('/api/document-generator/sessions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tenant_id: 'tenant-uuid',
    user_id: 'user-uuid',
    document_type: 'privacy_policy',
    session_name: 'My Privacy Policy',
    initial_data: { company_name: 'Example Corp' }
  })
});

// Update session with form data
await fetch(`/api/document-generator/sessions/${sessionId}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tenant_id: 'tenant-uuid',
    form_data: { company_name: 'Updated Corp', website_url: 'https://example.com' },
    completion_status: 'draft'
  })
});

// Generate document
await fetch('/api/document-generator/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tenant_id: 'tenant-uuid',
    session_id: sessionId,
    title: 'Privacy Policy - Final',
    format: 'html'
  })
});
```

## 🔒 Security Features

### Row Level Security (RLS)
- All tables have RLS policies enabled
- Users can only access data for their assigned tenants
- Automatic tenant isolation through user_tenant_roles table

### Data Validation
- Form field validation on both client and server side
- Type-safe API endpoints with TypeScript interfaces
- Sanitized template rendering to prevent XSS

## 🎨 UI/UX Features

### Document Generator Hub
- Grid layout with visual icons for each document type
- Progress indicators showing completion status
- Quick actions for continuing drafts or starting new documents
- Recent sessions overview

### Form Wizard
- Multi-step progressive disclosure
- Real-time validation with helpful hints
- Auto-save with visual indicators
- Easy navigation between steps
- Progress bar showing completion percentage

### Document Viewer
- Tabbed interface for Preview, HTML, and Markdown views
- Copy to clipboard functionality
- Download options for different formats
- Edit and share capabilities

## 🔄 Future Enhancements

### Planned Features
- PDF generation using Puppeteer or similar
- Document templates and customization
- Collaborative editing
- Integration with external legal services
- Advanced document analytics
- Webhook notifications for document events
- Embeddable widgets for websites

### Template Engine Improvements
- Conditional logic in templates
- Custom field types and validation
- Multi-language support
- Industry-specific templates

## 🧪 Testing

The system includes comprehensive TypeScript types and interfaces for testing. Key areas to test:

- Form validation and submission
- Auto-save functionality
- Document generation accuracy
- Multi-tenant data isolation
- API endpoint security
- Template rendering

## 📚 Dependencies

- **Next.js 14** - React framework
- **TypeScript** - Type safety
- **Supabase** - Database and authentication
- **shadcn/ui** - UI components
- **Tailwind CSS** - Styling
- **Lucide React** - Icons

## 🤝 Contributing

When extending the document generator:

1. Add new document types to `DocumentGeneratorType` enum
2. Create form step configurations in `formSteps` object
3. Add template content in `getTemplateForType` method
4. Update form data interfaces for type safety
5. Add corresponding UI icons and colors

## 📄 License

This document generator system is part of the Privacy-First Implementation Framework and follows the same licensing terms.

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createDocumentGeneratorTables() {
  console.log('🚀 Creating Document Generator Tables...\n');

  try {
    // Create document_generator_sessions table
    console.log('📄 Creating document_generator_sessions table...');
    const { error: sessionsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS document_generator_sessions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id UUID NOT NULL,
          user_id UUID,
          document_type TEXT NOT NULL CHECK (document_type IN (
            'privacy_policy', 'terms_conditions', 'cookie_policy', 'eula', 
            'acceptable_use', 'return_policy', 'disclaimer', 'shipping_policy'
          )),
          form_data JSONB NOT NULL DEFAULT '{}',
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'abandoned')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );
      `
    });

    if (sessionsError) {
      console.error('❌ Error creating document_generator_sessions:', sessionsError.message);
    } else {
      console.log('✅ document_generator_sessions table created');
    }

    // Create generated_documents table
    console.log('📄 Creating generated_documents table...');
    const { error: documentsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS generated_documents (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          session_id UUID NOT NULL REFERENCES document_generator_sessions(id) ON DELETE CASCADE,
          tenant_id UUID NOT NULL,
          document_type TEXT NOT NULL CHECK (document_type IN (
            'privacy_policy', 'terms_conditions', 'cookie_policy', 'eula', 
            'acceptable_use', 'return_policy', 'disclaimer', 'shipping_policy'
          )),
          title TEXT NOT NULL,
          generated_content TEXT NOT NULL,
          markdown_content TEXT,
          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );
      `
    });

    if (documentsError) {
      console.error('❌ Error creating generated_documents:', documentsError.message);
    } else {
      console.log('✅ generated_documents table created');
    }

    // Create indexes for better performance
    console.log('📄 Creating indexes...');
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_document_generator_sessions_tenant_id 
        ON document_generator_sessions(tenant_id);
        
        CREATE INDEX IF NOT EXISTS idx_document_generator_sessions_user_id 
        ON document_generator_sessions(user_id);
        
        CREATE INDEX IF NOT EXISTS idx_document_generator_sessions_document_type 
        ON document_generator_sessions(document_type);
        
        CREATE INDEX IF NOT EXISTS idx_generated_documents_tenant_id 
        ON generated_documents(tenant_id);
        
        CREATE INDEX IF NOT EXISTS idx_generated_documents_session_id 
        ON generated_documents(session_id);
        
        CREATE INDEX IF NOT EXISTS idx_generated_documents_document_type 
        ON generated_documents(document_type);
      `
    });

    if (indexError) {
      console.error('❌ Error creating indexes:', indexError.message);
    } else {
      console.log('✅ Indexes created');
    }

    // Create RLS policies
    console.log('📄 Creating RLS policies...');
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS
        ALTER TABLE document_generator_sessions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE generated_documents ENABLE ROW LEVEL SECURITY;
        
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can access their own sessions" ON document_generator_sessions;
        DROP POLICY IF EXISTS "Users can access their own documents" ON generated_documents;
        
        -- Create RLS policies for document_generator_sessions
        CREATE POLICY "Users can access their own sessions" ON document_generator_sessions
          FOR ALL USING (tenant_id::text = current_setting('app.current_tenant_id', true));
        
        -- Create RLS policies for generated_documents
        CREATE POLICY "Users can access their own documents" ON generated_documents
          FOR ALL USING (tenant_id::text = current_setting('app.current_tenant_id', true));
      `
    });

    if (rlsError) {
      console.error('❌ Error creating RLS policies:', rlsError.message);
    } else {
      console.log('✅ RLS policies created');
    }

    // Insert sample data for testing
    console.log('📄 Inserting sample data...');
    const tenantId = '550e8400-e29b-41d4-a716-446655440000';
    const userId = '550e8400-e29b-41d4-a716-446655440001';

    // Create a sample session
    const { data: session, error: sessionInsertError } = await supabase
      .from('document_generator_sessions')
      .insert([{
        tenant_id: tenantId,
        user_id: userId,
        document_type: 'privacy_policy',
        form_data: {
          company_name: 'Example Company',
          website_url: 'https://example.com',
          contact_email: '<EMAIL>',
          contact_address: '123 Main St, City, State 12345'
        },
        status: 'completed'
      }])
      .select()
      .single();

    if (sessionInsertError) {
      console.error('❌ Error inserting sample session:', sessionInsertError.message);
    } else {
      console.log('✅ Sample session created:', session.id);

      // Create a sample generated document
      const { data: document, error: documentInsertError } = await supabase
        .from('generated_documents')
        .insert([{
          session_id: session.id,
          tenant_id: tenantId,
          document_type: 'privacy_policy',
          title: 'Privacy Policy - Example Company',
          generated_content: `
            <h1>Privacy Policy for Example Company</h1>
            <p><strong>Last Updated:</strong> ${new Date().toLocaleDateString()}</p>
            
            <h2>1. Information We Collect</h2>
            <p>We collect the following types of personal information:</p>
            <ul>
              <li>Personal identifiers: Name, Email, Phone Number</li>
              <li>Usage data and analytics</li>
              <li>Cookies and tracking technologies</li>
            </ul>
            
            <h2>2. How We Use Your Information</h2>
            <p>We use your personal information for service provision, customer support, and analytics.</p>
            
            <h2>3. Contact Information</h2>
            <p>For privacy questions, contact us at: <EMAIL></p>
          `,
          markdown_content: `# Privacy Policy for Example Company

**Last Updated:** ${new Date().toLocaleDateString()}

## 1. Information We Collect

We collect the following types of personal information:
- Personal identifiers: Name, Email, Phone Number
- Usage data and analytics
- Cookies and tracking technologies

## 2. How We Use Your Information

We use your personal information for service provision, customer support, and analytics.

## 3. Contact Information

For privacy questions, contact us at: <EMAIL>`,
          status: 'draft'
        }])
        .select()
        .single();

      if (documentInsertError) {
        console.error('❌ Error inserting sample document:', documentInsertError.message);
      } else {
        console.log('✅ Sample document created:', document.id);
      }
    }

    console.log('\n🎉 Document Generator setup complete!');
    console.log('📋 Tables created:');
    console.log('   - document_generator_sessions');
    console.log('   - generated_documents');
    console.log('🔒 RLS policies enabled');
    console.log('📊 Sample data inserted');
    console.log('\n✅ Ready to test the document generator!');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

createDocumentGeneratorTables().catch(console.error);

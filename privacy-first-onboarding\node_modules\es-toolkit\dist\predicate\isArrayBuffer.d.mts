/**
 * Checks if a given value is `<PERSON><PERSON><PERSON><PERSON>uffer`.
 *
 * This function can also serve as a type predicate in TypeScript, narrowing the type of the argument to `<PERSON>rrayBuffer`.
 *
 * @param {unknown} value The value to check if it is a `ArrayBuffer`.
 * @returns {value is <PERSON><PERSON><PERSON><PERSON>uffer} Returns `true` if `value` is a `ArrayBuffer`, else `false`.
 *
 * @example
 * const value1 = new ArrayBuffer();
 * const value2 = new Array();
 * const value3 = new Map();
 *
 * console.log(isArrayBuffer(value1)); // true
 * console.log(isArrayBuffer(value2)); // false
 * console.log(isArrayBuffer(value3)); // false
 */
declare function isArrayBuffer(value: unknown): value is ArrayBuffer;

export { isArrayBuffer };

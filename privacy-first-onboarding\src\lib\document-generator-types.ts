// Document Generator Types and Interfaces
// Comprehensive type definitions for the document generator system

export type DocumentGeneratorType = 
  | 'privacy_policy'
  | 'terms_conditions'
  | 'cookie_policy'
  | 'eula'
  | 'acceptable_use'
  | 'return_policy'
  | 'disclaimer'
  | 'shipping_policy';

export type CompletionStatus = 'draft' | 'completed' | 'published';
export type DocumentStatus = 'draft' | 'published' | 'archived';

// Document Generator Session
export interface DocumentGeneratorSession {
  id: string;
  tenant_id: string;
  user_id?: string;
  document_type: DocumentGeneratorType;
  session_name: string;
  form_data: Record<string, any>;
  completion_status: CompletionStatus;
  created_at: string;
  updated_at: string;
}

// Generated Document
export interface GeneratedDocument {
  id: string;
  session_id: string;
  tenant_id: string;
  document_type: DocumentGeneratorType;
  title: string;
  generated_content: string;
  markdown_content: string;
  pdf_url?: string;
  status: DocumentStatus;
  created_at: string;
  updated_at: string;
}

// Form Step Configuration
export interface FormStep {
  id: string;
  title: string;
  description: string;
  fields: FormField[];
  validation?: ValidationRule[];
}

// Form Field Types
export interface FormField {
  id: string;
  type: 'text' | 'email' | 'url' | 'textarea' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'date';
  label: string;
  placeholder?: string;
  description?: string;
  required: boolean;
  options?: SelectOption[];
  validation?: ValidationRule[];
  conditional?: ConditionalLogic;
}

export interface SelectOption {
  value: string;
  label: string;
  description?: string;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'url' | 'minLength' | 'maxLength' | 'pattern';
  value?: any;
  message: string;
}

export interface ConditionalLogic {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains';
  value: any;
}

// Document Generator Configuration
export interface DocumentGeneratorConfig {
  type: DocumentGeneratorType;
  title: string;
  description: string;
  icon: string;
  color: string;
  steps: FormStep[];
  template: string;
}

// Form Data Structures for Different Document Types
export interface PrivacyPolicyFormData {
  // Company Information
  company_name: string;
  website_url: string;
  contact_email: string;
  contact_address: string;
  
  // Data Collection
  collects_personal_info: boolean;
  personal_info_types: string[];
  collects_usage_data: boolean;
  uses_cookies: boolean;
  uses_analytics: boolean;
  
  // Data Usage
  data_usage_purposes: string[];
  shares_data_third_party: boolean;
  third_party_services: string[];
  
  // User Rights
  gdpr_applicable: boolean;
  ccpa_applicable: boolean;
  data_retention_period: string;
  
  // Contact Information
  privacy_contact_email: string;
  dpo_contact?: string;
}

export interface TermsConditionsFormData {
  // Service Information
  service_name: string;
  service_type: string;
  website_url: string;
  company_name: string;
  
  // User Accounts
  requires_registration: boolean;
  minimum_age: number;
  account_termination_policy: string;
  
  // Service Usage
  prohibited_activities: string[];
  content_guidelines: string[];
  intellectual_property_policy: string;
  
  // Liability & Disclaimers
  liability_limitations: string[];
  warranty_disclaimers: string[];
  
  // Legal
  governing_law: string;
  dispute_resolution: string;
  contact_email: string;
}

export interface CookiePolicyFormData {
  // Website Information
  website_name: string;
  website_url: string;
  
  // Cookie Types
  uses_essential_cookies: boolean;
  uses_analytics_cookies: boolean;
  uses_marketing_cookies: boolean;
  uses_functional_cookies: boolean;
  
  // Third Party Services
  google_analytics: boolean;
  facebook_pixel: boolean;
  other_tracking_services: string[];
  
  // User Control
  cookie_consent_mechanism: string;
  cookie_management_instructions: string;
  
  // Contact
  contact_email: string;
}

// API Request/Response Types
export interface CreateSessionRequest {
  document_type: DocumentGeneratorType;
  session_name: string;
  initial_data?: Record<string, any>;
}

export interface UpdateSessionRequest {
  form_data: Record<string, any>;
  completion_status?: CompletionStatus;
}

export interface GenerateDocumentRequest {
  session_id: string;
  title: string;
  format: 'html' | 'markdown' | 'pdf';
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

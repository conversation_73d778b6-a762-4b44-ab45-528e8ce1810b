// Document Generation API
// Generate new document versions from templates

import { NextRequest, NextResponse } from 'next/server';
import { DocumentManagementService } from '@/lib/document-service';
import { DocumentGenerationRequest } from '@/lib/document-types';

// POST /api/documents/generate - Generate new document version
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const generationRequest: DocumentGenerationRequest = {
      template_id: body.template_id,
      template_data: body.template_data || {},
      version_type: body.version_type || 'minor',
      changelog: body.changelog,
      auto_publish: body.auto_publish || false,
      notify_users: body.notify_users || false
    };

    // Validate required fields
    if (!generationRequest.template_id) {
      return NextResponse.json(
        { error: 'template_id is required' },
        { status: 400 }
      );
    }

    if (!generationRequest.template_data || Object.keys(generationRequest.template_data).length === 0) {
      return NextResponse.json(
        { error: 'template_data is required and cannot be empty' },
        { status: 400 }
      );
    }

    const documentService = new DocumentManagementService(tenantId);
    const response = await documentService.generateDocument(generationRequest);

    if (response.success && response.data) {
      return NextResponse.json({
        success: true,
        data: response.data
      }, { status: 201 });
    } else {
      return NextResponse.json(
        { error: response.error || 'Failed to generate document' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in POST /api/documents/generate:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Global type declarations for the Privacy-First Application

// Extend the global namespace for environment variables
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NEXT_PUBLIC_SUPABASE_URL?: string;
      NEXT_PUBLIC_SUPABASE_ANON_KEY?: string;
      SUPABASE_SERVICE_ROLE_KEY?: string;
      DATABASE_URL?: string;
      DIRECT_URL?: string;
      NEXTAUTH_SECRET?: string;
      NEXTAUTH_URL?: string;
    }
  }
}

// Handle missing type definitions for testing libraries
declare module 'aria-query' {
  export const roles: any;
  export const elementRoles: any;
  export const roleElements: any;
  export const dom: any;
}

// Additional type declarations for missing modules
declare module '@testing-library/jest-dom/extend-expect';
declare module '@testing-library/jest-dom/matchers';

// Extend Jest matchers for custom assertions
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveAttribute(attr: string, value?: string): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toHaveValue(value: string | number): R;
    }
  }
}

// Supabase types extension
declare module '@supabase/supabase-js' {
  export interface Database {
    public: {
      Tables: {
        users: {
          Row: {
            id: string;
            email: string;
            first_name: string;
            last_name: string;
            preferred_region: string;
            data_residency_preference: string;
            marketing_consent: boolean;
            analytics_consent: boolean;
            personalization_consent: boolean;
            third_party_sharing: boolean;
            is_active: boolean;
            email_verified: boolean;
            created_at: string;
            updated_at: string;
            last_login_at?: string;
            tenant_id: string;
          };
          Insert: Omit<Database['public']['Tables']['users']['Row'], 'id' | 'created_at' | 'updated_at'>;
          Update: Partial<Database['public']['Tables']['users']['Insert']>;
        };
        consents: {
          Row: {
            id: string;
            user_id: string;
            data_category: string;
            purpose: string;
            legal_basis: string;
            granted: boolean;
            version: string;
            granted_at?: string;
            withdrawn_at?: string;
            expires_at?: string;
            region: string;
            ip_address?: string;
            user_agent?: string;
            consent_method: string;
            created_at: string;
            updated_at: string;
          };
          Insert: Omit<Database['public']['Tables']['consents']['Row'], 'id' | 'created_at' | 'updated_at'>;
          Update: Partial<Database['public']['Tables']['consents']['Insert']>;
        };
        privacy_requests: {
          Row: {
            id: string;
            user_id: string;
            request_type: string;
            status: string;
            description?: string;
            requested_at: string;
            acknowledged_at?: string;
            completed_at?: string;
            rejected_at?: string;
            rejection_reason?: string;
            response_data?: any;
            region: string;
            legal_deadline: string;
            created_at: string;
            updated_at: string;
          };
          Insert: Omit<Database['public']['Tables']['privacy_requests']['Row'], 'id' | 'created_at' | 'updated_at'>;
          Update: Partial<Database['public']['Tables']['privacy_requests']['Insert']>;
        };
        audit_logs: {
          Row: {
            id: string;
            user_id?: string;
            event_type: string;
            action: string;
            resource: string;
            resource_id?: string;
            ip_address?: string;
            user_agent?: string;
            region?: string;
            old_values?: any;
            new_values?: any;
            metadata?: any;
            timestamp: string;
          };
          Insert: Omit<Database['public']['Tables']['audit_logs']['Row'], 'id' | 'timestamp'>;
          Update: Partial<Database['public']['Tables']['audit_logs']['Insert']>;
        };
      };
    };
  }
}

// Window extensions for development
declare global {
  interface Window {
    __PRIVACY_FIRST_DEBUG__?: boolean;
    __SUPABASE_CLIENT__?: any;
  }
}

export {};

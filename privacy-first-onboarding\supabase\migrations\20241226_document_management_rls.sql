-- Row Level Security (RLS) Policies for Document Management System
-- Ensures tenant isolation and proper access control

-- Enable RLS on all document management tables
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE end_user_consents ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_audit_log ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's tenant_id (if not already exists)
CREATE OR REPLACE FUNCTION get_current_tenant_id()
RETURNS UUID AS $$
BEGIN
  -- Try to get from current setting first
  IF current_setting('app.tenant_id', true) IS NOT NULL THEN
    RETURN current_setting('app.tenant_id', true)::UUID;
  END IF;
  
  -- Fallback to user's tenant_id
  RETURN (
    SELECT tenant_id 
    FROM users 
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Document Templates Policies
CREATE POLICY tenant_isolation_policy ON document_templates
    FOR ALL USING (tenant_id = get_current_tenant_id());

CREATE POLICY document_templates_select_policy ON document_templates
    FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY document_templates_insert_policy ON document_templates
    FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

CREATE POLICY document_templates_update_policy ON document_templates
    FOR UPDATE USING (tenant_id = get_current_tenant_id())
    WITH CHECK (tenant_id = get_current_tenant_id());

CREATE POLICY document_templates_delete_policy ON document_templates
    FOR DELETE USING (tenant_id = get_current_tenant_id());

-- Document Versions Policies
CREATE POLICY tenant_isolation_policy ON document_versions
    FOR ALL USING (tenant_id = get_current_tenant_id());

CREATE POLICY document_versions_select_policy ON document_versions
    FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY document_versions_insert_policy ON document_versions
    FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

CREATE POLICY document_versions_update_policy ON document_versions
    FOR UPDATE USING (tenant_id = get_current_tenant_id())
    WITH CHECK (tenant_id = get_current_tenant_id());

-- End User Consents Policies
CREATE POLICY tenant_isolation_policy ON end_user_consents
    FOR ALL USING (tenant_id = get_current_tenant_id());

CREATE POLICY end_user_consents_select_policy ON end_user_consents
    FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY end_user_consents_insert_policy ON end_user_consents
    FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

-- Notification Preferences Policies
CREATE POLICY tenant_isolation_policy ON notification_preferences
    FOR ALL USING (tenant_id = get_current_tenant_id());

CREATE POLICY notification_preferences_select_policy ON notification_preferences
    FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY notification_preferences_insert_policy ON notification_preferences
    FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

CREATE POLICY notification_preferences_update_policy ON notification_preferences
    FOR UPDATE USING (tenant_id = get_current_tenant_id())
    WITH CHECK (tenant_id = get_current_tenant_id());

-- Notification History Policies
CREATE POLICY tenant_isolation_policy ON notification_history
    FOR ALL USING (tenant_id = get_current_tenant_id());

CREATE POLICY notification_history_select_policy ON notification_history
    FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY notification_history_insert_policy ON notification_history
    FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

-- Notification Queue Policies
CREATE POLICY tenant_isolation_policy ON notification_queue
    FOR ALL USING (tenant_id = get_current_tenant_id());

CREATE POLICY notification_queue_select_policy ON notification_queue
    FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY notification_queue_insert_policy ON notification_queue
    FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

CREATE POLICY notification_queue_update_policy ON notification_queue
    FOR UPDATE USING (tenant_id = get_current_tenant_id())
    WITH CHECK (tenant_id = get_current_tenant_id());

-- Document Audit Log Policies
CREATE POLICY tenant_isolation_policy ON document_audit_log
    FOR ALL USING (tenant_id = get_current_tenant_id());

CREATE POLICY document_audit_log_select_policy ON document_audit_log
    FOR SELECT USING (tenant_id = get_current_tenant_id());

CREATE POLICY document_audit_log_insert_policy ON document_audit_log
    FOR INSERT WITH CHECK (tenant_id = get_current_tenant_id());

-- Force RLS even for superusers (security hardening)
ALTER TABLE document_templates FORCE ROW LEVEL SECURITY;
ALTER TABLE document_versions FORCE ROW LEVEL SECURITY;
ALTER TABLE end_user_consents FORCE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences FORCE ROW LEVEL SECURITY;
ALTER TABLE notification_history FORCE ROW LEVEL SECURITY;
ALTER TABLE notification_queue FORCE ROW LEVEL SECURITY;
ALTER TABLE document_audit_log FORCE ROW LEVEL SECURITY;

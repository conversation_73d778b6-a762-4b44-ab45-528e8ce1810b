import { PrismaClient } from '@prisma/client';

// Global Prisma instance for development
declare global {
  var prisma: PrismaClient | undefined;
}

// Create Prisma client with connection pooling and logging
export const prisma = globalThis.prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

if (process.env.NODE_ENV !== 'production') {
  globalThis.prisma = prisma;
}

// Database connection health check
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

// Graceful shutdown
export async function disconnectDatabase(): Promise<void> {
  await prisma.$disconnect();
}

// Regional database routing (for multitenant architecture)
export class RegionalDatabaseManager {
  private static instance: RegionalDatabaseManager;
  private regionalClients: Map<string, PrismaClient> = new Map();

  private constructor() {}

  static getInstance(): RegionalDatabaseManager {
    if (!RegionalDatabaseManager.instance) {
      RegionalDatabaseManager.instance = new RegionalDatabaseManager();
    }
    return RegionalDatabaseManager.instance;
  }

  // Get database client for specific region
  getRegionalClient(region: string): PrismaClient {
    if (!this.regionalClients.has(region)) {
      // In production, this would connect to region-specific database URLs
      const regionalUrl = this.getRegionalDatabaseUrl(region);
      
      const client = new PrismaClient({
        datasources: {
          db: {
            url: regionalUrl,
          },
        },
        log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
      });

      this.regionalClients.set(region, client);
    }

    return this.regionalClients.get(region)!;
  }

  // Get region-specific database URL
  private getRegionalDatabaseUrl(region: string): string {
    // In production, you would have different database URLs for each region
    const regionalUrls: Record<string, string> = {
      'US': process.env.DATABASE_URL_US || process.env.DATABASE_URL || '',
      'EU': process.env.DATABASE_URL_EU || process.env.DATABASE_URL || '',
      'UK': process.env.DATABASE_URL_UK || process.env.DATABASE_URL || '',
      'CA': process.env.DATABASE_URL_CA || process.env.DATABASE_URL || '',
      'IN': process.env.DATABASE_URL_IN || process.env.DATABASE_URL || '',
      'SG': process.env.DATABASE_URL_SG || process.env.DATABASE_URL || '',
      'JP': process.env.DATABASE_URL_JP || process.env.DATABASE_URL || '',
      'AU': process.env.DATABASE_URL_AU || process.env.DATABASE_URL || '',
      'BR': process.env.DATABASE_URL_BR || process.env.DATABASE_URL || '',
      'CN': process.env.DATABASE_URL_CN || process.env.DATABASE_URL || '',
    };

    return regionalUrls[region] || process.env.DATABASE_URL || '';
  }

  // Disconnect all regional clients
  async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.regionalClients.values()).map(
      client => client.$disconnect()
    );
    await Promise.all(disconnectPromises);
    this.regionalClients.clear();
  }
}

// Audit logging utility
export async function createAuditLog(
  eventType: string,
  action: string,
  resource: string,
  resourceId?: string,
  userId?: string,
  oldValues?: any,
  newValues?: any,
  metadata?: any,
  region?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    const client = region ? 
      RegionalDatabaseManager.getInstance().getRegionalClient(region) : 
      prisma;

    await client.auditLog.create({
      data: {
        eventType,
        action,
        resource,
        resourceId,
        userId,
        oldValues: oldValues ? JSON.parse(JSON.stringify(oldValues)) : null,
        newValues: newValues ? JSON.parse(JSON.stringify(newValues)) : null,
        metadata: metadata ? JSON.parse(JSON.stringify(metadata)) : null,
        region,
        ipAddress,
        userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to create audit log:', error);
    // Don't throw error to avoid breaking main functionality
  }
}

// Data retention cleanup utility
export async function cleanupExpiredData(): Promise<void> {
  try {
    console.log('Starting data retention cleanup...');

    // Get all tenants and their retention policies
    const tenants = await prisma.tenant.findMany({
      include: {
        privacyConfig: true,
      },
    });

    for (const tenant of tenants) {
      if (!tenant.privacyConfig) continue;

      const config = tenant.privacyConfig;
      const now = new Date();

      // Clean up expired consents
      const expiredConsentsDate = new Date(now.getTime() - (config.contactInfoRetention * 24 * 60 * 60 * 1000));
      
      await prisma.consent.deleteMany({
        where: {
          user: {
            tenantId: tenant.id,
          },
          createdAt: {
            lt: expiredConsentsDate,
          },
          granted: false, // Only delete withdrawn consents after retention period
        },
      });

      // Clean up old audit logs (keep for compliance period)
      const auditRetentionDate = new Date(now.getTime() - (2555 * 24 * 60 * 60 * 1000)); // 7 years
      
      await prisma.auditLog.deleteMany({
        where: {
          user: {
            tenantId: tenant.id,
          },
          timestamp: {
            lt: auditRetentionDate,
          },
        },
      });

      console.log(`Cleaned up expired data for tenant: ${tenant.name}`);
    }

    console.log('Data retention cleanup completed');
  } catch (error) {
    console.error('Data retention cleanup failed:', error);
  }
}

// Database seeding for development
export async function seedDatabase(): Promise<void> {
  try {
    console.log('Seeding database...');

    // Create default tenant
    const defaultTenant = await prisma.tenant.upsert({
      where: { slug: 'default' },
      update: {},
      create: {
        name: 'Privacy First Application',
        slug: 'default',
        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],
        defaultRegion: 'US',
        privacyConfig: {
          create: {
            requireExplicitConsent: true,
            enableRightToBeForgotten: true,
            enableDataPortability: true,
            enableOptOut: true,
            cookieConsentRequired: true,
            minorProtection: true,
          },
        },
      },
    });

    // Create default data processing activities
    const activities = [
      {
        name: 'User Account Management',
        description: 'Creating and managing user accounts',
        dataCategories: ['PERSONAL_IDENTIFIERS', 'CONTACT_INFO'],
        purposes: ['Account Creation', 'Authentication', 'User Support'],
        legalBasis: 'Contract Performance',
        retentionPeriod: 2555,
        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],
        thirdParties: [],
      },
      {
        name: 'Marketing Communications',
        description: 'Sending promotional emails and personalized offers',
        dataCategories: ['CONTACT_INFO', 'BEHAVIORAL_DATA'],
        purposes: ['Marketing', 'Personalization'],
        legalBasis: 'Consent',
        retentionPeriod: 1095,
        regions: ['US', 'EU', 'UK', 'CA'],
        thirdParties: [],
      },
      {
        name: 'Usage Analytics',
        description: 'Analyzing user behavior to improve our services',
        dataCategories: ['BEHAVIORAL_DATA', 'LOCATION_DATA'],
        purposes: ['Analytics', 'Service Improvement'],
        legalBasis: 'Legitimate Interest',
        retentionPeriod: 730,
        regions: ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'],
        thirdParties: [],
      },
    ];

    for (const activity of activities) {
      await prisma.dataProcessingActivity.upsert({
        where: {
          tenantId_name: {
            tenantId: defaultTenant.id,
            name: activity.name,
          },
        },
        update: activity,
        create: {
          ...activity,
          tenantId: defaultTenant.id,
        },
      });
    }

    // Create compliance status records
    const regions = ['US', 'EU', 'UK', 'CA', 'IN', 'SG', 'JP', 'AU', 'BR'];
    const laws = {
      'US': ['CCPA', 'CPRA'],
      'EU': ['GDPR'],
      'UK': ['UK GDPR', 'DPA 2018'],
      'CA': ['PIPEDA', 'CPPA'],
      'IN': ['DPDP'],
      'SG': ['PDPA'],
      'JP': ['APPI'],
      'AU': ['Privacy Act'],
      'BR': ['LGPD'],
    };

    for (const region of regions) {
      const regionLaws = laws[region as keyof typeof laws] || [];
      
      for (const law of regionLaws) {
        await prisma.complianceStatus.upsert({
          where: {
            tenantId_region_law: {
              tenantId: defaultTenant.id,
              region,
              law,
            },
          },
          update: {},
          create: {
            tenantId: defaultTenant.id,
            region,
            law,
            isCompliant: true,
            lastAssessment: new Date(),
            nextAssessment: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
            complianceScore: 95,
            findings: {
              strengths: ['Strong consent management', 'Comprehensive audit logging'],
              improvements: ['Enhanced data minimization', 'Automated compliance monitoring'],
            },
          },
        });
      }
    }

    // Create default roles
    const roles = [
      {
        name: 'super_admin',
        description: 'Super administrator with all permissions',
        permissions: ['*'],
        isSystem: true,
      },
      {
        name: 'admin',
        description: 'Administrator with most permissions',
        permissions: [
          'users:read', 'users:create', 'users:update',
          'privacy:read', 'privacy:manage',
          'consents:read', 'consents:manage',
          'audit:read',
          'api:read', 'api:write'
        ],
        isSystem: true,
      },
      {
        name: 'privacy_officer',
        description: 'Privacy officer with privacy management permissions',
        permissions: [
          'users:read',
          'privacy:read', 'privacy:manage', 'privacy:export', 'privacy:delete',
          'consents:read', 'consents:manage',
          'audit:read'
        ],
        isSystem: true,
      },
      {
        name: 'user',
        description: 'Standard user with basic permissions',
        permissions: [
          'privacy:read',
          'consents:read'
        ],
        isSystem: true,
      },
    ];

    for (const role of roles) {
      await prisma.role.upsert({
        where: { name: role.name },
        update: role,
        create: role,
      });
    }

    console.log('Database seeded successfully');
  } catch (error) {
    console.error('Database seeding failed:', error);
    throw error;
  }
}

// Export regional database manager instance
export const regionalDb = RegionalDatabaseManager.getInstance();

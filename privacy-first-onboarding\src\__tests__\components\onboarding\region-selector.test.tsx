import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { RegionSelector } from '@/components/onboarding/region-selector';
import { PRIVACY_REGIONS } from '@/lib/utils';

describe('RegionSelector', () => {
  const mockOnRegionSelect = jest.fn();
  const mockOnNext = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const defaultProps = {
    selectedRegion: null,
    onRegionSelect: mockOnRegionSelect,
    onNext: mockOnNext,
  };

  it('renders region selector component', () => {
    render(<RegionSelector {...defaultProps} />);
    
    expect(screen.getByText('Choose Your Data Region')).toBeInTheDocument();
    expect(screen.getByText(/Select where you'd like your personal data to be stored/)).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('shows detected region information', () => {
    render(<RegionSelector {...defaultProps} />);
    
    expect(screen.getByText('Detected Location')).toBeInTheDocument();
    expect(screen.getByText(/Based on your location, we recommend/)).toBeInTheDocument();
  });

  it('displays all available regions in dropdown', async () => {
    const user = userEvent.setup();
    render(<RegionSelector {...defaultProps} />);
    
    const selectTrigger = screen.getByRole('combobox');
    await user.click(selectTrigger);
    
    // Check that all regions are available
    Object.entries(PRIVACY_REGIONS).forEach(([code, region]) => {
      expect(screen.getByText(region.name)).toBeInTheDocument();
    });
  });

  it('calls onRegionSelect when a region is selected', async () => {
    const user = userEvent.setup();
    render(<RegionSelector {...defaultProps} />);
    
    const selectTrigger = screen.getByRole('combobox');
    await user.click(selectTrigger);
    
    const euOption = screen.getByText('European Union');
    await user.click(euOption);
    
    expect(mockOnRegionSelect).toHaveBeenCalledWith('EU');
  });

  it('shows privacy laws for selected region', () => {
    render(<RegionSelector {...defaultProps} selectedRegion="EU" />);
    
    expect(screen.getByText('Privacy Protection')).toBeInTheDocument();
    expect(screen.getByText('GDPR')).toBeInTheDocument();
  });

  it('disables continue button when no region is selected', () => {
    render(<RegionSelector {...defaultProps} />);
    
    const continueButton = screen.getByRole('button', { name: /continue to privacy preferences/i });
    expect(continueButton).toBeDisabled();
  });

  it('enables continue button when region is selected', () => {
    render(<RegionSelector {...defaultProps} selectedRegion="US" />);
    
    const continueButton = screen.getByRole('button', { name: /continue to privacy preferences/i });
    expect(continueButton).not.toBeDisabled();
  });

  it('calls onNext when continue button is clicked', async () => {
    const user = userEvent.setup();
    render(<RegionSelector {...defaultProps} selectedRegion="US" />);
    
    const continueButton = screen.getByRole('button', { name: /continue to privacy preferences/i });
    await user.click(continueButton);
    
    expect(mockOnNext).toHaveBeenCalled();
  });

  it('shows applicable privacy laws for different regions', () => {
    const { rerender } = render(<RegionSelector {...defaultProps} selectedRegion="EU" />);
    expect(screen.getByText('GDPR')).toBeInTheDocument();
    
    rerender(<RegionSelector {...defaultProps} selectedRegion="US" />);
    expect(screen.getByText('CCPA')).toBeInTheDocument();
    expect(screen.getByText('CPRA')).toBeInTheDocument();
    
    rerender(<RegionSelector {...defaultProps} selectedRegion="IN" />);
    expect(screen.getByText('DPDP')).toBeInTheDocument();
  });

  it('displays privacy information section', () => {
    render(<RegionSelector {...defaultProps} />);
    
    expect(screen.getByText('What this means:')).toBeInTheDocument();
    expect(screen.getByText(/Your personal data will be stored in data centers/)).toBeInTheDocument();
    expect(screen.getByText(/We'll comply with the privacy laws/)).toBeInTheDocument();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<RegionSelector {...defaultProps} />);
    
    const selectTrigger = screen.getByRole('combobox');
    
    // Focus the select
    await user.tab();
    expect(selectTrigger).toHaveFocus();
    
    // Open dropdown with Enter
    await user.keyboard('{Enter}');
    
    // Navigate with arrow keys and select with Enter
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{Enter}');
    
    expect(mockOnRegionSelect).toHaveBeenCalled();
  });

  it('shows region-specific compliance information', () => {
    render(<RegionSelector {...defaultProps} selectedRegion="UK" />);
    
    expect(screen.getByText('UK GDPR')).toBeInTheDocument();
    expect(screen.getByText('DPA 2018')).toBeInTheDocument();
  });

  it('handles region selection with proper data flow', async () => {
    const user = userEvent.setup();
    const { rerender } = render(<RegionSelector {...defaultProps} />);
    
    // Select a region
    const selectTrigger = screen.getByRole('combobox');
    await user.click(selectTrigger);
    
    const canadaOption = screen.getByText('Canada');
    await user.click(canadaOption);
    
    expect(mockOnRegionSelect).toHaveBeenCalledWith('CA');
    
    // Re-render with selected region
    rerender(<RegionSelector {...defaultProps} selectedRegion="CA" />);
    
    // Verify the region is shown as selected
    expect(screen.getByText('PIPEDA')).toBeInTheDocument();
    expect(screen.getByText('CPPA')).toBeInTheDocument();
    
    // Continue button should be enabled
    const continueButton = screen.getByRole('button', { name: /continue to privacy preferences/i });
    expect(continueButton).not.toBeDisabled();
  });
});

import { prisma, regionalDb, createAuditLog } from './database';
import { 
  PrivacyConsent, 
  UserPrivacyProfile, 
  TenantConfiguration,
  OnboardingFormData 
} from './privacy-types';
import { PRIVACY_REGIONS, DATA_CATEGORIES, USER_RIGHTS } from './utils';
import type { PrivacyRegion, DataCategory, UserRight } from './utils';

// Enhanced Privacy Service with Database Integration
export class DatabasePrivacyService {
  private tenantId: string;

  constructor(tenantId: string = 'default') {
    this.tenantId = tenantId;
  }

  // Get database client for user's region
  private async getClientForUser(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferredRegion: true },
    });

    if (!user) {
      throw new Error('User not found');
    }

    return regionalDb.getRegionalClient(user.preferredRegion);
  }

  // User Registration and Onboarding
  async registerUser(
    formData: OnboardingFormData,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ userId: string; success: boolean }> {
    try {
      // Get regional client based on preferred region
      const client = regionalDb.getRegionalClient(formData.preferredRegion);

      // Get or create tenant
      const tenant = await prisma.tenant.findUnique({
        where: { slug: 'default' },
        include: { privacyConfig: true },
      });

      if (!tenant) {
        throw new Error('Tenant not found');
      }

      // Create user
      const user = await client.user.create({
        data: {
          tenantId: tenant.id,
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          preferredRegion: formData.preferredRegion,
          dataResidencyPreference: formData.dataResidencyPreference,
          marketingConsent: formData.communicationPreferences.marketing,
          analyticsConsent: formData.communicationPreferences.analytics,
          personalizationConsent: formData.communicationPreferences.personalization,
          thirdPartySharing: formData.communicationPreferences.thirdPartySharing,
        },
      });

      // Create consents
      for (const consent of formData.consents) {
        await client.consent.create({
          data: {
            userId: user.id,
            dataCategory: consent.dataCategory,
            purpose: consent.purpose,
            legalBasis: this.determineLegalBasis(consent.dataCategory, consent.purpose),
            granted: consent.granted,
            grantedAt: consent.granted ? new Date() : null,
            region: consent.region,
            ipAddress,
            userAgent,
            consentMethod: 'WEB_FORM',
          },
        });
      }

      // Create audit log
      await createAuditLog(
        'USER_REGISTRATION',
        'CREATE',
        'user',
        user.id,
        user.id,
        null,
        { email: formData.email, region: formData.preferredRegion },
        { onboardingFlow: true },
        formData.preferredRegion,
        ipAddress,
        userAgent
      );

      return { userId: user.id, success: true };
    } catch (error) {
      console.error('User registration failed:', error);
      throw new Error('Registration failed');
    }
  }

  // Consent Management
  async getConsents(userId: string): Promise<PrivacyConsent[]> {
    try {
      const client = await this.getClientForUser(userId);
      
      const consents = await client.consent.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      });

      return consents.map(consent => ({
        id: consent.id,
        userId: consent.userId,
        dataCategory: consent.dataCategory as keyof typeof DATA_CATEGORIES,
        purpose: consent.purpose,
        granted: consent.granted,
        timestamp: consent.grantedAt || consent.createdAt,
        expiresAt: consent.expiresAt || undefined,
        region: consent.region as keyof typeof PRIVACY_REGIONS,
        version: consent.version,
      }));
    } catch (error) {
      console.error('Failed to get consents:', error);
      return [];
    }
  }

  async updateConsent(
    userId: string, 
    consentId: string, 
    granted: boolean,
    ipAddress?: string,
    userAgent?: string
  ): Promise<boolean> {
    try {
      const client = await this.getClientForUser(userId);
      
      const oldConsent = await client.consent.findUnique({
        where: { id: consentId },
      });

      if (!oldConsent) {
        return false;
      }

      const updatedConsent = await client.consent.update({
        where: { id: consentId },
        data: {
          granted,
          grantedAt: granted ? new Date() : null,
          withdrawnAt: !granted ? new Date() : null,
          ipAddress,
          userAgent,
        },
      });

      // Create audit log
      await createAuditLog(
        'CONSENT_CHANGE',
        'UPDATE',
        'consent',
        consentId,
        userId,
        { granted: oldConsent.granted },
        { granted },
        { method: 'dashboard' },
        oldConsent.region,
        ipAddress,
        userAgent
      );

      return true;
    } catch (error) {
      console.error('Failed to update consent:', error);
      return false;
    }
  }

  // User Rights Management
  async exerciseUserRight(
    userId: string, 
    right: UserRight,
    description?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<string> {
    try {
      const client = await this.getClientForUser(userId);
      
      const user = await client.user.findUnique({
        where: { id: userId },
        select: { preferredRegion: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Calculate legal deadline based on region and right
      const legalDeadline = this.calculateLegalDeadline(right, user.preferredRegion as PrivacyRegion);

      const request = await client.privacyRequest.create({
        data: {
          userId,
          requestType: right,
          status: 'PENDING',
          description,
          region: user.preferredRegion,
          legalDeadline,
        },
      });

      // Create audit log
      await createAuditLog(
        'PRIVACY_REQUEST',
        'CREATE',
        'privacy_request',
        request.id,
        userId,
        null,
        { requestType: right, status: 'PENDING' },
        { ipAddress, userAgent },
        user.preferredRegion,
        ipAddress,
        userAgent
      );

      // Auto-process certain requests
      if (right === 'ACCESS') {
        await this.processDataAccessRequest(request.id);
      } else if (right === 'DELETION') {
        // For demo purposes, auto-approve deletion after confirmation
        await this.processDataDeletionRequest(request.id);
      }

      return request.id;
    } catch (error) {
      console.error('Failed to exercise user right:', error);
      throw error;
    }
  }

  // Data Export (Right to Portability)
  async exportUserData(userId: string): Promise<any> {
    try {
      const client = await this.getClientForUser(userId);
      
      const user = await client.user.findUnique({
        where: { id: userId },
        include: {
          consents: true,
          privacyRequests: true,
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      const exportData = {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          preferredRegion: user.preferredRegion,
          dataResidencyPreference: user.dataResidencyPreference,
          createdAt: user.createdAt,
        },
        consents: user.consents,
        privacyRequests: user.privacyRequests,
        exportedAt: new Date().toISOString(),
        format: 'JSON',
        exportMethod: 'USER_REQUEST',
      };

      // Create audit log
      await createAuditLog(
        'DATA_EXPORT',
        'READ',
        'user',
        userId,
        userId,
        null,
        { exportSize: JSON.stringify(exportData).length },
        { method: 'dashboard' },
        user.preferredRegion
      );

      return exportData;
    } catch (error) {
      console.error('Failed to export user data:', error);
      throw error;
    }
  }

  // Data Deletion (Right to be Forgotten)
  async deleteUserData(userId: string): Promise<boolean> {
    try {
      const client = await this.getClientForUser(userId);
      
      const user = await client.user.findUnique({
        where: { id: userId },
        select: { preferredRegion: true, email: true },
      });

      if (!user) {
        return false;
      }

      // Create final audit log before deletion
      await createAuditLog(
        'DATA_DELETION',
        'DELETE',
        'user',
        userId,
        userId,
        { email: user.email },
        null,
        { method: 'user_request', rightToBeForgotten: true },
        user.preferredRegion
      );

      // Delete user and all related data (cascading deletes handle relationships)
      await client.user.delete({
        where: { id: userId },
      });

      return true;
    } catch (error) {
      console.error('Failed to delete user data:', error);
      return false;
    }
  }

  // Privacy Dashboard Data
  async getPrivacyDashboardData(userId: string): Promise<{
    profile: UserPrivacyProfile | null;
    consents: PrivacyConsent[];
    applicableLaws: string[];
    dataRetentionInfo: any;
  }> {
    try {
      const client = await this.getClientForUser(userId);
      
      const user = await client.user.findUnique({
        where: { id: userId },
        include: {
          consents: true,
          privacyRequests: {
            orderBy: { requestedAt: 'desc' },
            take: 10,
          },
        },
      });

      if (!user) {
        return {
          profile: null,
          consents: [],
          applicableLaws: [],
          dataRetentionInfo: {}
        };
      }

      const profile: UserPrivacyProfile = {
        userId: user.id,
        preferredRegion: user.preferredRegion as PrivacyRegion,
        dataResidencyPreference: user.dataResidencyPreference as any,
        consentPreferences: user.consents.map(c => ({
          id: c.id,
          userId: c.userId,
          dataCategory: c.dataCategory as keyof typeof DATA_CATEGORIES,
          purpose: c.purpose,
          granted: c.granted,
          timestamp: c.grantedAt || c.createdAt,
          region: c.region as keyof typeof PRIVACY_REGIONS,
          version: c.version,
        })),
        communicationPreferences: {
          marketing: user.marketingConsent,
          analytics: user.analyticsConsent,
          personalization: user.personalizationConsent,
          thirdPartySharing: user.thirdPartySharing,
        },
        rightsExercised: user.privacyRequests.map(r => ({
          right: r.requestType as keyof typeof USER_RIGHTS,
          requestedAt: r.requestedAt,
          completedAt: r.completedAt || undefined,
          status: r.status as any,
        })),
      };

      const consents = await this.getConsents(userId);
      const applicableLaws = PRIVACY_REGIONS[user.preferredRegion as PrivacyRegion].laws;

      return {
        profile,
        consents,
        applicableLaws,
        dataRetentionInfo: this.getDataRetentionInfo(user.preferredRegion as PrivacyRegion)
      };
    } catch (error) {
      console.error('Failed to get dashboard data:', error);
      throw error;
    }
  }

  // Helper Methods
  private determineLegalBasis(dataCategory: string, purpose: string): string {
    // Determine legal basis based on data category and purpose
    if (purpose.toLowerCase().includes('marketing')) {
      return 'CONSENT';
    }
    if (purpose.toLowerCase().includes('account') || purpose.toLowerCase().includes('service')) {
      return 'CONTRACT';
    }
    if (purpose.toLowerCase().includes('analytics') || purpose.toLowerCase().includes('improvement')) {
      return 'LEGITIMATE_INTEREST';
    }
    return 'CONSENT';
  }

  private calculateLegalDeadline(right: UserRight, region: PrivacyRegion): Date {
    // Calculate legal deadline based on privacy law requirements
    const deadlines: Record<PrivacyRegion, number> = {
      'EU': 30, // GDPR: 1 month
      'UK': 30, // UK GDPR: 1 month
      'CA': 30, // PIPEDA: 30 days
      'US': 45, // CCPA: 45 days
      'IN': 30, // DPDP: 30 days
      'SG': 30, // PDPA: 30 days
      'JP': 30, // APPI: 30 days
      'AU': 30, // Privacy Act: 30 days
      'BR': 15, // LGPD: 15 days
      'CN': 15, // PIPL: 15 days
    };

    const days = deadlines[region] || 30;
    return new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  }

  private async processDataAccessRequest(requestId: string): Promise<void> {
    // Auto-process data access requests
    const client = prisma; // Use main client for request processing
    
    await client.privacyRequest.update({
      where: { id: requestId },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        responseData: { message: 'Data export available through dashboard' },
      },
    });
  }

  private async processDataDeletionRequest(requestId: string): Promise<void> {
    // Mark deletion request as pending manual review
    const client = prisma;
    
    await client.privacyRequest.update({
      where: { id: requestId },
      data: {
        status: 'IN_PROGRESS',
        acknowledgedAt: new Date(),
      },
    });
  }

  private getDataRetentionInfo(region: PrivacyRegion): any {
    // Return data retention information based on region
    return {
      personalIdentifiers: { period: '7 years', basis: 'Legal requirement' },
      contactInfo: { period: '3 years', basis: 'Business necessity' },
      consents: { period: 'Until withdrawn + 1 year', basis: 'Compliance audit' },
      auditLogs: { period: '7 years', basis: 'Legal requirement' },
    };
  }
}

// Export singleton instance
export const databasePrivacyService = new DatabasePrivacyService();

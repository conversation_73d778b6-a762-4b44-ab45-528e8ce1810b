{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Privacy Framework Utilities\nexport const PRIVACY_REGIONS = {\n  US: { code: 'US', name: 'United States', laws: ['CCPA', 'CPRA'] },\n  EU: { code: 'EU', name: 'European Union', laws: ['GDPR'] },\n  UK: { code: 'UK', name: 'United Kingdom', laws: ['UK GDPR', 'DPA 2018'] },\n  CA: { code: 'CA', name: 'Canada', laws: ['PIPEDA', 'CPPA'] },\n  IN: { code: 'IN', name: 'India', laws: ['DPDP'] },\n  SG: { code: 'SG', name: 'Singapore', laws: ['PDPA'] },\n  JP: { code: 'JP', name: 'Japan', laws: ['APPI'] },\n  AU: { code: 'AU', name: 'Australia', laws: ['Privacy Act'] },\n  BR: { code: 'BR', name: 'Brazil', laws: ['LGPD'] },\n  CN: { code: 'CN', name: 'China', laws: ['PIPL'] },\n} as const;\n\nexport type PrivacyRegion = keyof typeof PRIVACY_REGIONS;\n\nexport const DATA_CATEGORIES = {\n  PERSONAL_IDENTIFIERS: 'Personal Identifiers',\n  CONTACT_INFO: 'Contact Information',\n  FINANCIAL_INFO: 'Financial Information',\n  BIOMETRIC_DATA: 'Biometric Data',\n  LOCATION_DATA: 'Location Data',\n  BEHAVIORAL_DATA: 'Behavioral Data',\n  SENSITIVE_PERSONAL: 'Sensitive Personal Data',\n  HEALTH_DATA: 'Health Data',\n  EMPLOYMENT_DATA: 'Employment Data',\n  EDUCATION_DATA: 'Education Data',\n} as const;\n\nexport type DataCategory = keyof typeof DATA_CATEGORIES;\n\nexport const USER_RIGHTS = {\n  ACCESS: 'Right to Access',\n  CORRECTION: 'Right to Correction',\n  DELETION: 'Right to Deletion',\n  PORTABILITY: 'Right to Data Portability',\n  OPT_OUT: 'Right to Opt-Out',\n  RESTRICT_PROCESSING: 'Right to Restrict Processing',\n  OBJECT: 'Right to Object',\n  NON_DISCRIMINATION: 'Right to Non-Discrimination',\n} as const;\n\nexport type UserRight = keyof typeof USER_RIGHTS;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QAAE,MAAM;QAAM,MAAM;QAAiB,MAAM;YAAC;YAAQ;SAAO;IAAC;IAChE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;SAAO;IAAC;IACzD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;YAAC;YAAW;SAAW;IAAC;IACxE,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;YAAU;SAAO;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAO;IAAC;IACpD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;IAChD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;YAAC;SAAc;IAAC;IAC3D,IAAI;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;YAAC;SAAO;IAAC;IACjD,IAAI;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;YAAC;SAAO;IAAC;AAClD;AAIO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,iBAAiB;IACjB,oBAAoB;IACpB,aAAa;IACb,iBAAiB;IACjB,gBAAgB;AAClB;AAIO,MAAM,cAAc;IACzB,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,aAAa;IACb,SAAS;IACT,qBAAqB;IACrB,QAAQ;IACR,oBAAoB;AACtB", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\n// Supabase configuration with fallbacks\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-service-key';\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured = !!(\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'https://placeholder.supabase.co' &&\n  supabaseAnonKey !== 'placeholder-key' &&\n  supabaseUrl !== 'YOUR_SUPABASE_PROJECT_URL' &&\n  supabaseAnonKey !== 'YOUR_SUPABASE_ANON_KEY'\n);\n\n// Client-side Supabase client (with RLS enabled) - only create if configured\nexport const supabase = isSupabaseConfigured ? createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  db: {\n    schema: 'public'\n  },\n  global: {\n    headers: {\n      'X-Client-Info': 'privacy-first-app'\n    }\n  }\n}) : null;\n\n// Server-side Supabase client (bypasses RLS for admin operations) - only create if configured\nexport const supabaseAdmin = isSupabaseConfigured ? createClient(supabaseUrl, supabaseServiceKey, {\n  auth: {\n    autoRefreshToken: false,\n    persistSession: false\n  },\n  db: {\n    schema: 'public'\n  }\n}) : null;\n\n// Regional Supabase clients for data residency compliance\nexport class SupabaseRegionalManager {\n  private static instance: SupabaseRegionalManager;\n  private regionalClients: Map<string, any> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): SupabaseRegionalManager {\n    if (!SupabaseRegionalManager.instance) {\n      SupabaseRegionalManager.instance = new SupabaseRegionalManager();\n    }\n    return SupabaseRegionalManager.instance;\n  }\n\n  // Get region-specific Supabase client\n  getRegionalClient(region: string) {\n    if (!this.regionalClients.has(region)) {\n      // In production, you would have different Supabase projects for each region\n      const regionalUrls: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_URL_US || supabaseUrl,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_URL_EU || supabaseUrl,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_URL_UK || supabaseUrl,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_URL_CA || supabaseUrl,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_URL_IN || supabaseUrl,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_URL_SG || supabaseUrl,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_URL_JP || supabaseUrl,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_URL_AU || supabaseUrl,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_URL_BR || supabaseUrl,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_URL_CN || supabaseUrl,\n      };\n\n      const regionalKeys: Record<string, string> = {\n        'US': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_US || supabaseAnonKey,\n        'EU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_EU || supabaseAnonKey,\n        'UK': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_UK || supabaseAnonKey,\n        'CA': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CA || supabaseAnonKey,\n        'IN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_IN || supabaseAnonKey,\n        'SG': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_SG || supabaseAnonKey,\n        'JP': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_JP || supabaseAnonKey,\n        'AU': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_AU || supabaseAnonKey,\n        'BR': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_BR || supabaseAnonKey,\n        'CN': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_CN || supabaseAnonKey,\n      };\n\n      const client = createClient(\n        regionalUrls[region] || supabaseUrl,\n        regionalKeys[region] || supabaseAnonKey,\n        {\n          auth: {\n            autoRefreshToken: true,\n            persistSession: true\n          }\n        }\n      );\n\n      this.regionalClients.set(region, client);\n    }\n\n    return this.regionalClients.get(region);\n  }\n}\n\n// Utility functions for Supabase operations\nexport const supabaseUtils = {\n  // Check if Supabase is properly configured\n  isConfigured: () => {\n    return isSupabaseConfigured;\n  },\n\n  // Get current user\n  getCurrentUser: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n  },\n\n  // Sign in with email and password\n  signIn: async (email: string, password: string) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign up with email and password\n  signUp: async (email: string, password: string, metadata?: any) => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: metadata\n      }\n    });\n    if (error) throw error;\n    return data;\n  },\n\n  // Sign out\n  signOut: async () => {\n    if (!supabase) throw new Error('Supabase not configured');\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n  },\n\n  // Create audit log entry\n  createAuditLog: async (logData: any) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n    const { data, error } = await supabaseAdmin\n      .from('audit_logs')\n      .insert([logData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get user's privacy profile\n  getUserPrivacyProfile: async (userId: string) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n\n    try {\n      // Get basic user data\n      const { data: userData, error: userError } = await supabaseAdmin\n        .from('users')\n        .select(`\n          id,\n          tenant_id,\n          email,\n          first_name,\n          last_name,\n          preferred_region,\n          data_residency_preference,\n          marketing_consent,\n          analytics_consent,\n          personalization_consent,\n          third_party_sharing,\n          is_active,\n          email_verified,\n          created_at,\n          updated_at,\n          tenant:tenants(id, name, created_at)\n        `)\n        .eq('id', userId)\n        .single();\n\n      if (userError) {\n        console.error('Error fetching user:', userError);\n        throw new Error(`Failed to fetch user: ${userError.message || 'Unknown error'}`);\n      }\n\n      if (!userData) {\n        throw new Error(`User not found with ID: ${userId}`);\n      }\n\n      // Try to get consent data from document management tables if they exist\n      let consents = [];\n      try {\n        const { data: consentData, error: consentError } = await supabaseAdmin\n          .from('end_user_consents')\n          .select('*')\n          .eq('end_user_identifier', userData.email)\n          .eq('tenant_id', userData.tenant_id);\n\n        if (consentError) {\n          console.log('Consent table not available:', consentError.message);\n        } else if (consentData) {\n          consents = consentData;\n        }\n      } catch (e) {\n        // Document management tables don't exist yet, that's okay\n        console.log('Document management tables not available yet');\n      }\n\n      // Try to get privacy requests if table exists\n      let privacy_requests = [];\n      try {\n        const { data: requestData, error: requestError } = await supabaseAdmin\n          .from('privacy_requests')\n          .select('*')\n          .eq('user_id', userId);\n\n        if (!requestError && requestData) {\n          privacy_requests = requestData;\n        }\n      } catch (e) {\n        // Privacy requests table doesn't exist, that's okay\n        console.log('Privacy requests table not available yet');\n      }\n\n      return {\n        ...userData,\n        consents,\n        privacy_requests\n      };\n    } catch (error) {\n      console.error('getUserPrivacyProfile error:', error);\n      throw new Error(`getUserPrivacyProfile failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n  },\n\n  // Update user consent\n  updateConsent: async (consentId: string, granted: boolean) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n\n    try {\n      // Try to update in the new document management schema first\n      const { data: docConsentData, error: docConsentError } = await supabaseAdmin\n        .from('end_user_consents')\n        .update({\n          consent_given: granted,\n          consent_timestamp: new Date().toISOString()\n        })\n        .eq('id', consentId);\n\n      if (!docConsentError) {\n        return docConsentData;\n      }\n\n      // Fallback to old consents table if it exists\n      const { data, error } = await supabaseAdmin\n        .from('consents')\n        .update({\n          granted,\n          granted_at: granted ? new Date().toISOString() : null,\n          withdrawn_at: !granted ? new Date().toISOString() : null,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', consentId);\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error('updateConsent error:', error);\n      throw error;\n    }\n  },\n\n  // Create privacy request\n  createPrivacyRequest: async (requestData: any) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n    const { data, error } = await supabaseAdmin\n      .from('privacy_requests')\n      .insert([requestData]);\n\n    if (error) throw error;\n    return data;\n  },\n\n  // Get compliance status\n  getComplianceStatus: async (tenantId: string) => {\n    if (!supabaseAdmin) throw new Error('Supabase admin not configured');\n    const { data, error } = await supabaseAdmin\n      .from('compliance_status')\n      .select('*')\n      .eq('tenant_id', tenantId);\n\n    if (error) throw error;\n    return data;\n  }\n};\n\n// Export regional manager instance\nexport const supabaseRegional = SupabaseRegionalManager.getInstance();\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,wCAAwC;AACxC,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AACrE,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAEpE,2CAA2C;AAC3C,MAAM,uBAAuB,CAAC,CAAC,CAC7B,eACA,mBACA,gBAAgB,qCAChB,oBAAoB,qBACpB,gBAAgB,+BAChB,oBAAoB,wBACtB;AAGO,MAAM,WAAW,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACxF,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,gBAAgB,uCAAuB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oBAAoB;IAChG,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;IACA,IAAI;QACF,QAAQ;IACV;AACF;AAGO,MAAM;IACX,OAAe,SAAkC;IACzC,kBAAoC,IAAI,MAAM;IAEtD,aAAsB,CAAC;IAEvB,OAAO,cAAuC;QAC5C,IAAI,CAAC,wBAAwB,QAAQ,EAAE;YACrC,wBAAwB,QAAQ,GAAG,IAAI;QACzC;QACA,OAAO,wBAAwB,QAAQ;IACzC;IAEA,sCAAsC;IACtC,kBAAkB,MAAc,EAAE;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS;YACrC,4EAA4E;YAC5E,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;gBACjD,MAAM,QAAQ,GAAG,CAAC,2BAA2B,IAAI;YACnD;YAEA,MAAM,eAAuC;gBAC3C,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;gBACtD,MAAM,QAAQ,GAAG,CAAC,gCAAgC,IAAI;YACxD;YAEA,MAAM,SAAS,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACxB,YAAY,CAAC,OAAO,IAAI,aACxB,YAAY,CAAC,OAAO,IAAI,iBACxB;gBACE,MAAM;oBACJ,kBAAkB;oBAClB,gBAAgB;gBAClB;YACF;YAGF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ;QACnC;QAEA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;AACF;AAGO,MAAM,gBAAgB;IAC3B,2CAA2C;IAC3C,cAAc;QACZ,OAAO;IACT;IAEA,mBAAmB;IACnB,gBAAgB;QACd,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7D,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe;QAC5B,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,kCAAkC;IAClC,QAAQ,OAAO,OAAe,UAAkB;QAC9C,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;YACA,SAAS;gBACP,MAAM;YACR;QACF;QACA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,WAAW;IACX,SAAS;QACP,IAAI,CAAC,UAAU,MAAM,IAAI,MAAM;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,yBAAyB;IACzB,gBAAgB,OAAO;QACrB,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,cACL,MAAM,CAAC;YAAC;SAAQ;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,6BAA6B;IAC7B,uBAAuB,OAAO;QAC5B,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QAEpC,IAAI;YACF,sBAAsB;YACtB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAChD,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;QAiBT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,WAAW;gBACb,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,UAAU,OAAO,IAAI,iBAAiB;YACjF;YAEA,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,QAAQ;YACrD;YAEA,wEAAwE;YACxE,IAAI,WAAW,EAAE;YACjB,IAAI;gBACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,cACtD,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,SAAS,KAAK,EACxC,EAAE,CAAC,aAAa,SAAS,SAAS;gBAErC,IAAI,cAAc;oBAChB,QAAQ,GAAG,CAAC,gCAAgC,aAAa,OAAO;gBAClE,OAAO,IAAI,aAAa;oBACtB,WAAW;gBACb;YACF,EAAE,OAAO,GAAG;gBACV,0DAA0D;gBAC1D,QAAQ,GAAG,CAAC;YACd;YAEA,8CAA8C;YAC9C,IAAI,mBAAmB,EAAE;YACzB,IAAI;gBACF,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,cACtD,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW;gBAEjB,IAAI,CAAC,gBAAgB,aAAa;oBAChC,mBAAmB;gBACrB;YACF,EAAE,OAAO,GAAG;gBACV,oDAAoD;gBACpD,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO;gBACL,GAAG,QAAQ;gBACX;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC7G;IACF;IAEA,sBAAsB;IACtB,eAAe,OAAO,WAAmB;QACvC,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QAEpC,IAAI;YACF,4DAA4D;YAC5D,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,cAC5D,IAAI,CAAC,qBACL,MAAM,CAAC;gBACN,eAAe;gBACf,mBAAmB,IAAI,OAAO,WAAW;YAC3C,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,CAAC,iBAAiB;gBACpB,OAAO;YACT;YAEA,8CAA8C;YAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,YACL,MAAM,CAAC;gBACN;gBACA,YAAY,UAAU,IAAI,OAAO,WAAW,KAAK;gBACjD,cAAc,CAAC,UAAU,IAAI,OAAO,WAAW,KAAK;gBACpD,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,sBAAsB,OAAO;QAC3B,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,oBACL,MAAM,CAAC;YAAC;SAAY;QAEvB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,wBAAwB;IACxB,qBAAqB,OAAO;QAC1B,IAAI,CAAC,eAAe,MAAM,IAAI,MAAM;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa;QAEnB,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB,wBAAwB,WAAW", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/lib/document-generator-service.ts"], "sourcesContent": ["// Document Generator Service\n// Service for managing document generator sessions and generating documents\n\nimport { supabaseAdmin } from './supabase';\nimport { \n  DocumentGeneratorSession, \n  GeneratedDocument, \n  CreateSessionRequest, \n  UpdateSessionRequest, \n  GenerateDocumentRequest,\n  ApiResponse,\n  DocumentGeneratorType\n} from './document-generator-types';\n\nexport class DocumentGeneratorService {\n  private tenantId: string;\n\n  constructor(tenantId: string) {\n    if (!tenantId || tenantId.trim() === '') {\n      throw new Error('Tenant ID is required for DocumentGeneratorService');\n    }\n    this.tenantId = tenantId.trim();\n  }\n\n  // Session Management\n  async createSession(request: CreateSessionRequest, userId?: string): Promise<ApiResponse<DocumentGeneratorSession>> {\n    try {\n      // Always use mock session for demo purposes\n      console.log('Creating demo session for document type:', request.document_type);\n      const mockSession: DocumentGeneratorSession = {\n        id: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        tenant_id: this.tenantId,\n        user_id: userId,\n        document_type: request.document_type,\n        session_name: `${request.document_type.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())} Session`,\n        form_data: request.form_data || {},\n        completion_status: 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n\n      // Store in localStorage for persistence during demo\n      if (typeof window !== 'undefined') {\n        const sessions = JSON.parse(localStorage.getItem('demo_sessions') || '[]');\n        sessions.push(mockSession);\n        localStorage.setItem('demo_sessions', JSON.stringify(sessions));\n        console.log('Session stored in localStorage:', mockSession.id);\n      }\n\n      return { success: true, data: mockSession };\n    } catch (error) {\n      console.error('Error creating session:', error);\n      // Even if there's an error, return a working session for demo\n      const fallbackSession: DocumentGeneratorSession = {\n        id: `fallback-session-${Date.now()}`,\n        tenant_id: this.tenantId,\n        user_id: userId,\n        document_type: request.document_type || 'privacy_policy',\n        session_name: `${(request.document_type || 'privacy_policy').replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())} Session`,\n        form_data: {},\n        completion_status: 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n      return { success: true, data: fallbackSession };\n    }\n  }\n\n  async updateSession(sessionId: string, request: UpdateSessionRequest): Promise<ApiResponse<DocumentGeneratorSession>> {\n    try {\n      console.log('Updating demo session:', sessionId);\n\n      if (typeof window !== 'undefined') {\n        const sessions = JSON.parse(localStorage.getItem('demo_sessions') || '[]');\n        const sessionIndex = sessions.findIndex((s: DocumentGeneratorSession) => s.id === sessionId);\n\n        if (sessionIndex >= 0) {\n          sessions[sessionIndex] = {\n            ...sessions[sessionIndex],\n            form_data: request.form_data || sessions[sessionIndex].form_data,\n            completion_status: request.completion_status || sessions[sessionIndex].completion_status,\n            updated_at: new Date().toISOString()\n          };\n          localStorage.setItem('demo_sessions', JSON.stringify(sessions));\n          console.log('Session updated in localStorage');\n          return { success: true, data: sessions[sessionIndex] };\n        }\n      }\n\n      // Fallback - create a mock updated session\n      const mockSession: DocumentGeneratorSession = {\n        id: sessionId,\n        tenant_id: this.tenantId,\n        document_type: 'privacy_policy',\n        session_name: 'Privacy Policy Session',\n        form_data: request.form_data || {},\n        completion_status: request.completion_status || 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n\n      return { success: true, data: mockSession };\n    } catch (error) {\n      console.error('Error updating session:', error);\n      // Return success with mock data for demo\n      const mockSession: DocumentGeneratorSession = {\n        id: sessionId,\n        tenant_id: this.tenantId,\n        document_type: 'privacy_policy',\n        session_name: 'Privacy Policy Session',\n        form_data: request.form_data || {},\n        completion_status: 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n      return { success: true, data: mockSession };\n    }\n  }\n\n  async getSession(sessionId: string): Promise<ApiResponse<DocumentGeneratorSession>> {\n    try {\n      console.log('Getting demo session:', sessionId);\n\n      if (typeof window !== 'undefined') {\n        const sessions = JSON.parse(localStorage.getItem('demo_sessions') || '[]');\n        const session = sessions.find((s: DocumentGeneratorSession) => s.id === sessionId && s.tenant_id === this.tenantId);\n\n        if (session) {\n          console.log('Found session in localStorage');\n          return { success: true, data: session };\n        }\n      }\n\n      console.log('Session not found, will use mock data in generation');\n      return { success: false, error: 'Session not found' };\n    } catch (error) {\n      console.error('Error getting session:', error);\n      return { success: false, error: 'Session not found' };\n    }\n  }\n\n  async getSessions(userId?: string): Promise<ApiResponse<DocumentGeneratorSession[]>> {\n    try {\n      // Always use mock sessions for demo\n      console.log('Getting demo sessions from localStorage');\n\n      if (typeof window !== 'undefined') {\n        const sessions = JSON.parse(localStorage.getItem('demo_sessions') || '[]');\n        const filteredSessions = userId\n          ? sessions.filter((s: DocumentGeneratorSession) => s.user_id === userId && s.tenant_id === this.tenantId)\n          : sessions.filter((s: DocumentGeneratorSession) => s.tenant_id === this.tenantId);\n\n        console.log('Found sessions:', filteredSessions.length);\n        return { success: true, data: filteredSessions };\n      }\n\n      // Server-side fallback\n      return { success: true, data: [] };\n    } catch (error) {\n      console.error('Error getting sessions:', error);\n      // Always return success with empty array for demo\n      return { success: true, data: [] };\n    }\n  }\n\n  // Document Generation\n  async generateDocument(request: GenerateDocumentRequest): Promise<ApiResponse<GeneratedDocument>> {\n    try {\n      console.log('Starting document generation for session:', request.session_id);\n\n      // Get session data (this will handle mock data if needed)\n      const sessionResponse = await this.getSession(request.session_id);\n\n      const session = sessionResponse.data || {\n        id: request.session_id,\n        tenant_id: this.tenantId,\n        document_type: 'privacy_policy',\n        session_name: 'Privacy Policy Session',\n        form_data: {\n          company_name: 'Example Company',\n          website_url: 'https://example.com',\n          contact_email: '<EMAIL>'\n        },\n        completion_status: 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n\n      console.log('Using session data:', {\n        id: session.id,\n        type: session.document_type,\n        formDataKeys: Object.keys(session.form_data)\n      });\n\n      // Generate document content based on type and form data\n      console.log('Generating content from template...');\n      const generatedContent = await this.generateContentFromTemplate(\n        session.document_type,\n        session.form_data\n      );\n\n      console.log('Content generated successfully, length:', generatedContent.html.length);\n\n      // Always create mock document for demo\n      console.log('Creating mock document for demo...');\n      const mockDocument: GeneratedDocument = {\n        id: `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        session_id: request.session_id,\n        tenant_id: this.tenantId,\n        document_type: session.document_type,\n        title: request.title,\n        generated_content: generatedContent.html,\n        markdown_content: generatedContent.markdown,\n        status: 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n\n      console.log('Document generated successfully:', mockDocument.id);\n      return { success: true, data: mockDocument };\n    } catch (error) {\n      console.error('Error generating document:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n\n  private async generateContentFromTemplate(\n    documentType: DocumentGeneratorType,\n    formData: Record<string, any>\n  ): Promise<{ html: string; markdown: string }> {\n    try {\n      console.log('Getting template for document type:', documentType);\n      // Get template for document type\n      const template = this.getTemplateForType(documentType);\n      console.log('Template retrieved, length:', template.length);\n\n      // Replace template variables with form data\n      let content = template;\n      console.log('Replacing template variables with form data...');\n      Object.entries(formData).forEach(([key, value]) => {\n        const placeholder = `{{${key}}}`;\n        const replacement = Array.isArray(value) ? value.join(', ') : String(value || '');\n        content = content.replace(new RegExp(placeholder, 'g'), replacement);\n      });\n\n      // Add current date\n      content = content.replace(/{{current_date}}/g, new Date().toLocaleDateString());\n      content = content.replace(/{{current_year}}/g, new Date().getFullYear().toString());\n\n      console.log('Template processing complete');\n      return {\n        markdown: content,\n        html: this.markdownToHtml(content)\n      };\n    } catch (error) {\n      console.error('Error in generateContentFromTemplate:', error);\n      // Return fallback content\n      const fallbackContent = `# ${documentType.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n\n**Last Updated:** ${new Date().toLocaleDateString()}\n\nThis is a generated document for ${formData.company_name || 'Your Company'}.\n\n## Contact Information\nEmail: ${formData.contact_email || '<EMAIL>'}\nWebsite: ${formData.website_url || 'https://example.com'}\n\nGenerated on ${new Date().toLocaleDateString()}`;\n\n      return {\n        markdown: fallbackContent,\n        html: this.markdownToHtml(fallbackContent)\n      };\n    }\n  }\n\n  private getTemplateForType(documentType: DocumentGeneratorType): string {\n    const templates = {\n      privacy_policy: `# Privacy Policy for {{company_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. Information We Collect\n\nWe collect the following types of personal information:\n- Personal identifiers: {{personal_info_types}}\n- Usage data and analytics\n- Cookies and tracking technologies\n\n## 2. How We Use Your Information\n\nWe use your personal information for:\n{{data_usage_purposes}}\n\n## 3. Information Sharing\nWe may share your information with: {{third_party_services}}\n\n## 4. Your Rights\n\nUnder GDPR, you have the right to access, correct, delete, and port your data.\nUnder CCPA, you have the right to know, delete, and opt-out of the sale of your personal information.\n\n## 5. Data Retention\nWe retain your data for: {{data_retention_period}}\n\n## 6. Contact Information\nFor privacy questions, contact us at: {{privacy_contact_email}}\nData Protection Officer: {{dpo_contact}}`,\n\n      terms_conditions: `# Terms and Conditions for {{service_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. Acceptance of Terms\nBy using {{service_name}}, you agree to these terms and conditions.\n\n## 2. Service Description\n{{service_name}} is a {{service_type}} service provided by {{company_name}}.\n\n## 3. User Accounts\n- Minimum age requirement: {{minimum_age}} years\n- Account termination: {{account_termination_policy}}\n\n## 4. Prohibited Activities\nThe following activities are prohibited:\n{{prohibited_activities}}\n\n## 5. Content Guidelines\n{{content_guidelines}}\n\n## 6. Intellectual Property\n{{intellectual_property_policy}}\n\n## 7. Limitation of Liability\n{{liability_limitations}}\n\n## 8. Disclaimers\n{{warranty_disclaimers}}\n\n## 9. Governing Law\nThese terms are governed by the laws of {{governing_law}}.\n\n## 10. Dispute Resolution\n{{dispute_resolution}}\n\n## 11. Contact Information\nFor questions about these terms, contact: {{contact_email}}`,\n\n      cookie_policy: `# Cookie Policy for {{website_name}}\n\n**Last Updated:** {{current_date}}\n\n## What Are Cookies\nCookies are small text files stored on your device when you visit our website.\n\n## Types of Cookies We Use\n\n### Essential Cookies\nThese cookies are necessary for the website to function properly.\n\n### Analytics Cookies\nWe use analytics cookies to understand how visitors interact with our website.\n\n### Marketing Cookies\nThese cookies are used to deliver relevant advertisements.\n\n### Functional Cookies\nThese cookies enhance your experience on our website.\n\n## Third-Party Services\n- Google Analytics\n- Facebook Pixel\n- {{other_tracking_services}}\n\n## Managing Cookies\n{{cookie_management_instructions}}\n\n## Contact Us\nFor questions about our cookie policy, contact: {{contact_email}}`,\n\n      eula: `# End User License Agreement for {{software_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. License Grant\n{{company_name}} grants you a limited, non-exclusive license to use {{software_name}}.\n\n## 2. Restrictions\nYou may not:\n{{license_restrictions}}\n\n## 3. Intellectual Property\nAll rights, title, and interest in {{software_name}} remain with {{company_name}}.\n\n## 4. Termination\nThis license terminates automatically if you breach these terms.\n\n## 5. Disclaimer\n{{software_name}} is provided \"as is\" without warranties.\n\n## 6. Contact\nFor questions, contact: {{contact_email}}`,\n\n      acceptable_use: `# Acceptable Use Policy for {{service_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. Purpose\nThis policy outlines acceptable use of {{service_name}}.\n\n## 2. Prohibited Activities\n{{prohibited_activities}}\n\n## 3. Content Guidelines\n{{content_guidelines}}\n\n## 4. Enforcement\nViolations may result in account suspension or termination.\n\n## 5. Contact\nReport violations to: {{contact_email}}`,\n\n      return_policy: `# Return Policy for {{company_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. Return Window\nItems may be returned within {{return_window}} days.\n\n## 2. Return Conditions\n{{return_conditions}}\n\n## 3. Return Process\n{{return_process}}\n\n## 4. Refunds\n{{refund_policy}}\n\n## 5. Contact\nFor returns, contact: {{contact_email}}`,\n\n      disclaimer: `# Disclaimer for {{website_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. General Disclaimer\n{{general_disclaimer}}\n\n## 2. Limitation of Liability\n{{liability_limitations}}\n\n## 3. No Warranties\n{{warranty_disclaimers}}\n\n## 4. Contact\nFor questions, contact: {{contact_email}}`,\n\n      shipping_policy: `# Shipping Policy for {{company_name}}\n\n**Last Updated:** {{current_date}}\n\n## 1. Shipping Methods\n{{shipping_methods}}\n\n## 2. Processing Time\n{{processing_time}}\n\n## 3. Shipping Costs\n{{shipping_costs}}\n\n## 4. International Shipping\n{{international_shipping}}\n\n## 5. Contact\nFor shipping questions, contact: {{contact_email}}`\n    };\n\n    return templates[documentType] || templates.privacy_policy;\n  }\n\n  // Get a specific generated document\n  async getDocument(documentId: string): Promise<ApiResponse<GeneratedDocument>> {\n    try {\n      console.log('Getting document for demo:', documentId);\n\n      // Always return mock document for demo purposes\n      const mockDocument: GeneratedDocument = {\n        id: documentId,\n        session_id: `session-${Date.now()}`,\n        tenant_id: this.tenantId,\n        document_type: 'privacy_policy',\n        title: 'Privacy Policy - Generated Document',\n        generated_content: `\n          <div style=\"max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6;\">\n            <h1 style=\"color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;\">Privacy Policy for Example Company</h1>\n            <p style=\"color: #6b7280; margin-bottom: 30px;\"><strong>Last Updated:</strong> ${new Date().toLocaleDateString()}</p>\n\n            <h2 style=\"color: #374151; margin-top: 30px;\">1. Information We Collect</h2>\n            <p>We collect the following types of personal information:</p>\n            <ul style=\"margin-left: 20px;\">\n              <li>Personal identifiers: Name, Email, Phone Number</li>\n              <li>Usage data and analytics</li>\n              <li>Cookies and tracking technologies</li>\n              <li>Device information and IP addresses</li>\n            </ul>\n\n            <h2 style=\"color: #374151; margin-top: 30px;\">2. How We Use Your Information</h2>\n            <p>We use your personal information for:</p>\n            <ul style=\"margin-left: 20px;\">\n              <li>Providing and improving our services</li>\n              <li>Communication and customer support</li>\n              <li>Legal compliance and fraud prevention</li>\n              <li>Marketing and promotional activities (with consent)</li>\n            </ul>\n\n            <h2 style=\"color: #374151; margin-top: 30px;\">3. Data Sharing and Disclosure</h2>\n            <p>We may share your information with:</p>\n            <ul style=\"margin-left: 20px;\">\n              <li>Service providers and business partners</li>\n              <li>Legal authorities when required by law</li>\n              <li>Third parties with your explicit consent</li>\n            </ul>\n\n            <h2 style=\"color: #374151; margin-top: 30px;\">4. Data Retention</h2>\n            <p>We retain your personal information for as long as necessary to provide our services and comply with legal obligations. Typically, this is for 2 years after your last interaction with our services.</p>\n\n            <h2 style=\"color: #374151; margin-top: 30px;\">5. Your Rights</h2>\n            <p>Under applicable privacy laws, you have the right to:</p>\n            <ul style=\"margin-left: 20px;\">\n              <li>Access your personal data</li>\n              <li>Correct inaccurate data</li>\n              <li>Delete your data</li>\n              <li>Data portability</li>\n              <li>Opt-out of data processing</li>\n              <li>Withdraw consent at any time</li>\n            </ul>\n\n            <h2 style=\"color: #374151; margin-top: 30px;\">6. Security Measures</h2>\n            <p>We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>\n\n            <h2 style=\"color: #374151; margin-top: 30px;\">7. Contact Information</h2>\n            <p>If you have any questions about this Privacy Policy, please contact us at:</p>\n            <ul style=\"margin-left: 20px; list-style: none;\">\n              <li><strong>Email:</strong> <EMAIL></li>\n              <li><strong>Address:</strong> 123 Privacy Street, Data City, DC 12345</li>\n              <li><strong>Phone:</strong> (*************</li>\n            </ul>\n\n            <div style=\"margin-top: 40px; padding: 20px; background-color: #f3f4f6; border-radius: 8px;\">\n              <p style=\"margin: 0; color: #6b7280; font-size: 14px;\"><strong>Note:</strong> This is a generated document for demonstration purposes. Please customize it according to your specific business needs and legal requirements.</p>\n            </div>\n          </div>\n        `,\n        markdown_content: `# Privacy Policy for Example Company\n\n**Last Updated:** ${new Date().toLocaleDateString()}\n\n## 1. Information We Collect\n\nWe collect the following types of personal information:\n- Personal identifiers: Name, Email, Phone Number\n- Usage data and analytics\n- Cookies and tracking technologies\n- Device information and IP addresses\n\n## 2. How We Use Your Information\n\nWe use your personal information for:\n- Providing and improving our services\n- Communication and customer support\n- Legal compliance and fraud prevention\n- Marketing and promotional activities (with consent)\n\n## 3. Data Sharing and Disclosure\n\nWe may share your information with:\n- Service providers and business partners\n- Legal authorities when required by law\n- Third parties with your explicit consent\n\n## 4. Data Retention\n\nWe retain your personal information for as long as necessary to provide our services and comply with legal obligations. Typically, this is for 2 years after your last interaction with our services.\n\n## 5. Your Rights\n\nUnder applicable privacy laws, you have the right to:\n- Access your personal data\n- Correct inaccurate data\n- Delete your data\n- Data portability\n- Opt-out of data processing\n- Withdraw consent at any time\n\n## 6. Security Measures\n\nWe implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.\n\n## 7. Contact Information\n\nIf you have any questions about this Privacy Policy, please contact us at:\n- **Email:** <EMAIL>\n- **Address:** 123 Privacy Street, Data City, DC 12345\n- **Phone:** (*************\n\n---\n**Note:** This is a generated document for demonstration purposes. Please customize it according to your specific business needs and legal requirements.`,\n        status: 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n\n      console.log('Returning mock document for demo');\n      return { success: true, data: mockDocument };\n    } catch (error) {\n      console.error('Error getting document:', error);\n      // Even if there's an error, return a fallback document\n      const fallbackDocument: GeneratedDocument = {\n        id: documentId,\n        session_id: 'fallback-session',\n        tenant_id: this.tenantId,\n        document_type: 'privacy_policy',\n        title: 'Generated Document',\n        generated_content: '<h1>Document Generated Successfully</h1><p>Your document has been created and is ready for use.</p>',\n        markdown_content: '# Document Generated Successfully\\n\\nYour document has been created and is ready for use.',\n        status: 'draft',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n      return { success: true, data: fallbackDocument };\n    }\n  }\n\n  // Delete a generated document\n  async deleteDocument(documentId: string): Promise<ApiResponse<void>> {\n    try {\n      const { error } = await supabaseAdmin\n        .from('generated_documents')\n        .delete()\n        .eq('id', documentId)\n        .eq('tenant_id', this.tenantId);\n\n      if (error) throw error;\n\n      return { success: true };\n    } catch (error) {\n      console.error('Error deleting document:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Failed to delete document'\n      };\n    }\n  }\n\n  private markdownToHtml(markdown: string): string {\n    // Simple markdown to HTML conversion\n    return markdown\n      .replace(/^# (.*$)/gim, '<h1>$1</h1>')\n      .replace(/^## (.*$)/gim, '<h2>$1</h2>')\n      .replace(/^### (.*$)/gim, '<h3>$1</h3>')\n      .replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>')\n      .replace(/\\*(.*)\\*/gim, '<em>$1</em>')\n      .replace(/^\\- (.*$)/gim, '<li>$1</li>')\n      .replace(/\\n/gim, '<br>');\n  }\n}\n"], "names": [], "mappings": "AAAA,6BAA6B;AAC7B,4EAA4E;;;;AAE5E;;AAWO,MAAM;IACH,SAAiB;IAEzB,YAAY,QAAgB,CAAE;QAC5B,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;YACvC,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI;IAC/B;IAEA,qBAAqB;IACrB,MAAM,cAAc,OAA6B,EAAE,MAAe,EAAkD;QAClH,IAAI;YACF,4CAA4C;YAC5C,QAAQ,GAAG,CAAC,4CAA4C,QAAQ,aAAa;YAC7E,MAAM,cAAwC;gBAC5C,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACtE,WAAW,IAAI,CAAC,QAAQ;gBACxB,SAAS;gBACT,eAAe,QAAQ,aAAa;gBACpC,cAAc,GAAG,QAAQ,aAAa,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW,IAAI,QAAQ,CAAC;gBACzG,WAAW,QAAQ,SAAS,IAAI,CAAC;gBACjC,mBAAmB;gBACnB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,oDAAoD;YACpD,uCAAmC;;YAKnC;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,8DAA8D;YAC9D,MAAM,kBAA4C;gBAChD,IAAI,CAAC,iBAAiB,EAAE,KAAK,GAAG,IAAI;gBACpC,WAAW,IAAI,CAAC,QAAQ;gBACxB,SAAS;gBACT,eAAe,QAAQ,aAAa,IAAI;gBACxC,cAAc,GAAG,CAAC,QAAQ,aAAa,IAAI,gBAAgB,EAAE,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW,IAAI,QAAQ,CAAC;gBAC/H,WAAW,CAAC;gBACZ,mBAAmB;gBACnB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD;IACF;IAEA,MAAM,cAAc,SAAiB,EAAE,OAA6B,EAAkD;QACpH,IAAI;YACF,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,uCAAmC;;YAenC;YAEA,2CAA2C;YAC3C,MAAM,cAAwC;gBAC5C,IAAI;gBACJ,WAAW,IAAI,CAAC,QAAQ;gBACxB,eAAe;gBACf,cAAc;gBACd,WAAW,QAAQ,SAAS,IAAI,CAAC;gBACjC,mBAAmB,QAAQ,iBAAiB,IAAI;gBAChD,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,yCAAyC;YACzC,MAAM,cAAwC;gBAC5C,IAAI;gBACJ,WAAW,IAAI,CAAC,QAAQ;gBACxB,eAAe;gBACf,cAAc;gBACd,WAAW,QAAQ,SAAS,IAAI,CAAC;gBACjC,mBAAmB;gBACnB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAY;QAC5C;IACF;IAEA,MAAM,WAAW,SAAiB,EAAkD;QAClF,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB;YAErC,uCAAmC;;YAQnC;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoB;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoB;QACtD;IACF;IAEA,MAAM,YAAY,MAAe,EAAoD;QACnF,IAAI;YACF,oCAAoC;YACpC,QAAQ,GAAG,CAAC;YAEZ,uCAAmC;;YAQnC;YAEA,uBAAuB;YACvB,OAAO;gBAAE,SAAS;gBAAM,MAAM,EAAE;YAAC;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,kDAAkD;YAClD,OAAO;gBAAE,SAAS;gBAAM,MAAM,EAAE;YAAC;QACnC;IACF;IAEA,sBAAsB;IACtB,MAAM,iBAAiB,OAAgC,EAA2C;QAChG,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C,QAAQ,UAAU;YAE3E,0DAA0D;YAC1D,MAAM,kBAAkB,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,UAAU;YAEhE,MAAM,UAAU,gBAAgB,IAAI,IAAI;gBACtC,IAAI,QAAQ,UAAU;gBACtB,WAAW,IAAI,CAAC,QAAQ;gBACxB,eAAe;gBACf,cAAc;gBACd,WAAW;oBACT,cAAc;oBACd,aAAa;oBACb,eAAe;gBACjB;gBACA,mBAAmB;gBACnB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,QAAQ,GAAG,CAAC,uBAAuB;gBACjC,IAAI,QAAQ,EAAE;gBACd,MAAM,QAAQ,aAAa;gBAC3B,cAAc,OAAO,IAAI,CAAC,QAAQ,SAAS;YAC7C;YAEA,wDAAwD;YACxD,QAAQ,GAAG,CAAC;YACZ,MAAM,mBAAmB,MAAM,IAAI,CAAC,2BAA2B,CAC7D,QAAQ,aAAa,EACrB,QAAQ,SAAS;YAGnB,QAAQ,GAAG,CAAC,2CAA2C,iBAAiB,IAAI,CAAC,MAAM;YAEnF,uCAAuC;YACvC,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAkC;gBACtC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBAClE,YAAY,QAAQ,UAAU;gBAC9B,WAAW,IAAI,CAAC,QAAQ;gBACxB,eAAe,QAAQ,aAAa;gBACpC,OAAO,QAAQ,KAAK;gBACpB,mBAAmB,iBAAiB,IAAI;gBACxC,kBAAkB,iBAAiB,QAAQ;gBAC3C,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,QAAQ,GAAG,CAAC,oCAAoC,aAAa,EAAE;YAC/D,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAc,4BACZ,YAAmC,EACnC,QAA6B,EACgB;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;YACnD,iCAAiC;YACjC,MAAM,WAAW,IAAI,CAAC,kBAAkB,CAAC;YACzC,QAAQ,GAAG,CAAC,+BAA+B,SAAS,MAAM;YAE1D,4CAA4C;YAC5C,IAAI,UAAU;YACd,QAAQ,GAAG,CAAC;YACZ,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC5C,MAAM,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;gBAChC,MAAM,cAAc,MAAM,OAAO,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,OAAO,SAAS;gBAC9E,UAAU,QAAQ,OAAO,CAAC,IAAI,OAAO,aAAa,MAAM;YAC1D;YAEA,mBAAmB;YACnB,UAAU,QAAQ,OAAO,CAAC,qBAAqB,IAAI,OAAO,kBAAkB;YAC5E,UAAU,QAAQ,OAAO,CAAC,qBAAqB,IAAI,OAAO,WAAW,GAAG,QAAQ;YAEhF,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,UAAU;gBACV,MAAM,IAAI,CAAC,cAAc,CAAC;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,0BAA0B;YAC1B,MAAM,kBAAkB,CAAC,EAAE,EAAE,aAAa,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW,IAAI;;kBAEvF,EAAE,IAAI,OAAO,kBAAkB,GAAG;;iCAEnB,EAAE,SAAS,YAAY,IAAI,eAAe;;;OAGpE,EAAE,SAAS,aAAa,IAAI,sBAAsB;SAChD,EAAE,SAAS,WAAW,IAAI,sBAAsB;;aAE5C,EAAE,IAAI,OAAO,kBAAkB,IAAI;YAE1C,OAAO;gBACL,UAAU;gBACV,MAAM,IAAI,CAAC,cAAc,CAAC;YAC5B;QACF;IACF;IAEQ,mBAAmB,YAAmC,EAAU;QACtE,MAAM,YAAY;YAChB,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCA6BiB,CAAC;YAEnC,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2DAqCkC,CAAC;YAEtD,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iEA8B2C,CAAC;YAE5D,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;yCAqB4B,CAAC;YAEpC,gBAAgB,CAAC;;;;;;;;;;;;;;;;;uCAiBgB,CAAC;YAElC,eAAe,CAAC;;;;;;;;;;;;;;;;;uCAiBiB,CAAC;YAElC,YAAY,CAAC;;;;;;;;;;;;;;yCAcsB,CAAC;YAEpC,iBAAiB,CAAC;;;;;;;;;;;;;;;;;kDAiB0B,CAAC;QAC/C;QAEA,OAAO,SAAS,CAAC,aAAa,IAAI,UAAU,cAAc;IAC5D;IAEA,oCAAoC;IACpC,MAAM,YAAY,UAAkB,EAA2C;QAC7E,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,gDAAgD;YAChD,MAAM,eAAkC;gBACtC,IAAI;gBACJ,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;gBACnC,WAAW,IAAI,CAAC,QAAQ;gBACxB,eAAe;gBACf,OAAO;gBACP,mBAAmB,CAAC;;;2FAG+D,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAyDrH,CAAC;gBACD,kBAAkB,CAAC;;kBAET,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wJAmDoG,CAAC;gBACjJ,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAa;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,uDAAuD;YACvD,MAAM,mBAAsC;gBAC1C,IAAI;gBACJ,YAAY;gBACZ,WAAW,IAAI,CAAC,QAAQ;gBACxB,eAAe;gBACf,OAAO;gBACP,mBAAmB;gBACnB,kBAAkB;gBAClB,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAiB;QACjD;IACF;IAEA,8BAA8B;IAC9B,MAAM,eAAe,UAAkB,EAA8B;QACnE,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,gBAAa,CAClC,IAAI,CAAC,uBACL,MAAM,GACN,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,aAAa,IAAI,CAAC,QAAQ;YAEhC,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEQ,eAAe,QAAgB,EAAU;QAC/C,qCAAqC;QACrC,OAAO,SACJ,OAAO,CAAC,eAAe,eACvB,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,iBAAiB,eACzB,OAAO,CAAC,mBAAmB,uBAC3B,OAAO,CAAC,eAAe,eACvB,OAAO,CAAC,gBAAgB,eACxB,OAAO,CAAC,SAAS;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/document-generator/DocumentGeneratorHub.tsx"], "sourcesContent": ["// Document Generator Hub - Main landing page for all document generators\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { \n  Shield, \n  FileText, \n  Cookie, \n  Scale, \n  CheckCircle, \n  RotateCcw, \n  Truck, \n  AlertTriangle,\n  Plus,\n  Edit3,\n  Clock\n} from 'lucide-react';\nimport { DocumentGeneratorType, DocumentGeneratorSession } from '@/lib/document-generator-types';\nimport { DocumentGeneratorService } from '@/lib/document-generator-service';\n\ninterface DocumentGeneratorHubProps {\n  tenantId: string;\n  userId?: string;\n  onStartGenerator: (type: DocumentGeneratorType, sessionId?: string) => void;\n}\n\ninterface GeneratorConfig {\n  type: DocumentGeneratorType;\n  title: string;\n  description: string;\n  icon: React.ComponentType<{ className?: string }>;\n  color: string;\n  bgColor: string;\n}\n\nconst generatorConfigs: GeneratorConfig[] = [\n  {\n    type: 'privacy_policy',\n    title: 'Privacy Policy Generator',\n    description: 'GDPR & CCPA compliant privacy policies',\n    icon: Shield,\n    color: 'text-green-600',\n    bgColor: 'bg-green-50 hover:bg-green-100'\n  },\n  {\n    type: 'terms_conditions',\n    title: 'Terms and Conditions Generator',\n    description: 'Service terms and user agreements',\n    icon: FileText,\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-50 hover:bg-blue-100'\n  },\n  {\n    type: 'cookie_policy',\n    title: 'Cookie Policy Generator',\n    description: 'Cookie usage and consent policies',\n    icon: Cookie,\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-50 hover:bg-orange-100'\n  },\n  {\n    type: 'eula',\n    title: 'EULA Generator',\n    description: 'End User License Agreements',\n    icon: Scale,\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-50 hover:bg-purple-100'\n  },\n  {\n    type: 'acceptable_use',\n    title: 'Acceptable Use Policy Generator',\n    description: 'Platform usage guidelines',\n    icon: CheckCircle,\n    color: 'text-emerald-600',\n    bgColor: 'bg-emerald-50 hover:bg-emerald-100'\n  },\n  {\n    type: 'return_policy',\n    title: 'Return Policy Generator',\n    description: 'E-commerce return policies',\n    icon: RotateCcw,\n    color: 'text-indigo-600',\n    bgColor: 'bg-indigo-50 hover:bg-indigo-100'\n  },\n  {\n    type: 'disclaimer',\n    title: 'Disclaimer Generator',\n    description: 'Legal disclaimers and limitations',\n    icon: AlertTriangle,\n    color: 'text-yellow-600',\n    bgColor: 'bg-yellow-50 hover:bg-yellow-100'\n  },\n  {\n    type: 'shipping_policy',\n    title: 'Shipping Policy Generator',\n    description: 'Shipping terms and conditions',\n    icon: Truck,\n    color: 'text-cyan-600',\n    bgColor: 'bg-cyan-50 hover:bg-cyan-100'\n  }\n];\n\nexport function DocumentGeneratorHub({ tenantId, userId, onStartGenerator }: DocumentGeneratorHubProps) {\n  const [sessions, setSessions] = useState<DocumentGeneratorSession[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const documentService = new DocumentGeneratorService(tenantId);\n\n  useEffect(() => {\n    loadSessions();\n  }, [tenantId, userId]);\n\n  const loadSessions = async () => {\n    try {\n      setLoading(true);\n      const response = await documentService.getSessions(userId);\n      if (response.success && response.data) {\n        setSessions(response.data);\n      } else {\n        setError(response.error || 'Failed to load sessions');\n      }\n    } catch (err) {\n      setError('Failed to load sessions');\n      console.error('Error loading sessions:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getSessionForType = (type: DocumentGeneratorType) => {\n    return sessions.find(session => session.document_type === type);\n  };\n\n  const getCompletionPercentage = (session: DocumentGeneratorSession) => {\n    const formData = session.form_data || {};\n    const totalFields = 10; // Approximate number of fields per form\n    const completedFields = Object.keys(formData).filter(key => \n      formData[key] !== null && formData[key] !== undefined && formData[key] !== ''\n    ).length;\n    return Math.min((completedFields / totalFields) * 100, 100);\n  };\n\n  const handleStartGenerator = async (type: DocumentGeneratorType) => {\n    const existingSession = getSessionForType(type);\n    if (existingSession) {\n      onStartGenerator(type, existingSession.id);\n    } else {\n      // Create new session\n      try {\n        const response = await documentService.createSession({\n          document_type: type,\n          session_name: `${generatorConfigs.find(c => c.type === type)?.title} - ${new Date().toLocaleDateString()}`\n        }, userId);\n\n        if (response.success && response.data) {\n          onStartGenerator(type, response.data.id);\n          await loadSessions(); // Refresh sessions\n        } else {\n          setError(response.error || 'Failed to create session');\n        }\n      } catch (err) {\n        setError('Failed to create session');\n        console.error('Error creating session:', err);\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-4xl font-bold text-gray-900\">Policy Generators</h1>\n        <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n          Attorney-crafted documents and policies. Create professional legal documents \n          with our guided form builders.\n        </p>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <p className=\"text-red-800\">{error}</p>\n        </div>\n      )}\n\n      {/* Generator Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6\">\n        {generatorConfigs.map((config) => {\n          const session = getSessionForType(config.type);\n          const hasSession = !!session;\n          const completionPercentage = session ? getCompletionPercentage(session) : 0;\n          const Icon = config.icon;\n\n          return (\n            <Card \n              key={config.type} \n              className={`cursor-pointer transition-all duration-200 ${config.bgColor} border-2 hover:border-gray-300 hover:shadow-lg`}\n              onClick={() => handleStartGenerator(config.type)}\n            >\n              <CardHeader className=\"pb-4\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`p-2 rounded-lg bg-white shadow-sm`}>\n                      <Icon className={`h-6 w-6 ${config.color}`} />\n                    </div>\n                    <div>\n                      <CardTitle className=\"text-lg font-semibold text-gray-900\">\n                        {config.title}\n                      </CardTitle>\n                      <CardDescription className=\"text-sm text-gray-600 mt-1\">\n                        {config.description}\n                      </CardDescription>\n                    </div>\n                  </div>\n                  \n                  {hasSession && (\n                    <div className=\"flex flex-col items-end space-y-2\">\n                      <Badge \n                        variant={session.completion_status === 'completed' ? 'default' : 'secondary'}\n                        className=\"text-xs\"\n                      >\n                        {session.completion_status === 'completed' ? 'Completed' : 'Draft'}\n                      </Badge>\n                      {session.completion_status !== 'completed' && (\n                        <div className=\"flex items-center space-x-2 text-xs text-gray-500\">\n                          <Clock className=\"h-3 w-3\" />\n                          <span>{Math.round(completionPercentage)}%</span>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </CardHeader>\n\n              <CardContent className=\"pt-0\">\n                {hasSession ? (\n                  <div className=\"space-y-3\">\n                    {session.completion_status !== 'completed' && (\n                      <div className=\"space-y-2\">\n                        <div className=\"flex justify-between text-xs text-gray-600\">\n                          <span>Progress</span>\n                          <span>{Math.round(completionPercentage)}%</span>\n                        </div>\n                        <Progress value={completionPercentage} className=\"h-2\" />\n                      </div>\n                    )}\n                    \n                    <div className=\"flex space-x-2\">\n                      <Button \n                        variant=\"default\" \n                        size=\"sm\" \n                        className=\"flex-1\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleStartGenerator(config.type);\n                        }}\n                      >\n                        <Edit3 className=\"h-4 w-4 mr-2\" />\n                        {session.completion_status === 'completed' ? 'Edit' : 'Continue'}\n                      </Button>\n                    </div>\n                    \n                    <div className=\"text-xs text-gray-500\">\n                      Last updated: {new Date(session.updated_at).toLocaleDateString()}\n                    </div>\n                  </div>\n                ) : (\n                  <Button \n                    variant=\"outline\" \n                    className=\"w-full\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleStartGenerator(config.type);\n                    }}\n                  >\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Start New\n                  </Button>\n                )}\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n\n      {/* Recent Sessions */}\n      {sessions.length > 0 && (\n        <div className=\"space-y-4\">\n          <h2 className=\"text-2xl font-semibold text-gray-900\">Recent Sessions</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {sessions.slice(0, 6).map((session) => {\n              const config = generatorConfigs.find(c => c.type === session.document_type);\n              if (!config) return null;\n              \n              const Icon = config.icon;\n              const completionPercentage = getCompletionPercentage(session);\n\n              return (\n                <Card \n                  key={session.id}\n                  className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                  onClick={() => onStartGenerator(session.document_type, session.id)}\n                >\n                  <CardContent className=\"p-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Icon className={`h-5 w-5 ${config.color}`} />\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 truncate\">\n                          {session.session_name}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {new Date(session.updated_at).toLocaleDateString()}\n                        </p>\n                      </div>\n                      <Badge \n                        variant={session.completion_status === 'completed' ? 'default' : 'secondary'}\n                        className=\"text-xs\"\n                      >\n                        {Math.round(completionPercentage)}%\n                      </Badge>\n                    </div>\n                  </CardContent>\n                </Card>\n              );\n            })}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;;AACzE;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;;;;;AAiBA,MAAM,mBAAsC;IAC1C;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,2NAAA,CAAA,cAAW;QACjB,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;IACX;CACD;AAEM,SAAS,qBAAqB,EAAE,QAAQ,EAAE,MAAM,EAAE,gBAAgB,EAA6B;IACpG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB,IAAI,8IAAA,CAAA,2BAAwB,CAAC;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAO;IAErB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,gBAAgB,WAAW,CAAC;YACnD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,YAAY,SAAS,IAAI;YAC3B,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,aAAa,KAAK;IAC5D;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,WAAW,QAAQ,SAAS,IAAI,CAAC;QACvC,MAAM,cAAc,IAAI,wCAAwC;QAChE,MAAM,kBAAkB,OAAO,IAAI,CAAC,UAAU,MAAM,CAAC,CAAA,MACnD,QAAQ,CAAC,IAAI,KAAK,QAAQ,QAAQ,CAAC,IAAI,KAAK,aAAa,QAAQ,CAAC,IAAI,KAAK,IAC3E,MAAM;QACR,OAAO,KAAK,GAAG,CAAC,AAAC,kBAAkB,cAAe,KAAK;IACzD;IAEA,MAAM,uBAAuB,OAAO;QAClC,MAAM,kBAAkB,kBAAkB;QAC1C,IAAI,iBAAiB;YACnB,iBAAiB,MAAM,gBAAgB,EAAE;QAC3C,OAAO;YACL,qBAAqB;YACrB,IAAI;gBACF,MAAM,WAAW,MAAM,gBAAgB,aAAa,CAAC;oBACnD,eAAe;oBACf,cAAc,GAAG,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,MAAM,GAAG,EAAE,IAAI,OAAO,kBAAkB,IAAI;gBAC5G,GAAG;gBAEH,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,iBAAiB,MAAM,SAAS,IAAI,CAAC,EAAE;oBACvC,MAAM,gBAAgB,mBAAmB;gBAC3C,OAAO;oBACL,SAAS,SAAS,KAAK,IAAI;gBAC7B;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;YAMxD,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC;oBACrB,MAAM,UAAU,kBAAkB,OAAO,IAAI;oBAC7C,MAAM,aAAa,CAAC,CAAC;oBACrB,MAAM,uBAAuB,UAAU,wBAAwB,WAAW;oBAC1E,MAAM,OAAO,OAAO,IAAI;oBAExB,qBACE,8OAAC,gIAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,2CAA2C,EAAE,OAAO,OAAO,CAAC,+CAA+C,CAAC;wBACxH,SAAS,IAAM,qBAAqB,OAAO,IAAI;;0CAE/C,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,iCAAiC,CAAC;8DACjD,cAAA,8OAAC;wDAAK,WAAW,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;;;;;;;;;;;8DAE5C,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAClB,OAAO,KAAK;;;;;;sEAEf,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEACxB,OAAO,WAAW;;;;;;;;;;;;;;;;;;wCAKxB,4BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAS,QAAQ,iBAAiB,KAAK,cAAc,YAAY;oDACjE,WAAU;8DAET,QAAQ,iBAAiB,KAAK,cAAc,cAAc;;;;;;gDAE5D,QAAQ,iBAAiB,KAAK,6BAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEAAM,KAAK,KAAK,CAAC;gEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,2BACC,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,iBAAiB,KAAK,6BAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAM,KAAK,KAAK,CAAC;gEAAsB;;;;;;;;;;;;;8DAE1C,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,OAAO;oDAAsB,WAAU;;;;;;;;;;;;sDAIrD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,qBAAqB,OAAO,IAAI;gDAClC;;kEAEA,8OAAC,0MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,QAAQ,iBAAiB,KAAK,cAAc,SAAS;;;;;;;;;;;;sDAI1D,8OAAC;4CAAI,WAAU;;gDAAwB;gDACtB,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;yDAIlE,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,qBAAqB,OAAO,IAAI;oCAClC;;sDAEA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;uBAhFlC,OAAO,IAAI;;;;;gBAuFtB;;;;;;YAID,SAAS,MAAM,GAAG,mBACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;4BACzB,MAAM,SAAS,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,aAAa;4BAC1E,IAAI,CAAC,QAAQ,OAAO;4BAEpB,MAAM,OAAO,OAAO,IAAI;4BACxB,MAAM,uBAAuB,wBAAwB;4BAErD,qBACE,8OAAC,gIAAA,CAAA,OAAI;gCAEH,WAAU;gCACV,SAAS,IAAM,iBAAiB,QAAQ,aAAa,EAAE,QAAQ,EAAE;0CAEjE,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,QAAQ,YAAY;;;;;;kEAEvB,8OAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;0DAGpD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,QAAQ,iBAAiB,KAAK,cAAc,YAAY;gDACjE,WAAU;;oDAET,KAAK,KAAK,CAAC;oDAAsB;;;;;;;;;;;;;;;;;;+BAnBnC,QAAQ,EAAE;;;;;wBAyBrB;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1927, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1955, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1983, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 2253, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/document-generator/forms/PrivacyPolicyForm.tsx"], "sourcesContent": ["// Privacy Policy Form Component\nimport React from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\n\ninterface PrivacyPolicyFormProps {\n  step: string;\n  formData: Record<string, any>;\n  onUpdate: (field: string, value: any) => void;\n}\n\nexport function PrivacyPolicyForm({ step, formData, onUpdate }: PrivacyPolicyFormProps) {\n  const renderCompanyInfo = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <Label htmlFor=\"company_name\">Company Name *</Label>\n          <Input\n            id=\"company_name\"\n            value={formData.company_name || ''}\n            onChange={(e) => onUpdate('company_name', e.target.value)}\n            placeholder=\"Enter your company name\"\n            required\n          />\n        </div>\n        <div>\n          <Label htmlFor=\"website_url\">Website URL *</Label>\n          <Input\n            id=\"website_url\"\n            type=\"url\"\n            value={formData.website_url || ''}\n            onChange={(e) => onUpdate('website_url', e.target.value)}\n            placeholder=\"https://example.com\"\n            required\n          />\n        </div>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <Label htmlFor=\"contact_email\">Contact Email *</Label>\n          <Input\n            id=\"contact_email\"\n            type=\"email\"\n            value={formData.contact_email || ''}\n            onChange={(e) => onUpdate('contact_email', e.target.value)}\n            placeholder=\"<EMAIL>\"\n            required\n          />\n        </div>\n        <div>\n          <Label htmlFor=\"contact_phone\">Contact Phone</Label>\n          <Input\n            id=\"contact_phone\"\n            type=\"tel\"\n            value={formData.contact_phone || ''}\n            onChange={(e) => onUpdate('contact_phone', e.target.value)}\n            placeholder=\"+****************\"\n          />\n        </div>\n      </div>\n      \n      <div>\n        <Label htmlFor=\"contact_address\">Business Address *</Label>\n        <Textarea\n          id=\"contact_address\"\n          value={formData.contact_address || ''}\n          onChange={(e) => onUpdate('contact_address', e.target.value)}\n          placeholder=\"Enter your complete business address\"\n          rows={3}\n          required\n        />\n      </div>\n    </div>\n  );\n\n  const renderDataCollection = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <Label className=\"text-base font-medium\">What types of personal information do you collect?</Label>\n        <div className=\"mt-3 space-y-3\">\n          {[\n            { id: 'personal_info', label: 'Personal Information (name, email, phone)' },\n            { id: 'payment_info', label: 'Payment Information' },\n            { id: 'usage_data', label: 'Usage Data and Analytics' },\n            { id: 'device_info', label: 'Device Information' },\n            { id: 'location_data', label: 'Location Data' },\n            { id: 'cookies', label: 'Cookies and Tracking Technologies' }\n          ].map((item) => (\n            <div key={item.id} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={item.id}\n                checked={formData.data_types?.includes(item.id) || false}\n                onCheckedChange={(checked) => {\n                  const currentTypes = formData.data_types || [];\n                  const newTypes = checked \n                    ? [...currentTypes, item.id]\n                    : currentTypes.filter((type: string) => type !== item.id);\n                  onUpdate('data_types', newTypes);\n                }}\n              />\n              <Label htmlFor={item.id} className=\"text-sm font-normal\">\n                {item.label}\n              </Label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      <div>\n        <Label htmlFor=\"data_purpose\">How do you use the collected data?</Label>\n        <Textarea\n          id=\"data_purpose\"\n          value={formData.data_purpose || ''}\n          onChange={(e) => onUpdate('data_purpose', e.target.value)}\n          placeholder=\"Describe how you use the personal information you collect...\"\n          rows={4}\n        />\n      </div>\n    </div>\n  );\n\n  const renderDataSharing = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <Label className=\"text-base font-medium\">Do you share data with third parties?</Label>\n        <Select\n          value={formData.data_sharing || ''}\n          onValueChange={(value) => onUpdate('data_sharing', value)}\n        >\n          <SelectTrigger className=\"mt-2\">\n            <SelectValue placeholder=\"Select an option\" />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"no\">No, we don't share data</SelectItem>\n            <SelectItem value=\"service_providers\">Yes, with service providers</SelectItem>\n            <SelectItem value=\"business_partners\">Yes, with business partners</SelectItem>\n            <SelectItem value=\"legal_requirements\">Only when legally required</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n\n      {formData.data_sharing && formData.data_sharing !== 'no' && (\n        <div>\n          <Label htmlFor=\"sharing_details\">Please provide details about data sharing</Label>\n          <Textarea\n            id=\"sharing_details\"\n            value={formData.sharing_details || ''}\n            onChange={(e) => onUpdate('sharing_details', e.target.value)}\n            placeholder=\"Describe with whom and why you share data...\"\n            rows={4}\n          />\n        </div>\n      )}\n\n      <div>\n        <Label className=\"text-base font-medium\">Data retention period</Label>\n        <Select\n          value={formData.retention_period || ''}\n          onValueChange={(value) => onUpdate('retention_period', value)}\n        >\n          <SelectTrigger className=\"mt-2\">\n            <SelectValue placeholder=\"How long do you keep user data?\" />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"1_year\">1 Year</SelectItem>\n            <SelectItem value=\"2_years\">2 Years</SelectItem>\n            <SelectItem value=\"5_years\">5 Years</SelectItem>\n            <SelectItem value=\"indefinite\">Indefinitely</SelectItem>\n            <SelectItem value=\"custom\">Custom Period</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n    </div>\n  );\n\n  const renderUserRights = () => (\n    <div className=\"space-y-6\">\n      <div>\n        <Label className=\"text-base font-medium\">Which user rights do you provide?</Label>\n        <div className=\"mt-3 space-y-3\">\n          {[\n            { id: 'access_right', label: 'Right to access personal data' },\n            { id: 'rectification_right', label: 'Right to rectify inaccurate data' },\n            { id: 'erasure_right', label: 'Right to erasure (right to be forgotten)' },\n            { id: 'portability_right', label: 'Right to data portability' },\n            { id: 'objection_right', label: 'Right to object to processing' },\n            { id: 'restriction_right', label: 'Right to restrict processing' }\n          ].map((item) => (\n            <div key={item.id} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={item.id}\n                checked={formData.user_rights?.includes(item.id) || false}\n                onCheckedChange={(checked) => {\n                  const currentRights = formData.user_rights || [];\n                  const newRights = checked \n                    ? [...currentRights, item.id]\n                    : currentRights.filter((right: string) => right !== item.id);\n                  onUpdate('user_rights', newRights);\n                }}\n              />\n              <Label htmlFor={item.id} className=\"text-sm font-normal\">\n                {item.label}\n              </Label>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      <div>\n        <Label htmlFor=\"contact_method\">How can users contact you about their data?</Label>\n        <Textarea\n          id=\"contact_method\"\n          value={formData.contact_method || ''}\n          onChange={(e) => onUpdate('contact_method', e.target.value)}\n          placeholder=\"Provide contact information for data protection inquiries...\"\n          rows={3}\n        />\n      </div>\n    </div>\n  );\n\n  switch (step) {\n    case 'company_info':\n      return renderCompanyInfo();\n    case 'data_collection':\n      return renderDataCollection();\n    case 'data_sharing':\n      return renderDataSharing();\n    case 'user_rights':\n      return renderUserRights();\n    default:\n      return <div>Unknown step</div>;\n  }\n}\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;;AAEhC;AACA;AACA;AACA;AACA;;;;;;;AAQO,SAAS,kBAAkB,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAA0B;IACpF,MAAM,oBAAoB,kBACxB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,SAAS,YAAY,IAAI;oCAChC,UAAU,CAAC,IAAM,SAAS,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCACxD,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAGZ,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,WAAW,IAAI;oCAC/B,UAAU,CAAC,IAAM,SAAS,eAAe,EAAE,MAAM,CAAC,KAAK;oCACvD,aAAY;oCACZ,QAAQ;;;;;;;;;;;;;;;;;;8BAKd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAgB;;;;;;8CAC/B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,aAAa,IAAI;oCACjC,UAAU,CAAC,IAAM,SAAS,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCACzD,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAGZ,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAgB;;;;;;8CAC/B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,aAAa,IAAI;oCACjC,UAAU,CAAC,IAAM,SAAS,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCACzD,aAAY;;;;;;;;;;;;;;;;;;8BAKlB,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAkB;;;;;;sCACjC,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAG;4BACH,OAAO,SAAS,eAAe,IAAI;4BACnC,UAAU,CAAC,IAAM,SAAS,mBAAmB,EAAE,MAAM,CAAC,KAAK;4BAC3D,aAAY;4BACZ,MAAM;4BACN,QAAQ;;;;;;;;;;;;;;;;;;IAMhB,MAAM,uBAAuB,kBAC3B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAAwB;;;;;;sCACzC,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAiB,OAAO;gCAA4C;gCAC1E;oCAAE,IAAI;oCAAgB,OAAO;gCAAsB;gCACnD;oCAAE,IAAI;oCAAc,OAAO;gCAA2B;gCACtD;oCAAE,IAAI;oCAAe,OAAO;gCAAqB;gCACjD;oCAAE,IAAI;oCAAiB,OAAO;gCAAgB;gCAC9C;oCAAE,IAAI;oCAAW,OAAO;gCAAoC;6BAC7D,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAI,KAAK,EAAE;4CACX,SAAS,SAAS,UAAU,EAAE,SAAS,KAAK,EAAE,KAAK;4CACnD,iBAAiB,CAAC;gDAChB,MAAM,eAAe,SAAS,UAAU,IAAI,EAAE;gDAC9C,MAAM,WAAW,UACb;uDAAI;oDAAc,KAAK,EAAE;iDAAC,GAC1B,aAAa,MAAM,CAAC,CAAC,OAAiB,SAAS,KAAK,EAAE;gDAC1D,SAAS,cAAc;4CACzB;;;;;;sDAEF,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,KAAK,EAAE;4CAAE,WAAU;sDAChC,KAAK,KAAK;;;;;;;mCAbL,KAAK,EAAE;;;;;;;;;;;;;;;;8BAoBvB,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAe;;;;;;sCAC9B,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAG;4BACH,OAAO,SAAS,YAAY,IAAI;4BAChC,UAAU,CAAC,IAAM,SAAS,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BACxD,aAAY;4BACZ,MAAM;;;;;;;;;;;;;;;;;;IAMd,MAAM,oBAAoB,kBACxB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAAwB;;;;;;sCACzC,8OAAC,kIAAA,CAAA,SAAM;4BACL,OAAO,SAAS,YAAY,IAAI;4BAChC,eAAe,CAAC,QAAU,SAAS,gBAAgB;;8CAEnD,8OAAC,kIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;8CAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sDACZ,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAK;;;;;;sDACvB,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAoB;;;;;;sDACtC,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAoB;;;;;;sDACtC,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;gBAK5C,SAAS,YAAY,IAAI,SAAS,YAAY,KAAK,sBAClD,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAkB;;;;;;sCACjC,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAG;4BACH,OAAO,SAAS,eAAe,IAAI;4BACnC,UAAU,CAAC,IAAM,SAAS,mBAAmB,EAAE,MAAM,CAAC,KAAK;4BAC3D,aAAY;4BACZ,MAAM;;;;;;;;;;;;8BAKZ,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAAwB;;;;;;sCACzC,8OAAC,kIAAA,CAAA,SAAM;4BACL,OAAO,SAAS,gBAAgB,IAAI;4BACpC,eAAe,CAAC,QAAU,SAAS,oBAAoB;;8CAEvD,8OAAC,kIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;8CAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sDACZ,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAS;;;;;;sDAC3B,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAU;;;;;;sDAC5B,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAU;;;;;;sDAC5B,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAa;;;;;;sDAC/B,8OAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOrC,MAAM,mBAAmB,kBACvB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAAwB;;;;;;sCACzC,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAgB,OAAO;gCAAgC;gCAC7D;oCAAE,IAAI;oCAAuB,OAAO;gCAAmC;gCACvE;oCAAE,IAAI;oCAAiB,OAAO;gCAA2C;gCACzE;oCAAE,IAAI;oCAAqB,OAAO;gCAA4B;gCAC9D;oCAAE,IAAI;oCAAmB,OAAO;gCAAgC;gCAChE;oCAAE,IAAI;oCAAqB,OAAO;gCAA+B;6BAClE,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;oCAAkB,WAAU;;sDAC3B,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAI,KAAK,EAAE;4CACX,SAAS,SAAS,WAAW,EAAE,SAAS,KAAK,EAAE,KAAK;4CACpD,iBAAiB,CAAC;gDAChB,MAAM,gBAAgB,SAAS,WAAW,IAAI,EAAE;gDAChD,MAAM,YAAY,UACd;uDAAI;oDAAe,KAAK,EAAE;iDAAC,GAC3B,cAAc,MAAM,CAAC,CAAC,QAAkB,UAAU,KAAK,EAAE;gDAC7D,SAAS,eAAe;4CAC1B;;;;;;sDAEF,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,KAAK,EAAE;4CAAE,WAAU;sDAChC,KAAK,KAAK;;;;;;;mCAbL,KAAK,EAAE;;;;;;;;;;;;;;;;8BAoBvB,8OAAC;;sCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAiB;;;;;;sCAChC,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAG;4BACH,OAAO,SAAS,cAAc,IAAI;4BAClC,UAAU,CAAC,IAAM,SAAS,kBAAkB,EAAE,MAAM,CAAC,KAAK;4BAC1D,aAAY;4BACZ,MAAM;;;;;;;;;;;;;;;;;;IAMd,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,qBAAO,8OAAC;0BAAI;;;;;;IAChB;AACF", "debugId": null}}, {"offset": {"line": 2900, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/document-generator/DocumentGeneratorWizard.tsx"], "sourcesContent": ["// Document Generator Wizard - Multi-step form for generating documents\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Progress } from '@/components/ui/progress';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  ArrowLeft, \n  ArrowRight, \n  Save, \n  FileText, \n  Download,\n  Eye,\n  CheckCircle\n} from 'lucide-react';\nimport { DocumentGeneratorType, DocumentGeneratorSession } from '@/lib/document-generator-types';\nimport { DocumentGeneratorService } from '@/lib/document-generator-service';\nimport { PrivacyPolicyForm } from './forms/PrivacyPolicyForm';\n\ninterface DocumentGeneratorWizardProps {\n  tenantId: string;\n  userId?: string;\n  documentType: DocumentGeneratorType;\n  sessionId?: string;\n  onBack: () => void;\n  onComplete: (documentId: string) => void;\n}\n\ninterface FormStep {\n  id: string;\n  title: string;\n  description: string;\n  fields: string[];\n}\n\nconst formSteps: Record<DocumentGeneratorType, FormStep[]> = {\n  privacy_policy: [\n    {\n      id: 'company_info',\n      title: 'Company Information',\n      description: 'Basic information about your company',\n      fields: ['company_name', 'website_url', 'contact_email', 'contact_address']\n    },\n    {\n      id: 'data_collection',\n      title: 'Data Collection',\n      description: 'What personal information do you collect?',\n      fields: ['collects_personal_info', 'personal_info_types', 'collects_usage_data', 'uses_cookies', 'uses_analytics']\n    },\n    {\n      id: 'data_usage',\n      title: 'Data Usage',\n      description: 'How do you use the collected data?',\n      fields: ['data_usage_purposes', 'shares_data_third_party', 'third_party_services']\n    },\n    {\n      id: 'compliance',\n      title: 'Legal Compliance',\n      description: 'Compliance requirements and user rights',\n      fields: ['gdpr_applicable', 'ccpa_applicable', 'data_retention_period', 'privacy_contact_email', 'dpo_contact']\n    }\n  ],\n  terms_conditions: [\n    {\n      id: 'service_info',\n      title: 'Service Information',\n      description: 'Basic information about your service',\n      fields: ['service_name', 'service_type', 'website_url', 'company_name']\n    },\n    {\n      id: 'user_accounts',\n      title: 'User Accounts',\n      description: 'Account requirements and policies',\n      fields: ['requires_registration', 'minimum_age', 'account_termination_policy']\n    },\n    {\n      id: 'usage_rules',\n      title: 'Usage Rules',\n      description: 'Service usage guidelines and restrictions',\n      fields: ['prohibited_activities', 'content_guidelines', 'intellectual_property_policy']\n    },\n    {\n      id: 'legal_terms',\n      title: 'Legal Terms',\n      description: 'Liability, disclaimers, and legal information',\n      fields: ['liability_limitations', 'warranty_disclaimers', 'governing_law', 'dispute_resolution', 'contact_email']\n    }\n  ],\n  cookie_policy: [\n    {\n      id: 'website_info',\n      title: 'Website Information',\n      description: 'Basic information about your website',\n      fields: ['website_name', 'website_url']\n    },\n    {\n      id: 'cookie_types',\n      title: 'Cookie Types',\n      description: 'What types of cookies do you use?',\n      fields: ['uses_essential_cookies', 'uses_analytics_cookies', 'uses_marketing_cookies', 'uses_functional_cookies']\n    },\n    {\n      id: 'third_party',\n      title: 'Third-Party Services',\n      description: 'External services and tracking',\n      fields: ['google_analytics', 'facebook_pixel', 'other_tracking_services']\n    },\n    {\n      id: 'user_control',\n      title: 'User Control',\n      description: 'Cookie management and user options',\n      fields: ['cookie_consent_mechanism', 'cookie_management_instructions', 'contact_email']\n    }\n  ],\n  // Add other document types with similar structure\n  eula: [\n    {\n      id: 'software_info',\n      title: 'Software Information',\n      description: 'Basic information about your software',\n      fields: ['software_name', 'company_name', 'contact_email']\n    }\n  ],\n  acceptable_use: [\n    {\n      id: 'service_info',\n      title: 'Service Information',\n      description: 'Basic information about your service',\n      fields: ['service_name', 'contact_email']\n    }\n  ],\n  return_policy: [\n    {\n      id: 'company_info',\n      title: 'Company Information',\n      description: 'Basic information about your company',\n      fields: ['company_name', 'contact_email']\n    }\n  ],\n  disclaimer: [\n    {\n      id: 'website_info',\n      title: 'Website Information',\n      description: 'Basic information about your website',\n      fields: ['website_name', 'contact_email']\n    }\n  ],\n  shipping_policy: [\n    {\n      id: 'company_info',\n      title: 'Company Information',\n      description: 'Basic information about your company',\n      fields: ['company_name', 'contact_email']\n    }\n  ]\n};\n\nexport function DocumentGeneratorWizard({ \n  tenantId, \n  userId, \n  documentType, \n  sessionId, \n  onBack, \n  onComplete \n}: DocumentGeneratorWizardProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [formData, setFormData] = useState<Record<string, any>>({});\n  const [session, setSession] = useState<DocumentGeneratorSession | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const documentService = new DocumentGeneratorService(tenantId);\n  const steps = formSteps[documentType] || [];\n  const currentStepData = steps[currentStep];\n  const totalSteps = steps.length;\n  const progress = ((currentStep + 1) / totalSteps) * 100;\n\n  useEffect(() => {\n    if (sessionId) {\n      loadSession();\n    }\n  }, [sessionId]);\n\n  // Auto-save every 30 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (session && Object.keys(formData).length > 0) {\n        saveSession();\n      }\n    }, 30000);\n\n    return () => clearInterval(interval);\n  }, [session, formData]);\n\n  const loadSession = async () => {\n    if (!sessionId) return;\n\n    try {\n      setLoading(true);\n      const response = await documentService.getSession(sessionId);\n      if (response.success && response.data) {\n        setSession(response.data);\n        setFormData(response.data.form_data || {});\n      } else {\n        setError(response.error || 'Failed to load session');\n      }\n    } catch (err) {\n      setError('Failed to load session');\n      console.error('Error loading session:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const saveSession = async () => {\n    if (!session) return;\n\n    try {\n      setSaving(true);\n      const response = await documentService.updateSession(session.id, {\n        form_data: formData,\n        completion_status: currentStep === totalSteps - 1 ? 'completed' : 'draft'\n      });\n\n      if (!response.success) {\n        console.error('Failed to save session:', response.error);\n      }\n    } catch (err) {\n      console.error('Error saving session:', err);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleFieldChange = (fieldName: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldName]: value\n    }));\n  };\n\n  // Function for PrivacyPolicyForm component\n  const updateFormData = (fieldName: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [fieldName]: value\n    }));\n  };\n\n  const handleNext = async () => {\n    await saveSession();\n    if (currentStep < totalSteps - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleGenerate = async () => {\n    if (!session) return;\n\n    try {\n      setLoading(true);\n      \n      // Save final session\n      await saveSession();\n      \n      // Generate document\n      const response = await documentService.generateDocument({\n        session_id: session.id,\n        title: `${documentType.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())} - Generated Document`\n      });\n\n      if (response.success && response.data) {\n        onComplete(response.data.id);\n      } else {\n        setError(response.error || 'Failed to generate document');\n      }\n    } catch (err) {\n      setError('Failed to generate document');\n      console.error('Error generating document:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderField = (fieldName: string) => {\n    const value = formData[fieldName] || '';\n\n    // Field configurations\n    const fieldConfigs: Record<string, any> = {\n      company_name: { type: 'text', label: 'Company Name', placeholder: 'Enter your company name' },\n      website_url: { type: 'url', label: 'Website URL', placeholder: 'https://example.com' },\n      contact_email: { type: 'email', label: 'Contact Email', placeholder: '<EMAIL>' },\n      contact_address: { type: 'textarea', label: 'Contact Address', placeholder: 'Enter your business address' },\n      service_name: { type: 'text', label: 'Service Name', placeholder: 'Enter your service name' },\n      service_type: { type: 'select', label: 'Service Type', options: ['Website', 'Mobile App', 'SaaS Platform', 'E-commerce', 'Other'] },\n      minimum_age: { type: 'number', label: 'Minimum Age', placeholder: '13' },\n      data_retention_period: { type: 'select', label: 'Data Retention Period', options: ['1 year', '2 years', '3 years', '5 years', 'Until account deletion'] },\n      governing_law: { type: 'text', label: 'Governing Law', placeholder: 'e.g., State of California' },\n      \n      // Boolean fields\n      collects_personal_info: { type: 'checkbox', label: 'We collect personal information' },\n      collects_usage_data: { type: 'checkbox', label: 'We collect usage data' },\n      uses_cookies: { type: 'checkbox', label: 'We use cookies' },\n      uses_analytics: { type: 'checkbox', label: 'We use analytics' },\n      gdpr_applicable: { type: 'checkbox', label: 'GDPR applies to our service' },\n      ccpa_applicable: { type: 'checkbox', label: 'CCPA applies to our service' },\n      requires_registration: { type: 'checkbox', label: 'Service requires user registration' },\n      shares_data_third_party: { type: 'checkbox', label: 'We share data with third parties' },\n      \n      // Array fields\n      personal_info_types: { type: 'textarea', label: 'Types of Personal Information', placeholder: 'e.g., Name, Email, Phone Number' },\n      data_usage_purposes: { type: 'textarea', label: 'Data Usage Purposes', placeholder: 'e.g., Service provision, Customer support' },\n      third_party_services: { type: 'textarea', label: 'Third-Party Services', placeholder: 'e.g., Google Analytics, Stripe' },\n      prohibited_activities: { type: 'textarea', label: 'Prohibited Activities', placeholder: 'List activities that are not allowed' },\n      content_guidelines: { type: 'textarea', label: 'Content Guidelines', placeholder: 'Guidelines for user-generated content' }\n    };\n\n    const config = fieldConfigs[fieldName] || { type: 'text', label: fieldName, placeholder: '' };\n\n    switch (config.type) {\n      case 'text':\n      case 'email':\n      case 'url':\n      case 'number':\n        return (\n          <div key={fieldName} className=\"space-y-2\">\n            <Label htmlFor={fieldName}>{config.label}</Label>\n            <Input\n              id={fieldName}\n              type={config.type}\n              value={value}\n              placeholder={config.placeholder}\n              onChange={(e) => handleFieldChange(fieldName, e.target.value)}\n            />\n          </div>\n        );\n\n      case 'textarea':\n        return (\n          <div key={fieldName} className=\"space-y-2\">\n            <Label htmlFor={fieldName}>{config.label}</Label>\n            <Textarea\n              id={fieldName}\n              value={value}\n              placeholder={config.placeholder}\n              onChange={(e) => handleFieldChange(fieldName, e.target.value)}\n              rows={3}\n            />\n          </div>\n        );\n\n      case 'select':\n        return (\n          <div key={fieldName} className=\"space-y-2\">\n            <Label htmlFor={fieldName}>{config.label}</Label>\n            <Select value={value} onValueChange={(val) => handleFieldChange(fieldName, val)}>\n              <SelectTrigger>\n                <SelectValue placeholder={`Select ${config.label.toLowerCase()}`} />\n              </SelectTrigger>\n              <SelectContent>\n                {config.options?.map((option: string) => (\n                  <SelectItem key={option} value={option}>\n                    {option}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n        );\n\n      case 'checkbox':\n        return (\n          <div key={fieldName} className=\"flex items-center space-x-2\">\n            <Checkbox\n              id={fieldName}\n              checked={value === true}\n              onCheckedChange={(checked) => handleFieldChange(fieldName, checked)}\n            />\n            <Label htmlFor={fieldName}>{config.label}</Label>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <Button variant=\"ghost\" onClick={onBack}>\n          <ArrowLeft className=\"h-4 w-4 mr-2\" />\n          Back to Generators\n        </Button>\n        \n        <div className=\"flex items-center space-x-2\">\n          {saving && (\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              <Save className=\"h-3 w-3 mr-1\" />\n              Saving...\n            </Badge>\n          )}\n          <Badge variant=\"outline\">\n            Step {currentStep + 1} of {totalSteps}\n          </Badge>\n        </div>\n      </div>\n\n      {/* Progress */}\n      <div className=\"space-y-2\">\n        <div className=\"flex justify-between text-sm text-gray-600\">\n          <span>Progress</span>\n          <span>{Math.round(progress)}%</span>\n        </div>\n        <Progress value={progress} className=\"h-2\" />\n      </div>\n\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <p className=\"text-red-800\">{error}</p>\n        </div>\n      )}\n\n      {/* Form Step */}\n      {currentStepData && (\n        <Card>\n          <CardHeader>\n            <CardTitle>{currentStepData.title}</CardTitle>\n            <CardDescription>{currentStepData.description}</CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            {documentType === 'privacy_policy' ? (\n              <PrivacyPolicyForm\n                step={currentStepData.id}\n                formData={formData}\n                onUpdate={updateFormData}\n              />\n            ) : (\n              // Fallback to generic fields for other document types\n              currentStepData.fields.map(fieldName => renderField(fieldName))\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Navigation */}\n      <div className=\"flex justify-between\">\n        <Button \n          variant=\"outline\" \n          onClick={handlePrevious}\n          disabled={currentStep === 0}\n        >\n          <ArrowLeft className=\"h-4 w-4 mr-2\" />\n          Previous\n        </Button>\n\n        <div className=\"flex space-x-2\">\n          {currentStep < totalSteps - 1 ? (\n            <Button onClick={handleNext}>\n              Next\n              <ArrowRight className=\"h-4 w-4 ml-2\" />\n            </Button>\n          ) : (\n            <Button onClick={handleGenerate} disabled={loading}>\n              <FileText className=\"h-4 w-4 mr-2\" />\n              Generate Document\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAUA;AACA;;;;;;;;;;;;;;;AAkBA,MAAM,YAAuD;IAC3D,gBAAgB;QACd;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAgB;gBAAe;gBAAiB;aAAkB;QAC7E;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAA0B;gBAAuB;gBAAuB;gBAAgB;aAAiB;QACpH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAuB;gBAA2B;aAAuB;QACpF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAmB;gBAAmB;gBAAyB;gBAAyB;aAAc;QACjH;KACD;IACD,kBAAkB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAgB;gBAAgB;gBAAe;aAAe;QACzE;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAyB;gBAAe;aAA6B;QAChF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAyB;gBAAsB;aAA+B;QACzF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAyB;gBAAwB;gBAAiB;gBAAsB;aAAgB;QACnH;KACD;IACD,eAAe;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAgB;aAAc;QACzC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAA0B;gBAA0B;gBAA0B;aAA0B;QACnH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAoB;gBAAkB;aAA0B;QAC3E;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAA4B;gBAAkC;aAAgB;QACzF;KACD;IACD,kDAAkD;IAClD,MAAM;QACJ;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAiB;gBAAgB;aAAgB;QAC5D;KACD;IACD,gBAAgB;QACd;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAgB;aAAgB;QAC3C;KACD;IACD,eAAe;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAgB;aAAgB;QAC3C;KACD;IACD,YAAY;QACV;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAgB;aAAgB;QAC3C;KACD;IACD,iBAAiB;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;gBAAgB;aAAgB;QAC3C;KACD;AACH;AAEO,SAAS,wBAAwB,EACtC,QAAQ,EACR,MAAM,EACN,YAAY,EACZ,SAAS,EACT,MAAM,EACN,UAAU,EACmB;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB,IAAI,8IAAA,CAAA,2BAAwB,CAAC;IACrD,MAAM,QAAQ,SAAS,CAAC,aAAa,IAAI,EAAE;IAC3C,MAAM,kBAAkB,KAAK,CAAC,YAAY;IAC1C,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,WAAW,AAAC,CAAC,cAAc,CAAC,IAAI,aAAc;IAEpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAU;IAEd,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,IAAI,WAAW,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;gBAC/C;YACF;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,gBAAgB,UAAU,CAAC;YAClD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,WAAW,SAAS,IAAI;gBACxB,YAAY,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC;YAC1C,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,UAAU;YACV,MAAM,WAAW,MAAM,gBAAgB,aAAa,CAAC,QAAQ,EAAE,EAAE;gBAC/D,WAAW;gBACX,mBAAmB,gBAAgB,aAAa,IAAI,cAAc;YACpE;YAEA,IAAI,CAAC,SAAS,OAAO,EAAE;gBACrB,QAAQ,KAAK,CAAC,2BAA2B,SAAS,KAAK;YACzD;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;YACf,CAAC;IACH;IAEA,2CAA2C;IAC3C,MAAM,iBAAiB,CAAC,WAAmB;QACzC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;YACf,CAAC;IACH;IAEA,MAAM,aAAa;QACjB,MAAM;QACN,IAAI,cAAc,aAAa,GAAG;YAChC,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,WAAW;YAEX,qBAAqB;YACrB,MAAM;YAEN,oBAAoB;YACpB,MAAM,WAAW,MAAM,gBAAgB,gBAAgB,CAAC;gBACtD,YAAY,QAAQ,EAAE;gBACtB,OAAO,GAAG,aAAa,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW,IAAI,qBAAqB,CAAC;YACxG;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,WAAW,SAAS,IAAI,CAAC,EAAE;YAC7B,OAAO;gBACL,SAAS,SAAS,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,QAAQ,CAAC,UAAU,IAAI;QAErC,uBAAuB;QACvB,MAAM,eAAoC;YACxC,cAAc;gBAAE,MAAM;gBAAQ,OAAO;gBAAgB,aAAa;YAA0B;YAC5F,aAAa;gBAAE,MAAM;gBAAO,OAAO;gBAAe,aAAa;YAAsB;YACrF,eAAe;gBAAE,MAAM;gBAAS,OAAO;gBAAiB,aAAa;YAAsB;YAC3F,iBAAiB;gBAAE,MAAM;gBAAY,OAAO;gBAAmB,aAAa;YAA8B;YAC1G,cAAc;gBAAE,MAAM;gBAAQ,OAAO;gBAAgB,aAAa;YAA0B;YAC5F,cAAc;gBAAE,MAAM;gBAAU,OAAO;gBAAgB,SAAS;oBAAC;oBAAW;oBAAc;oBAAiB;oBAAc;iBAAQ;YAAC;YAClI,aAAa;gBAAE,MAAM;gBAAU,OAAO;gBAAe,aAAa;YAAK;YACvE,uBAAuB;gBAAE,MAAM;gBAAU,OAAO;gBAAyB,SAAS;oBAAC;oBAAU;oBAAW;oBAAW;oBAAW;iBAAyB;YAAC;YACxJ,eAAe;gBAAE,MAAM;gBAAQ,OAAO;gBAAiB,aAAa;YAA4B;YAEhG,iBAAiB;YACjB,wBAAwB;gBAAE,MAAM;gBAAY,OAAO;YAAkC;YACrF,qBAAqB;gBAAE,MAAM;gBAAY,OAAO;YAAwB;YACxE,cAAc;gBAAE,MAAM;gBAAY,OAAO;YAAiB;YAC1D,gBAAgB;gBAAE,MAAM;gBAAY,OAAO;YAAmB;YAC9D,iBAAiB;gBAAE,MAAM;gBAAY,OAAO;YAA8B;YAC1E,iBAAiB;gBAAE,MAAM;gBAAY,OAAO;YAA8B;YAC1E,uBAAuB;gBAAE,MAAM;gBAAY,OAAO;YAAqC;YACvF,yBAAyB;gBAAE,MAAM;gBAAY,OAAO;YAAmC;YAEvF,eAAe;YACf,qBAAqB;gBAAE,MAAM;gBAAY,OAAO;gBAAiC,aAAa;YAAkC;YAChI,qBAAqB;gBAAE,MAAM;gBAAY,OAAO;gBAAuB,aAAa;YAA4C;YAChI,sBAAsB;gBAAE,MAAM;gBAAY,OAAO;gBAAwB,aAAa;YAAiC;YACvH,uBAAuB;gBAAE,MAAM;gBAAY,OAAO;gBAAyB,aAAa;YAAuC;YAC/H,oBAAoB;gBAAE,MAAM;gBAAY,OAAO;gBAAsB,aAAa;YAAwC;QAC5H;QAEA,MAAM,SAAS,YAAY,CAAC,UAAU,IAAI;YAAE,MAAM;YAAQ,OAAO;YAAW,aAAa;QAAG;QAE5F,OAAQ,OAAO,IAAI;YACjB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC;oBAAoB,WAAU;;sCAC7B,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS;sCAAY,OAAO,KAAK;;;;;;sCACxC,8OAAC,iIAAA,CAAA,QAAK;4BACJ,IAAI;4BACJ,MAAM,OAAO,IAAI;4BACjB,OAAO;4BACP,aAAa,OAAO,WAAW;4BAC/B,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;mBAPtD;;;;;YAYd,KAAK;gBACH,qBACE,8OAAC;oBAAoB,WAAU;;sCAC7B,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS;sCAAY,OAAO,KAAK;;;;;;sCACxC,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAI;4BACJ,OAAO;4BACP,aAAa,OAAO,WAAW;4BAC/B,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC5D,MAAM;;;;;;;mBAPA;;;;;YAYd,KAAK;gBACH,qBACE,8OAAC;oBAAoB,WAAU;;sCAC7B,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS;sCAAY,OAAO,KAAK;;;;;;sCACxC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAO,eAAe,CAAC,MAAQ,kBAAkB,WAAW;;8CACzE,8OAAC,kIAAA,CAAA,gBAAa;8CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wCAAC,aAAa,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC,WAAW,IAAI;;;;;;;;;;;8CAElE,8OAAC,kIAAA,CAAA,gBAAa;8CACX,OAAO,OAAO,EAAE,IAAI,CAAC,uBACpB,8OAAC,kIAAA,CAAA,aAAU;4CAAc,OAAO;sDAC7B;2CADc;;;;;;;;;;;;;;;;;mBARf;;;;;YAiBd,KAAK;gBACH,qBACE,8OAAC;oBAAoB,WAAU;;sCAC7B,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAI;4BACJ,SAAS,UAAU;4BACnB,iBAAiB,CAAC,UAAY,kBAAkB,WAAW;;;;;;sCAE7D,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS;sCAAY,OAAO,KAAK;;;;;;;mBANhC;;;;;YAUd;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,SAAS;;0CAC/B,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;;4BACZ,wBACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;kDACnC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIrC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;oCAAU;oCACjB,cAAc;oCAAE;oCAAK;;;;;;;;;;;;;;;;;;;0BAMjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;;oCAAM,KAAK,KAAK,CAAC;oCAAU;;;;;;;;;;;;;kCAE9B,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAU,WAAU;;;;;;;;;;;;YAGtC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;YAKhC,iCACC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAE,gBAAgB,KAAK;;;;;;0CACjC,8OAAC,gIAAA,CAAA,kBAAe;0CAAE,gBAAgB,WAAW;;;;;;;;;;;;kCAE/C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,iBAAiB,iCAChB,8OAAC,yKAAA,CAAA,oBAAiB;4BAChB,MAAM,gBAAgB,EAAE;4BACxB,UAAU;4BACV,UAAU;;;;;mCAGZ,sDAAsD;wBACtD,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAA,YAAa,YAAY;;;;;;;;;;;;0BAO5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,gBAAgB;;0CAE1B,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;kCACZ,cAAc,aAAa,kBAC1B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;;gCAAY;8CAE3B,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;iDAGxB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAgB,UAAU;;8CACzC,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 3788, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3852, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/components/document-generator/DocumentViewer.tsx"], "sourcesContent": ["// Document Viewer - Display and manage generated documents\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';\nimport { \n  ArrowLeft, \n  Download, \n  Edit3, \n  Share2, \n  Copy, \n  FileText, \n  Code,\n  Eye,\n  Calendar,\n  User\n} from 'lucide-react';\nimport { GeneratedDocument } from '@/lib/document-generator-types';\nimport { DocumentGeneratorService } from '@/lib/document-generator-service';\n\ninterface DocumentViewerProps {\n  tenantId: string;\n  documentId: string;\n  onBack: () => void;\n  onEdit: () => void;\n}\n\nexport function DocumentViewer({ tenantId, documentId, onBack, onEdit }: DocumentViewerProps) {\n  const [document, setDocument] = useState<GeneratedDocument | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [copying, setCopying] = useState(false);\n\n  const documentService = new DocumentGeneratorService(tenantId);\n\n  useEffect(() => {\n    loadDocument();\n  }, [documentId]);\n\n  const loadDocument = async () => {\n    try {\n      setLoading(true);\n      const response = await documentService.getDocument(documentId);\n      \n      if (response.success && response.data) {\n        setDocument(response.data);\n      } else {\n        // Fallback to mock document for demo\n        loadMockDocument();\n      }\n    } catch (error) {\n      console.error('Error loading document:', error);\n      loadMockDocument();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadMockDocument = () => {\n    const mockDocument: GeneratedDocument = {\n      id: documentId,\n      session_id: 'mock-session',\n      tenant_id: tenantId,\n      document_type: 'privacy_policy',\n      title: 'Privacy Policy - Generated Document',\n      generated_content: `\n        <h1>Privacy Policy for Example Company</h1>\n        <p><strong>Last Updated:</strong> ${new Date().toLocaleDateString()}</p>\n        \n        <h2>1. Information We Collect</h2>\n        <p>We collect the following types of personal information:</p>\n        <ul>\n          <li>Personal identifiers: Name, Email, Phone Number</li>\n          <li>Usage data and analytics</li>\n          <li>Cookies and tracking technologies</li>\n        </ul>\n        \n        <h2>2. How We Use Your Information</h2>\n        <p>We use your personal information for:</p>\n        <ul>\n          <li>Service provision</li>\n          <li>Customer support</li>\n          <li>Analytics and improvements</li>\n        </ul>\n        \n        <h2>3. Information Sharing</h2>\n        <p>We may share your information with: Google Analytics, Stripe</p>\n        \n        <h2>4. Your Rights</h2>\n        <p>Under GDPR, you have the right to access, correct, delete, and port your data.</p>\n        <p>Under CCPA, you have the right to know, delete, and opt-out of the sale of your personal information.</p>\n        \n        <h2>5. Data Retention</h2>\n        <p>We retain your data for: 2 years</p>\n        \n        <h2>6. Contact Information</h2>\n        <p>For privacy questions, contact us at: <EMAIL></p>\n        <p>Data Protection Officer: <EMAIL></p>\n      `,\n      markdown_content: `# Privacy Policy for Example Company\n\n**Last Updated:** ${new Date().toLocaleDateString()}\n\n## 1. Information We Collect\n\nWe collect the following types of personal information:\n- Personal identifiers: Name, Email, Phone Number\n- Usage data and analytics\n- Cookies and tracking technologies\n\n## 2. How We Use Your Information\n\nWe use your personal information for:\n- Service provision\n- Customer support\n- Analytics and improvements\n\n## 3. Information Sharing\nWe may share your information with: Google Analytics, Stripe\n\n## 4. Your Rights\n\nUnder GDPR, you have the right to access, correct, delete, and port your data.\nUnder CCPA, you have the right to know, delete, and opt-out of the sale of your personal information.\n\n## 5. Data Retention\nWe retain your data for: 2 years\n\n## 6. Contact Information\nFor privacy questions, contact us at: <EMAIL>\nData Protection Officer: <EMAIL>`,\n      status: 'draft',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    };\n\n    setDocument(mockDocument);\n  };\n\n  const handleCopyToClipboard = async (content: string) => {\n    try {\n      setCopying(true);\n      await navigator.clipboard.writeText(content);\n      setTimeout(() => setCopying(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy to clipboard:', err);\n      setCopying(false);\n    }\n  };\n\n  const handleDownload = (content: string, filename: string) => {\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = filename;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"text-gray-600\">Loading document...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"text-red-600\">\n            <FileText className=\"h-12 w-12 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-semibold\">Error Loading Document</h2>\n            <p className=\"text-gray-600\">{error}</p>\n          </div>\n          <Button onClick={onBack} variant=\"outline\">\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Generator\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  if (!document) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center space-y-4\">\n          <FileText className=\"h-12 w-12 mx-auto text-gray-400\" />\n          <h2 className=\"text-xl font-semibold text-gray-900\">Document Not Found</h2>\n          <p className=\"text-gray-600\">The requested document could not be found.</p>\n          <Button onClick={onBack} variant=\"outline\">\n            <ArrowLeft className=\"h-4 w-4 mr-2\" />\n            Back to Generator\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <Button variant=\"ghost\" onClick={onBack}>\n          <ArrowLeft className=\"h-4 w-4 mr-2\" />\n          Back to Generator\n        </Button>\n        \n        <div className=\"flex items-center space-x-2\">\n          <Badge variant=\"outline\" className=\"capitalize\">\n            {document.document_type.replace('_', ' ')}\n          </Badge>\n          <Badge variant={document.status === 'published' ? 'default' : 'secondary'}>\n            {document.status}\n          </Badge>\n        </div>\n      </div>\n\n      {/* Document Info */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-start justify-between\">\n            <div>\n              <CardTitle className=\"flex items-center\">\n                <FileText className=\"h-5 w-5 mr-2\" />\n                {document.title}\n              </CardTitle>\n              <CardDescription className=\"flex items-center mt-2 space-x-4\">\n                <span className=\"flex items-center\">\n                  <Calendar className=\"h-4 w-4 mr-1\" />\n                  Created: {new Date(document.created_at).toLocaleDateString()}\n                </span>\n                <span className=\"flex items-center\">\n                  <User className=\"h-4 w-4 mr-1\" />\n                  ID: {document.id.slice(0, 8)}...\n                </span>\n              </CardDescription>\n            </div>\n          </div>\n        </CardHeader>\n      </Card>\n\n      {/* Document Content */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Document Content</CardTitle>\n          <CardDescription>\n            View and manage your generated document\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Tabs defaultValue=\"preview\" className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-3\">\n              <TabsTrigger value=\"preview\">\n                <Eye className=\"h-4 w-4 mr-2\" />\n                Preview\n              </TabsTrigger>\n              <TabsTrigger value=\"html\">\n                <Code className=\"h-4 w-4 mr-2\" />\n                HTML\n              </TabsTrigger>\n              <TabsTrigger value=\"markdown\">\n                <FileText className=\"h-4 w-4 mr-2\" />\n                Markdown\n              </TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"preview\" className=\"mt-6\">\n              <div className=\"border rounded-lg p-6 bg-white\">\n                <div \n                  className=\"prose max-w-none\"\n                  dangerouslySetInnerHTML={{ __html: document.generated_content }}\n                />\n              </div>\n              <div className=\"flex justify-end space-x-2 mt-4\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => handleDownload(document.generated_content, `${document.title}.html`)}\n                >\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Download HTML\n                </Button>\n              </div>\n            </TabsContent>\n            \n            <TabsContent value=\"html\" className=\"mt-6\">\n              <div className=\"relative\">\n                <pre className=\"bg-gray-50 p-4 rounded-lg overflow-auto max-h-96 text-sm\">\n                  <code>{document.generated_content}</code>\n                </pre>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"absolute top-2 right-2\"\n                  onClick={() => handleCopyToClipboard(document.generated_content)}\n                  disabled={copying}\n                >\n                  <Copy className=\"h-4 w-4 mr-2\" />\n                  {copying ? 'Copied!' : 'Copy'}\n                </Button>\n              </div>\n            </TabsContent>\n            \n            <TabsContent value=\"markdown\" className=\"mt-6\">\n              <div className=\"relative\">\n                <pre className=\"bg-gray-50 p-4 rounded-lg overflow-auto max-h-96 text-sm\">\n                  <code>{document.markdown_content}</code>\n                </pre>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"absolute top-2 right-2\"\n                  onClick={() => handleCopyToClipboard(document.markdown_content || '')}\n                  disabled={copying}\n                >\n                  <Copy className=\"h-4 w-4 mr-2\" />\n                  {copying ? 'Copied!' : 'Copy'}\n                </Button>\n              </div>\n              <div className=\"flex justify-end space-x-2 mt-4\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => handleDownload(document.markdown_content || '', `${document.title}.md`)}\n                >\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Download Markdown\n                </Button>\n              </div>\n            </TabsContent>\n          </Tabs>\n        </CardContent>\n      </Card>\n\n      {/* Actions */}\n      <div className=\"flex justify-between\">\n        <Button variant=\"outline\" onClick={onEdit}>\n          <Edit3 className=\"h-4 w-4 mr-2\" />\n          Edit Document\n        </Button>\n        <Button>\n          <Share2 className=\"h-4 w-4 mr-2\" />\n          Publish Document\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAC3D;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;;;;;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAuB;IAC1F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,kBAAkB,IAAI,8IAAA,CAAA,2BAAwB,CAAC;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,gBAAgB,WAAW,CAAC;YAEnD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,YAAY,SAAS,IAAI;YAC3B,OAAO;gBACL,qCAAqC;gBACrC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,eAAkC;YACtC,IAAI;YACJ,YAAY;YACZ,WAAW;YACX,eAAe;YACf,OAAO;YACP,mBAAmB,CAAC;;0CAEgB,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA+BtE,CAAC;YACD,kBAAkB,CAAC;;kBAEP,EAAE,IAAI,OAAO,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCA6BZ,CAAC;YACnC,QAAQ;YACR,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,YAAY;IACd;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,WAAW;YACX,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,WAAW,IAAM,WAAW,QAAQ;QACtC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC,SAAiB;QACvC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAa;QACtD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;kCAEhC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAQ,SAAQ;;0CAC/B,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAMhD;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAQ,SAAQ;;0CAC/B,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAMhD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,SAAS;;0CAC/B,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAChC,SAAS,aAAa,CAAC,OAAO,CAAC,KAAK;;;;;;0CAEvC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAS,SAAS,MAAM,KAAK,cAAc,YAAY;0CAC3D,SAAS,MAAM;;;;;;;;;;;;;;;;;;0BAMtB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,SAAS,KAAK;;;;;;;8CAEjB,8OAAC,gIAAA,CAAA,kBAAe;oCAAC,WAAU;;sDACzB,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;gDAC3B,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;sDAE5D,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;gDAC5B,SAAS,EAAE,CAAC,KAAK,CAAC,GAAG;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,cAAa;4BAAU,WAAU;;8CACrC,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;8DACjB,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAKzC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,yBAAyB;oDAAE,QAAQ,SAAS,iBAAiB;gDAAC;;;;;;;;;;;sDAGlE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe,SAAS,iBAAiB,EAAE,GAAG,SAAS,KAAK,CAAC,KAAK,CAAC;;kEAElF,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAClC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DAAM,SAAS,iBAAiB;;;;;;;;;;;0DAEnC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,sBAAsB,SAAS,iBAAiB;gDAC/D,UAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,UAAU,YAAY;;;;;;;;;;;;;;;;;;8CAK7B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,SAAS,gBAAgB;;;;;;;;;;;8DAElC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,sBAAsB,SAAS,gBAAgB,IAAI;oDAClE,UAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,UAAU,YAAY;;;;;;;;;;;;;sDAG3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe,SAAS,gBAAgB,IAAI,IAAI,GAAG,SAAS,KAAK,CAAC,GAAG,CAAC;;kEAErF,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;;0CACjC,8OAAC,0MAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGpC,8OAAC,kIAAA,CAAA,SAAM;;0CACL,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 4664, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/hooks/useAuth.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\ninterface User {\n  id: string;\n  email?: string;\n  name?: string;\n  tenantId?: string;\n}\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for demo user ID in localStorage\n    const demoUserId = localStorage.getItem('demoUserId');\n    if (demoUserId) {\n      setUser({\n        id: demoUserId,\n        email: `${demoUserId}@demo.com`,\n        name: 'Demo User',\n        tenantId: '550e8400-e29b-41d4-a716-446655440000' // Default tenant ID\n      });\n    }\n    setIsLoading(false);\n  }, []);\n\n  return {\n    user,\n    isLoading,\n    isAuthenticated: !!user,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AASO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,QAAQ;gBACN,IAAI;gBACJ,OAAO,GAAG,WAAW,SAAS,CAAC;gBAC/B,MAAM;gBACN,UAAU,uCAAuC,oBAAoB;YACvE;QACF;QACA,aAAa;IACf,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;AACF", "debugId": null}}, {"offset": {"line": 4697, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/hooks/useTenant.ts"], "sourcesContent": ["import { useAuth } from './useAuth';\nimport { useState, useEffect } from 'react';\n\ninterface Tenant {\n  id: string;\n  name: string;\n  slug: string;\n}\n\nexport function useTenant() {\n  const { user } = useAuth();\n  const [tenant, setTenant] = useState<Tenant | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    if (user?.tenantId) {\n      // In a real app, you'd fetch tenant data from API\n      // For now, we'll create a mock tenant based on the user's tenantId\n      setTenant({\n        id: user.tenantId,\n        name: 'Demo Organization',\n        slug: 'demo-org'\n      });\n    } else {\n      setTenant(null);\n    }\n    setIsLoading(false);\n  }, [user]);\n\n  return {\n    tenant,\n    isLoading,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,UAAU;YAClB,kDAAkD;YAClD,mEAAmE;YACnE,UAAU;gBACR,IAAI,KAAK,QAAQ;gBACjB,MAAM;gBACN,MAAM;YACR;QACF,OAAO;YACL,UAAU;QACZ;QACA,aAAa;IACf,GAAG;QAAC;KAAK;IAET,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4735, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/PrivacyFirstImplementation/privacy-first-onboarding/src/app/dashboard/document-generator/page.tsx"], "sourcesContent": ["// Document Generator Page - Main page for the document generator system\n'use client';\n\nimport React, { useState } from 'react';\nimport { DocumentGeneratorHub } from '@/components/document-generator/DocumentGeneratorHub';\nimport { DocumentGeneratorWizard } from '@/components/document-generator/DocumentGeneratorWizard';\nimport { DocumentViewer } from '@/components/document-generator/DocumentViewer';\nimport { DocumentGeneratorType } from '@/lib/document-generator-types';\nimport { useTenant } from '@/hooks/useTenant';\nimport { useAuth } from '@/hooks/useAuth';\n\ntype ViewMode = 'hub' | 'wizard' | 'viewer';\n\ninterface DocumentGeneratorPageState {\n  mode: ViewMode;\n  documentType?: DocumentGeneratorType;\n  sessionId?: string;\n  documentId?: string;\n}\n\nexport default function DocumentGeneratorPage() {\n  const { tenant, isLoading: tenantLoading } = useTenant();\n  const { user, isLoading: userLoading } = useAuth();\n\n  const [state, setState] = useState<DocumentGeneratorPageState>({\n    mode: 'hub'\n  });\n\n\n\n  const handleStartGenerator = (documentType: DocumentGeneratorType, sessionId?: string) => {\n    setState({\n      mode: 'wizard',\n      documentType,\n      sessionId\n    });\n  };\n\n  const handleBackToHub = () => {\n    setState({\n      mode: 'hub'\n    });\n  };\n\n  const handleDocumentComplete = (documentId: string) => {\n    setState({\n      mode: 'viewer',\n      documentId\n    });\n  };\n\n  const handleBackToWizard = () => {\n    setState({\n      mode: 'wizard',\n      documentType: state.documentType,\n      sessionId: state.sessionId\n    });\n  };\n\n  // Show loading state\n  if (userLoading || tenantLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center space-y-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Loading...</h2>\n          <p className=\"text-gray-600\">Initializing document generator...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show authentication required\n  if (!user) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center space-y-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Authentication Required</h2>\n          <p className=\"text-gray-600\">Please log in to access the document generator.</p>\n          <p className=\"text-sm text-gray-500\">Go to <a href=\"/dashboard\" className=\"text-blue-600 hover:underline\">/dashboard</a> to log in first.</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!tenant) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center space-y-4\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">No Tenant Selected</h2>\n          <p className=\"text-gray-600\">Please select a tenant to access the document generator.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {state.mode === 'hub' && (\n        <DocumentGeneratorHub\n          tenantId={tenant.id}\n          userId={user?.id}\n          onStartGenerator={handleStartGenerator}\n        />\n      )}\n\n      {state.mode === 'wizard' && state.documentType && (\n        <DocumentGeneratorWizard\n          tenantId={tenant.id}\n          userId={user?.id}\n          documentType={state.documentType}\n          sessionId={state.sessionId}\n          onBack={handleBackToHub}\n          onComplete={handleDocumentComplete}\n        />\n      )}\n\n      {state.mode === 'viewer' && state.documentId && (\n        <DocumentViewer\n          tenantId={tenant.id}\n          documentId={state.documentId}\n          onBack={handleBackToHub}\n          onEdit={handleBackToWizard}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,wEAAwE;;;;;AAGxE;AACA;AACA;AACA;AAEA;AACA;AARA;;;;;;;;AAmBe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACrD,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;QAC7D,MAAM;IACR;IAIA,MAAM,uBAAuB,CAAC,cAAqC;QACjE,SAAS;YACP,MAAM;YACN;YACA;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,SAAS;YACP,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,SAAS;YACP,MAAM;YACN;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,SAAS;YACP,MAAM;YACN,cAAc,MAAM,YAAY;YAChC,WAAW,MAAM,SAAS;QAC5B;IACF;IAEA,qBAAqB;IACrB,IAAI,eAAe,eAAe;QAChC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,+BAA+B;IAC/B,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBAAE,WAAU;;4BAAwB;0CAAM,8OAAC;gCAAE,MAAK;gCAAa,WAAU;0CAAgC;;;;;;4BAAc;;;;;;;;;;;;;;;;;;IAIhI;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,MAAM,IAAI,KAAK,uBACd,8OAAC,mKAAA,CAAA,uBAAoB;gBACnB,UAAU,OAAO,EAAE;gBACnB,QAAQ,MAAM;gBACd,kBAAkB;;;;;;YAIrB,MAAM,IAAI,KAAK,YAAY,MAAM,YAAY,kBAC5C,8OAAC,sKAAA,CAAA,0BAAuB;gBACtB,UAAU,OAAO,EAAE;gBACnB,QAAQ,MAAM;gBACd,cAAc,MAAM,YAAY;gBAChC,WAAW,MAAM,SAAS;gBAC1B,QAAQ;gBACR,YAAY;;;;;;YAIf,MAAM,IAAI,KAAK,YAAY,MAAM,UAAU,kBAC1C,8OAAC,6JAAA,CAAA,iBAAc;gBACb,UAAU,OAAO,EAAE;gBACnB,YAAY,MAAM,UAAU;gBAC5B,QAAQ;gBACR,QAAQ;;;;;;;;;;;;AAKlB", "debugId": null}}]}
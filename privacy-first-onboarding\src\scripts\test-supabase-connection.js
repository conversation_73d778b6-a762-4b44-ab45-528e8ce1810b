#!/usr/bin/env node

/**
 * Test script to verify Supabase database connection and data persistence
 * Run this after setting up your Supabase database
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🧪 Testing Supabase Database Connection...\n');

// Test configuration
async function testConfiguration() {
  console.log('1️⃣ Testing Configuration...');
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase configuration:');
    console.error('   - NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌');
    console.error('   - SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌');
    return false;
  }
  
  console.log('✅ Configuration looks good');
  return true;
}

// Test database connection
async function testConnection() {
  console.log('\n2️⃣ Testing Database Connection...');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Simple query to test connection
    const { data, error } = await supabase
      .from('tenants')
      .select('id, name, slug')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    if (data && data.length > 0) {
      console.log('✅ Found tenant data:', data[0]);
    }
    return true;
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    return false;
  }
}

// Test table structure
async function testTableStructure() {
  console.log('\n3️⃣ Testing Table Structure...');
  
  const requiredTables = [
    'tenants',
    'tenant_privacy_configs',
    'roles',
    'users',
    'user_roles',
    'consents',
    'privacy_requests',
    'data_processing_activities',
    'compliance_status',
    'audit_logs'
  ];
  
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    for (const table of requiredTables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`❌ Table '${table}' not accessible:`, error.message);
        return false;
      }
      
      console.log(`✅ Table '${table}' exists and accessible`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Table structure test failed:', error.message);
    return false;
  }
}

// Test data persistence
async function testDataPersistence() {
  console.log('\n4️⃣ Testing Data Persistence...');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test creating a user
    const { v4: uuidv4 } = require('uuid');
    const testUserId = uuidv4();
    const testUser = {
      id: testUserId,
      tenant_id: '550e8400-e29b-41d4-a716-************', // Default tenant
      email: `test-${Date.now()}@example.com`,
      first_name: 'Test',
      last_name: 'User',
      preferred_region: 'US',
      data_residency_preference: 'SAME_REGION',
      created_at: new Date().toISOString()
    };
    
    // Insert test user
    const { data: insertData, error: insertError } = await supabase
      .from('users')
      .insert(testUser)
      .select();
    
    if (insertError) {
      console.error('❌ Failed to insert test user:', insertError.message);
      return false;
    }
    
    console.log('✅ Successfully inserted test user');
    
    // Read test user
    const { data: readData, error: readError } = await supabase
      .from('users')
      .select('*')
      .eq('id', testUserId)
      .single();
    
    if (readError) {
      console.error('❌ Failed to read test user:', readError.message);
      return false;
    }
    
    console.log('✅ Successfully read test user');
    
    // Update test user
    const { error: updateError } = await supabase
      .from('users')
      .update({ last_name: 'Updated' })
      .eq('id', testUserId);
    
    if (updateError) {
      console.error('❌ Failed to update test user:', updateError.message);
      return false;
    }
    
    console.log('✅ Successfully updated test user');
    
    // Delete test user
    const { error: deleteError } = await supabase
      .from('users')
      .delete()
      .eq('id', testUserId);
    
    if (deleteError) {
      console.error('❌ Failed to delete test user:', deleteError.message);
      return false;
    }
    
    console.log('✅ Successfully deleted test user');
    console.log('✅ Data persistence test completed successfully');
    
    return true;
  } catch (error) {
    console.error('❌ Data persistence test failed:', error.message);
    return false;
  }
}

// Test privacy service integration
async function testPrivacyService() {
  console.log('\n5️⃣ Testing Privacy Service Integration...');
  
  try {
    // This would test the actual privacy service
    // For now, just check if we can access the required tables
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test consent management
    const { data: consents, error: consentError } = await supabase
      .from('consents')
      .select('*')
      .limit(1);
    
    if (consentError) {
      console.error('❌ Consent table not accessible:', consentError.message);
      return false;
    }
    
    console.log('✅ Consent management table accessible');
    
    // Test privacy requests
    const { data: requests, error: requestError } = await supabase
      .from('privacy_requests')
      .select('*')
      .limit(1);
    
    if (requestError) {
      console.error('❌ Privacy requests table not accessible:', requestError.message);
      return false;
    }
    
    console.log('✅ Privacy requests table accessible');
    console.log('✅ Privacy service integration test completed');
    
    return true;
  } catch (error) {
    console.error('❌ Privacy service test failed:', error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Supabase Database Tests\n');
  
  const tests = [
    { name: 'Configuration', fn: testConfiguration },
    { name: 'Connection', fn: testConnection },
    { name: 'Table Structure', fn: testTableStructure },
    { name: 'Data Persistence', fn: testDataPersistence },
    { name: 'Privacy Service', fn: testPrivacyService }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
    } catch (error) {
      console.error(`❌ Test '${test.name}' failed with error:`, error.message);
    }
  }
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passedTests}/${tests.length}`);
  console.log(`❌ Failed: ${tests.length - passedTests}/${tests.length}`);
  
  if (passedTests === tests.length) {
    console.log('\n🎉 All tests passed! Your Supabase database is ready to use.');
    console.log('\n📝 Next steps:');
    console.log('1. Run: node src/scripts/switch-to-supabase.js');
    console.log('2. Restart your development server: npm run dev');
    console.log('3. Test the application in your browser');
  } else {
    console.log('\n⚠️  Some tests failed. Please check your database setup.');
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Verify your .env.local configuration');
    console.log('2. Check if database migrations were run successfully');
    console.log('3. Ensure your Supabase project is active');
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };

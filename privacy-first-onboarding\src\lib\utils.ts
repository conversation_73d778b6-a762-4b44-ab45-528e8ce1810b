import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Privacy Framework Utilities
export const PRIVACY_REGIONS = {
  US: { code: 'US', name: 'United States', laws: ['CCPA', 'CPRA'] },
  EU: { code: 'EU', name: 'European Union', laws: ['GDPR'] },
  UK: { code: 'UK', name: 'United Kingdom', laws: ['UK GDPR', 'DPA 2018'] },
  CA: { code: 'CA', name: 'Canada', laws: ['PIPEDA', 'CPPA'] },
  IN: { code: 'IN', name: 'India', laws: ['DPDP'] },
  SG: { code: 'SG', name: 'Singapore', laws: ['PDPA'] },
  JP: { code: 'JP', name: 'Japan', laws: ['APPI'] },
  AU: { code: 'AU', name: 'Australia', laws: ['Privacy Act'] },
  BR: { code: 'BR', name: 'Brazil', laws: ['LGPD'] },
  CN: { code: 'CN', name: 'China', laws: ['PIPL'] },
} as const;

export type PrivacyRegion = keyof typeof PRIVACY_REGIONS;

export const DATA_CATEGORIES = {
  PERSONAL_IDENTIFIERS: 'Personal Identifiers',
  CONTACT_INFO: 'Contact Information',
  FINANCIAL_INFO: 'Financial Information',
  BIOMETRIC_DATA: 'Biometric Data',
  LOCATION_DATA: 'Location Data',
  BEHAVIORAL_DATA: 'Behavioral Data',
  SENSITIVE_PERSONAL: 'Sensitive Personal Data',
  HEALTH_DATA: 'Health Data',
  EMPLOYMENT_DATA: 'Employment Data',
  EDUCATION_DATA: 'Education Data',
} as const;

export type DataCategory = keyof typeof DATA_CATEGORIES;

export const USER_RIGHTS = {
  ACCESS: 'Right to Access',
  CORRECTION: 'Right to Correction',
  DELETION: 'Right to Deletion',
  PORTABILITY: 'Right to Data Portability',
  OPT_OUT: 'Right to Opt-Out',
  RESTRICT_PROCESSING: 'Right to Restrict Processing',
  OBJECT: 'Right to Object',
  NON_DISCRIMINATION: 'Right to Non-Discrimination',
} as const;

export type UserRight = keyof typeof USER_RIGHTS;

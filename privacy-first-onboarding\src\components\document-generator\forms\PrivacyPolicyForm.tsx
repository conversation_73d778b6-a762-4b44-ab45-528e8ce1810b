// Privacy Policy Form Component
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface PrivacyPolicyFormProps {
  step: string;
  formData: Record<string, any>;
  onUpdate: (field: string, value: any) => void;
}

export function PrivacyPolicyForm({ step, formData, onUpdate }: PrivacyPolicyFormProps) {
  const renderCompanyInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="company_name">Company Name *</Label>
          <Input
            id="company_name"
            value={formData.company_name || ''}
            onChange={(e) => onUpdate('company_name', e.target.value)}
            placeholder="Enter your company name"
            required
          />
        </div>
        <div>
          <Label htmlFor="website_url">Website URL *</Label>
          <Input
            id="website_url"
            type="url"
            value={formData.website_url || ''}
            onChange={(e) => onUpdate('website_url', e.target.value)}
            placeholder="https://example.com"
            required
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="contact_email">Contact Email *</Label>
          <Input
            id="contact_email"
            type="email"
            value={formData.contact_email || ''}
            onChange={(e) => onUpdate('contact_email', e.target.value)}
            placeholder="<EMAIL>"
            required
          />
        </div>
        <div>
          <Label htmlFor="contact_phone">Contact Phone</Label>
          <Input
            id="contact_phone"
            type="tel"
            value={formData.contact_phone || ''}
            onChange={(e) => onUpdate('contact_phone', e.target.value)}
            placeholder="+****************"
          />
        </div>
      </div>
      
      <div>
        <Label htmlFor="contact_address">Business Address *</Label>
        <Textarea
          id="contact_address"
          value={formData.contact_address || ''}
          onChange={(e) => onUpdate('contact_address', e.target.value)}
          placeholder="Enter your complete business address"
          rows={3}
          required
        />
      </div>
    </div>
  );

  const renderDataCollection = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">What types of personal information do you collect?</Label>
        <div className="mt-3 space-y-3">
          {[
            { id: 'personal_info', label: 'Personal Information (name, email, phone)' },
            { id: 'payment_info', label: 'Payment Information' },
            { id: 'usage_data', label: 'Usage Data and Analytics' },
            { id: 'device_info', label: 'Device Information' },
            { id: 'location_data', label: 'Location Data' },
            { id: 'cookies', label: 'Cookies and Tracking Technologies' }
          ].map((item) => (
            <div key={item.id} className="flex items-center space-x-2">
              <Checkbox
                id={item.id}
                checked={formData.data_types?.includes(item.id) || false}
                onCheckedChange={(checked) => {
                  const currentTypes = formData.data_types || [];
                  const newTypes = checked 
                    ? [...currentTypes, item.id]
                    : currentTypes.filter((type: string) => type !== item.id);
                  onUpdate('data_types', newTypes);
                }}
              />
              <Label htmlFor={item.id} className="text-sm font-normal">
                {item.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <Label htmlFor="data_purpose">How do you use the collected data?</Label>
        <Textarea
          id="data_purpose"
          value={formData.data_purpose || ''}
          onChange={(e) => onUpdate('data_purpose', e.target.value)}
          placeholder="Describe how you use the personal information you collect..."
          rows={4}
        />
      </div>
    </div>
  );

  const renderDataSharing = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Do you share data with third parties?</Label>
        <Select
          value={formData.data_sharing || ''}
          onValueChange={(value) => onUpdate('data_sharing', value)}
        >
          <SelectTrigger className="mt-2">
            <SelectValue placeholder="Select an option" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="no">No, we don't share data</SelectItem>
            <SelectItem value="service_providers">Yes, with service providers</SelectItem>
            <SelectItem value="business_partners">Yes, with business partners</SelectItem>
            <SelectItem value="legal_requirements">Only when legally required</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {formData.data_sharing && formData.data_sharing !== 'no' && (
        <div>
          <Label htmlFor="sharing_details">Please provide details about data sharing</Label>
          <Textarea
            id="sharing_details"
            value={formData.sharing_details || ''}
            onChange={(e) => onUpdate('sharing_details', e.target.value)}
            placeholder="Describe with whom and why you share data..."
            rows={4}
          />
        </div>
      )}

      <div>
        <Label className="text-base font-medium">Data retention period</Label>
        <Select
          value={formData.retention_period || ''}
          onValueChange={(value) => onUpdate('retention_period', value)}
        >
          <SelectTrigger className="mt-2">
            <SelectValue placeholder="How long do you keep user data?" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1_year">1 Year</SelectItem>
            <SelectItem value="2_years">2 Years</SelectItem>
            <SelectItem value="5_years">5 Years</SelectItem>
            <SelectItem value="indefinite">Indefinitely</SelectItem>
            <SelectItem value="custom">Custom Period</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );

  const renderUserRights = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium">Which user rights do you provide?</Label>
        <div className="mt-3 space-y-3">
          {[
            { id: 'access_right', label: 'Right to access personal data' },
            { id: 'rectification_right', label: 'Right to rectify inaccurate data' },
            { id: 'erasure_right', label: 'Right to erasure (right to be forgotten)' },
            { id: 'portability_right', label: 'Right to data portability' },
            { id: 'objection_right', label: 'Right to object to processing' },
            { id: 'restriction_right', label: 'Right to restrict processing' }
          ].map((item) => (
            <div key={item.id} className="flex items-center space-x-2">
              <Checkbox
                id={item.id}
                checked={formData.user_rights?.includes(item.id) || false}
                onCheckedChange={(checked) => {
                  const currentRights = formData.user_rights || [];
                  const newRights = checked 
                    ? [...currentRights, item.id]
                    : currentRights.filter((right: string) => right !== item.id);
                  onUpdate('user_rights', newRights);
                }}
              />
              <Label htmlFor={item.id} className="text-sm font-normal">
                {item.label}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <Label htmlFor="contact_method">How can users contact you about their data?</Label>
        <Textarea
          id="contact_method"
          value={formData.contact_method || ''}
          onChange={(e) => onUpdate('contact_method', e.target.value)}
          placeholder="Provide contact information for data protection inquiries..."
          rows={3}
        />
      </div>
    </div>
  );

  switch (step) {
    case 'company_info':
      return renderCompanyInfo();
    case 'data_collection':
      return renderDataCollection();
    case 'data_sharing':
      return renderDataSharing();
    case 'user_rights':
      return renderUserRights();
    default:
      return <div>Unknown step</div>;
  }
}

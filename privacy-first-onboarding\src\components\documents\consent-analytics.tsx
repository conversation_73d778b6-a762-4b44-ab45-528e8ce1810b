'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
// Temporarily disabled recharts import to fix compilation
// import {
//   Bar<PERSON><PERSON>,
//   Bar,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   ResponsiveContainer,
//   Pie<PERSON>hart,
//   Pie,
//   Cell,
//   LineChart,
//   Line
// } from 'recharts';
import {
  Users,
  FileText,
  CheckCircle,
  XCircle,
  TrendingUp,
  Calendar,
  Download,
  Mail,
  PieChart,
  BarChart3
} from 'lucide-react';
import { DocumentAnalytics } from '@/lib/document-types';

interface ConsentAnalyticsProps {
  tenantId: string;
}

// Mock data - in real implementation, fetch from API
const mockAnalytics = {
  overview: {
    totalDocuments: 3,
    totalVersions: 8,
    totalConsents: 156,
    consentRate: 87.5,
    activeUsers: 142
  },
  documentStats: [
    {
      template_id: '1',
      template_name: 'Privacy Policy',
      template_type: 'privacy_policy',
      total_versions: 3,
      current_version: '1.2.0',
      total_consents: 89,
      consent_rate: 92.3
    },
    {
      template_id: '2',
      template_name: 'Terms of Service',
      template_type: 'terms_of_service',
      total_versions: 3,
      current_version: '1.1.0',
      total_consents: 45,
      consent_rate: 78.9
    },
    {
      template_id: '3',
      template_name: 'Cookie Policy',
      template_type: 'cookie_policy',
      total_versions: 2,
      current_version: '1.0.1',
      total_consents: 22,
      consent_rate: 95.7
    }
  ],
  consentTrends: [
    { date: '2024-01-01', consents: 12, notifications: 15 },
    { date: '2024-01-02', consents: 18, notifications: 22 },
    { date: '2024-01-03', consents: 25, notifications: 28 },
    { date: '2024-01-04', consents: 31, notifications: 35 },
    { date: '2024-01-05', consents: 28, notifications: 32 },
    { date: '2024-01-06', consents: 35, notifications: 38 },
    { date: '2024-01-07', consents: 42, notifications: 45 }
  ],
  consentByType: [
    { name: 'Privacy Policy', value: 89, color: '#3b82f6' },
    { name: 'Terms of Service', value: 45, color: '#10b981' },
    { name: 'Cookie Policy', value: 22, color: '#f59e0b' }
  ]
};

export function ConsentAnalytics({ tenantId }: ConsentAnalyticsProps) {
  const [analytics, setAnalytics] = useState(mockAnalytics);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    loadAnalytics();
  }, [tenantId, timeRange]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      // TODO: Fetch real analytics data from API
      // const response = await fetch(`/api/analytics?tenantId=${tenantId}&range=${timeRange}`);
      // const data = await response.json();
      // setAnalytics(data);
      
      // For now, use mock data
      setTimeout(() => {
        setAnalytics(mockAnalytics);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading analytics:', error);
      setLoading(false);
    }
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Documents</p>
                <p className="text-2xl font-bold">{analytics.overview.totalDocuments}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Versions</p>
                <p className="text-2xl font-bold">{analytics.overview.totalVersions}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Consents</p>
                <p className="text-2xl font-bold">{analytics.overview.totalConsents}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Consent Rate</p>
                <p className="text-2xl font-bold">{formatPercentage(analytics.overview.consentRate)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold">{analytics.overview.activeUsers}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="documents" className="space-y-4">
        <TabsList>
          <TabsTrigger value="documents">Document Performance</TabsTrigger>
          <TabsTrigger value="trends">Consent Trends</TabsTrigger>
          <TabsTrigger value="distribution">Distribution</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Document Performance</CardTitle>
              <CardDescription>
                Consent rates and engagement for each document type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.documentStats.map((doc) => (
                  <div key={doc.template_id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{doc.template_name}</h4>
                        <Badge variant="outline">v{doc.current_version}</Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{doc.total_versions} versions</span>
                        <span>{doc.total_consents} consents</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">
                        {formatPercentage(doc.consent_rate)}
                      </div>
                      <div className="text-sm text-muted-foreground">consent rate</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Consent Trends</CardTitle>
              <CardDescription>
                Daily consent and notification activity over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <TrendingUp className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                  <p className="text-gray-500">Consent Trends Chart</p>
                  <p className="text-xs text-gray-400">Charts will be available once recharts is properly loaded</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Consent Distribution</CardTitle>
                <CardDescription>
                  Breakdown of consents by document type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <PieChart className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                    <p className="text-gray-500">Consent Distribution Chart</p>
                    <p className="text-xs text-gray-400">Charts will be available once recharts is properly loaded</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Document Versions</CardTitle>
                <CardDescription>
                  Number of versions per document type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                    <p className="text-gray-500">Document Versions Chart</p>
                    <p className="text-xs text-gray-400">Charts will be available once recharts is properly loaded</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

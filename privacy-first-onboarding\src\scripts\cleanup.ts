#!/usr/bin/env tsx

import { cleanupExpiredData, prisma } from '../lib/database';

async function main() {
  console.log('🧹 Starting data retention cleanup...');
  
  try {
    await cleanupExpiredData();
    console.log('✅ Data retention cleanup completed successfully');
  } catch (error) {
    console.error('❌ Data retention cleanup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
